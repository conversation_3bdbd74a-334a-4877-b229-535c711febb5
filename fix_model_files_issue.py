#!/usr/bin/env python3
"""
修复模型文件加载问题
基于测试结果，问题是 group49-55 文件在ESP32文件系统中不存在
"""

import os
import subprocess
import json

def check_file_sizes():
    """检查文件大小，确定是否有问题"""
    print("🔍 检查模型文件大小...")
    
    model_dir = "data/models/mobilenet"
    problem_files = ['group49-shard1of1', 'group50-shard1of1', 'group51-shard1of1', 
                    'group52-shard1of1', 'group53-shard1of1', 'group54-shard1of1', 'group55-shard1of1']
    
    print("📋 问题文件大小检查:")
    for file_name in problem_files:
        file_path = os.path.join(model_dir, file_name)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"  ✅ {file_name}: {file_size} bytes")
        else:
            print(f"  ❌ {file_name}: 文件不存在")
    
    # 检查所有文件的大小分布
    print(f"\n📊 所有模型文件大小分布:")
    all_files = []
    for i in range(1, 56):
        file_name = f"group{i}-shard1of1"
        file_path = os.path.join(model_dir, file_name)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            all_files.append((file_name, file_size))
    
    # 按大小排序
    all_files.sort(key=lambda x: x[1])
    
    print(f"  最小文件: {all_files[0][0]} ({all_files[0][1]} bytes)")
    print(f"  最大文件: {all_files[-1][0]} ({all_files[-1][1]} bytes)")
    
    # 检查异常小的文件
    small_files = [f for f in all_files if f[1] < 100]
    if small_files:
        print(f"  ⚠️  异常小的文件 (<100 bytes):")
        for file_name, file_size in small_files:
            print(f"    - {file_name}: {file_size} bytes")

def check_filesystem_space():
    """检查文件系统空间使用情况"""
    print("\n💾 检查文件系统空间...")
    
    try:
        # 计算 data 目录总大小
        total_size = 0
        for root, dirs, files in os.walk("data"):
            for file in files:
                file_path = os.path.join(root, file)
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
        
        print(f"  📁 data 目录总大小: {total_size / 1024 / 1024:.2f} MB")
        
        # 计算模型文件大小
        model_size = 0
        model_dir = "data/models/mobilenet"
        if os.path.exists(model_dir):
            for file in os.listdir(model_dir):
                file_path = os.path.join(model_dir, file)
                if os.path.isfile(file_path):
                    model_size += os.path.getsize(file_path)
        
        print(f"  🤖 模型文件总大小: {model_size / 1024 / 1024:.2f} MB")
        
        # ESP32-S3 文件系统限制通常是 4MB 或更少
        if total_size > 4 * 1024 * 1024:
            print(f"  ⚠️  文件系统可能超出ESP32限制 (>4MB)")
        else:
            print(f"  ✅ 文件系统大小在合理范围内")
            
    except Exception as e:
        print(f"  ❌ 无法计算文件系统大小: {e}")

def generate_solutions():
    """生成解决方案"""
    print("\n🛠️ 解决方案:")
    
    print("\n1️⃣ 重新上传文件系统 (推荐)")
    print("   原因: 文件系统上传可能不完整")
    print("   操作:")
    print("   pio run -e seeed_xiao_esp32s3_sense -t uploadfs")
    print("   注意: 确保USB连接稳定，上传过程不要中断")
    
    print("\n2️⃣ 检查文件系统分区大小")
    print("   原因: 文件系统分区可能太小")
    print("   操作:")
    print("   - 检查 platformio.ini 中的 board_build.filesystem_size")
    print("   - 考虑增加到 4MB 或更大")
    print("   - 重新编译和上传")
    
    print("\n3️⃣ 分批上传文件")
    print("   原因: 一次性上传太多文件可能失败")
    print("   操作:")
    print("   - 先上传 group1-48 文件")
    print("   - 测试加载是否正常")
    print("   - 再上传 group49-55 文件")
    
    print("\n4️⃣ 使用压缩文件")
    print("   原因: 减少文件系统占用")
    print("   操作:")
    print("   - 压缩模型文件")
    print("   - 修改服务器支持压缩文件")
    print("   - 减少总文件大小")
    
    print("\n5️⃣ 检查硬件问题")
    print("   原因: Flash存储可能有问题")
    print("   操作:")
    print("   - 尝试不同的USB线缆")
    print("   - 检查ESP32-S3的Flash健康状态")
    print("   - 尝试降低上传波特率")

def create_test_script():
    """创建测试脚本"""
    print("\n📝 创建文件系统测试脚本...")
    
    test_script = """#!/usr/bin/env python3
# 文件系统上传测试脚本

import subprocess
import time

def upload_filesystem():
    print("🚀 开始上传文件系统...")
    try:
        result = subprocess.run([
            "pio", "run", "-e", "seeed_xiao_esp32s3_sense", "-t", "uploadfs"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 文件系统上传成功")
            print(result.stdout)
        else:
            print("❌ 文件系统上传失败")
            print(result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ 文件系统上传超时")
        return False
    except Exception as e:
        print(f"💥 上传过程出错: {e}")
        return False

if __name__ == "__main__":
    success = upload_filesystem()
    if success:
        print("\\n🎉 请重新测试模型文件加载")
    else:
        print("\\n⚠️ 请检查错误信息并重试")
"""
    
    with open("upload_filesystem.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("  ✅ 已创建 upload_filesystem.py")

def check_platformio_config():
    """检查 PlatformIO 配置"""
    print("\n⚙️ 检查 PlatformIO 配置...")
    
    if os.path.exists("platformio.ini"):
        with open("platformio.ini", "r", encoding="utf-8") as f:
            content = f.read()
        
        print("📋 相关配置检查:")
        
        # 检查文件系统大小
        if "board_build.filesystem_size" in content:
            print("  ✅ 找到 filesystem_size 配置")
        else:
            print("  ⚠️  未找到 filesystem_size 配置")
            print("     建议添加: board_build.filesystem_size = 4MB")
        
        # 检查分区表
        if "board_build.partitions" in content:
            print("  ✅ 找到自定义分区表配置")
        else:
            print("  ℹ️  使用默认分区表")
        
        # 检查上传速度
        if "upload_speed" in content:
            print("  ✅ 找到上传速度配置")
        else:
            print("  ℹ️  使用默认上传速度")
            print("     如果上传不稳定，可以添加: upload_speed = 115200")
    else:
        print("  ❌ 未找到 platformio.ini 文件")

def main():
    """主函数"""
    print("🚀 模型文件问题修复工具")
    print("=" * 50)
    
    print("📊 问题分析:")
    print("  - model.json 需要 55 个文件 (group1-55)")
    print("  - 本地文件系统中所有文件都存在")
    print("  - 测试显示 group49-55 返回 404 错误")
    print("  - 结论: ESP32 文件系统中缺少这些文件")
    
    # 检查文件大小
    check_file_sizes()
    
    # 检查文件系统空间
    check_filesystem_space()
    
    # 检查配置
    check_platformio_config()
    
    # 生成解决方案
    generate_solutions()
    
    # 创建测试脚本
    create_test_script()
    
    print(f"\n{'='*50}")
    print("🎯 推荐操作步骤:")
    print("1. 运行: python upload_filesystem.py")
    print("2. 等待文件系统上传完成")
    print("3. 重新测试: http://[设备IP]/test-model-loading.html")
    print("4. 如果仍有问题，检查 platformio.ini 配置")

if __name__ == "__main__":
    main()
