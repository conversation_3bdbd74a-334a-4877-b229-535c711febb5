#!/usr/bin/env python3
"""
诊断 MobileNet 模型文件加载问题
分析为什么最后几个权重文件加载失败
"""

import os
import json

def check_model_files():
    """检查模型文件完整性"""
    print("🔍 检查 MobileNet 模型文件完整性...")
    
    model_dir = "data/models/mobilenet"
    if not os.path.exists(model_dir):
        print(f"❌ 模型目录不存在: {model_dir}")
        return False
    
    # 检查 model.json
    model_json_path = os.path.join(model_dir, "model.json")
    if not os.path.exists(model_json_path):
        print("❌ model.json 文件不存在")
        return False
    
    # 读取 model.json 获取预期的分片数量
    try:
        with open(model_json_path, 'r') as f:
            model_config = json.load(f)
        
        # 查找权重清单
        weights_manifest = model_config.get('weightsManifest', [])
        expected_files = []
        
        for manifest in weights_manifest:
            paths = manifest.get('paths', [])
            expected_files.extend(paths)
        
        print(f"📋 model.json 中期望的文件数量: {len(expected_files)}")
        print("期望的文件列表:")
        for i, file_path in enumerate(expected_files):
            print(f"  {i+1:2d}. {file_path}")
            
    except Exception as e:
        print(f"❌ 读取 model.json 失败: {e}")
        return False
    
    # 检查实际文件
    print(f"\n📁 检查实际文件存在情况:")
    missing_files = []
    existing_files = []
    
    for expected_file in expected_files:
        file_path = os.path.join(model_dir, expected_file)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            existing_files.append((expected_file, file_size))
            print(f"  ✅ {expected_file} ({file_size} bytes)")
        else:
            missing_files.append(expected_file)
            print(f"  ❌ {expected_file} - 缺失")
    
    print(f"\n📊 统计:")
    print(f"  期望文件: {len(expected_files)}")
    print(f"  存在文件: {len(existing_files)}")
    print(f"  缺失文件: {len(missing_files)}")
    
    if missing_files:
        print(f"\n❌ 缺失的文件:")
        for file in missing_files:
            print(f"    - {file}")
        return False
    
    # 检查文件大小异常
    print(f"\n🔍 检查文件大小异常:")
    size_issues = []
    
    for file_name, file_size in existing_files:
        if file_size == 0:
            size_issues.append(f"{file_name} - 文件为空")
        elif file_size < 1000:  # 小于1KB可能有问题
            size_issues.append(f"{file_name} - 文件过小 ({file_size} bytes)")
    
    if size_issues:
        print("⚠️  发现文件大小异常:")
        for issue in size_issues:
            print(f"    - {issue}")
    else:
        print("✅ 所有文件大小正常")
    
    return len(missing_files) == 0 and len(size_issues) == 0

def analyze_server_config():
    """分析服务器配置"""
    print("\n🔍 分析服务器配置...")
    
    if not os.path.exists("src/camera_server.cpp"):
        print("❌ 未找到 src/camera_server.cpp")
        return False
    
    with open("src/camera_server.cpp", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查路由配置
    checks = {
        "模型分片路由数组": "httpd_uri_t model_shard_uris[55]" in content,
        "URI路径数组": "char uri_paths[55][64]" in content,
        "路由创建循环": "for (int i = 1; i <= 55; i++)" in content,
        "路由注册循环": "for (int i = 0; i < 55; i++)" in content,
        "分片路径格式": '"/models/mobilenet/group%d-shard1of1"' in content
    }
    
    print("📋 服务器配置检查:")
    all_good = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def suggest_solutions():
    """提供解决方案建议"""
    print("\n💡 可能的原因和解决方案:")
    
    print("\n1️⃣ 文件系统上传不完整")
    print("   原因: 文件系统上传过程中最后几个文件失败")
    print("   解决方案:")
    print("   - 重新上传文件系统: pio run -e seeed_xiao_esp32s3_sense -t uploadfs")
    print("   - 检查上传过程中的错误信息")
    print("   - 确保USB连接稳定")
    
    print("\n2️⃣ ESP32 内存不足")
    print("   原因: 同时处理太多HTTP请求导致内存耗尽")
    print("   解决方案:")
    print("   - 减少并发请求数量 (createLimitedFetch 参数)")
    print("   - 增加请求间隔")
    print("   - 检查内存使用情况")
    
    print("\n3️⃣ HTTP服务器连接限制")
    print("   原因: ESP32 HTTP服务器同时连接数限制")
    print("   解决方案:")
    print("   - 修改 httpd_config_t 中的 max_open_sockets")
    print("   - 增加 server_port 配置")
    print("   - 优化请求处理逻辑")
    
    print("\n4️⃣ 文件路径或命名问题")
    print("   原因: 文件名不匹配或路径错误")
    print("   解决方案:")
    print("   - 检查文件命名是否严格匹配")
    print("   - 验证路由路径配置")
    print("   - 检查大小写敏感性")
    
    print("\n5️⃣ 网络超时问题")
    print("   原因: 请求超时或网络不稳定")
    print("   解决方案:")
    print("   - 增加请求超时时间")
    print("   - 减少并发请求数量")
    print("   - 添加重试机制")

def generate_fix_script():
    """生成修复脚本"""
    print("\n🛠️ 生成修复建议:")
    
    print("\n📝 修改 collecttrain.js 中的并发控制:")
    print("```javascript")
    print("// 减少并发请求数量，从3改为1")
    print("loadedModel = await tf.loadLayersModel(modelPath, { fetchFunc: createLimitedFetch(1) });")
    print("```")
    
    print("\n📝 添加请求延迟:")
    print("```javascript")
    print("function createLimitedFetch(maxConcurrent = 1, delay = 100) {")
    print("    // ... 现有代码 ...")
    print("    const executeRequest = () => {")
    print("        setTimeout(() => {  // 添加延迟")
    print("            activeRequests++;")
    print("            fetch(url, options)")
    print("            // ... 其余代码")
    print("        }, delay);")
    print("    };")
    print("}")
    print("```")
    
    print("\n📝 修改服务器配置 (src/camera_server.cpp):")
    print("```cpp")
    print("// 增加最大连接数")
    print("config.max_open_sockets = 10;  // 默认是7")
    print("config.lru_purge_enable = true;")
    print("```")

def main():
    """主诊断函数"""
    print("🚀 MobileNet 模型加载问题诊断")
    print("=" * 50)
    
    # 检查文件完整性
    files_ok = check_model_files()
    
    # 分析服务器配置
    server_ok = analyze_server_config()
    
    # 提供解决方案
    suggest_solutions()
    
    # 生成修复脚本
    generate_fix_script()
    
    print(f"\n{'='*50}")
    print("诊断总结:")
    print(f"  模型文件完整性: {'✅ 正常' if files_ok else '❌ 有问题'}")
    print(f"  服务器配置: {'✅ 正常' if server_ok else '❌ 有问题'}")
    
    if files_ok and server_ok:
        print("\n🎯 推荐解决方案:")
        print("1. 减少并发请求数量 (createLimitedFetch(1))")
        print("2. 重新上传文件系统")
        print("3. 增加服务器最大连接数")
        print("4. 添加请求重试机制")
    else:
        print("\n⚠️  请先解决文件或配置问题")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"💥 诊断过程出错: {e}")
        exit(1)
