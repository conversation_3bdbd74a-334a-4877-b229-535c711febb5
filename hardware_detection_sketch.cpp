/*
 * ESP32-S3-EYE 硬件信息检测代码
 * 
 * 使用方法:
 * 1. 将此代码替换到 src/main.cpp
 * 2. 运行: pio run -e esp32s3_eye -t upload
 * 3. 运行: pio device monitor -e esp32s3_eye
 * 4. 查看详细的硬件信息输出
 */

#include <Arduino.h>
#include <WiFi.h>
#include "esp_partition.h"
#include "esp_ota_ops.h"
#include "esp_system.h"
#include "esp_chip_info.h"

void printSeparator(const char* title) {
  Serial.println("\n" + String("=").substring(0, 60));
  Serial.printf("🔍 %s\n", title);
  Serial.println(String("=").substring(0, 60));
}

void printChipInfo() {
  printSeparator("芯片基本信息");
  
  esp_chip_info_t chip_info;
  esp_chip_info(&chip_info);
  
  Serial.printf("📱 芯片型号: %s\n", ESP.getChipModel());
  Serial.printf("📱 芯片版本: %d\n", ESP.getChipRevision());
  Serial.printf("⚡ CPU 频率: %d MHz\n", ESP.getCpuFreqMHz());
  Serial.printf("🔢 CPU 核心数: %d\n", ESP.getChipCores());
  
  Serial.printf("🏭 芯片特性: ");
  if (chip_info.features & CHIP_FEATURE_WIFI_BGN) {
    Serial.print("WiFi ");
  }
  if (chip_info.features & CHIP_FEATURE_BT) {
    Serial.print("Bluetooth ");
  }
  if (chip_info.features & CHIP_FEATURE_BLE) {
    Serial.print("BLE ");
  }
  Serial.println();
  
  // MAC 地址
  uint8_t mac[6];
  esp_read_mac(mac, ESP_MAC_WIFI_STA);
  Serial.printf("🌐 WiFi MAC: %02X:%02X:%02X:%02X:%02X:%02X\n",
                mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
}

void printFlashInfo() {
  printSeparator("Flash 存储信息");
  
  uint32_t flash_size = ESP.getFlashChipSize();
  uint32_t flash_speed = ESP.getFlashChipSpeed();
  FlashMode_t flash_mode = ESP.getFlashChipMode();
  
  Serial.printf("💾 Flash 大小: %d bytes (%.1f MB)\n", 
                flash_size, flash_size / 1024.0 / 1024.0);
  Serial.printf("⚡ Flash 速度: %d MHz\n", flash_speed / 1000000);
  Serial.printf("🔧 Flash 模式: ");
  
  switch (flash_mode) {
    case FM_QIO:  Serial.println("QIO"); break;
    case FM_QOUT: Serial.println("QOUT"); break;
    case FM_DIO:  Serial.println("DIO"); break;
    case FM_DOUT: Serial.println("DOUT"); break;
    default:      Serial.println("Unknown"); break;
  }
  
  // 计算实际可用的 Flash 空间
  size_t sketch_size = ESP.getSketchSize();
  size_t free_sketch_space = ESP.getFreeSketchSpace();
  
  Serial.printf("📝 程序大小: %d KB\n", sketch_size / 1024);
  Serial.printf("📝 程序可用空间: %d KB\n", free_sketch_space / 1024);
}

void printPSRAMInfo() {
  printSeparator("PSRAM 信息");
  
  if (psramFound()) {
    Serial.println("🧠 PSRAM: ✅ 检测到");
    
    uint32_t psram_size = ESP.getPsramSize();
    uint32_t free_psram = ESP.getFreePsram();
    
    Serial.printf("📏 PSRAM 总大小: %d bytes (%.1f MB)\n", 
                  psram_size, psram_size / 1024.0 / 1024.0);
    Serial.printf("💚 PSRAM 可用: %d bytes (%.1f MB)\n", 
                  free_psram, free_psram / 1024.0 / 1024.0);
    Serial.printf("📊 PSRAM 使用率: %.1f%%\n", 
                  (psram_size - free_psram) * 100.0 / psram_size);
  } else {
    Serial.println("🧠 PSRAM: ❌ 未检测到");
    Serial.println("⚠️  可能的原因:");
    Serial.println("   1. 硬件不支持 PSRAM");
    Serial.println("   2. PSRAM 配置错误");
    Serial.println("   3. 固件编译时未启用 PSRAM");
  }
}

void printMemoryInfo() {
  printSeparator("内存使用信息");
  
  uint32_t heap_size = ESP.getHeapSize();
  uint32_t free_heap = ESP.getFreeHeap();
  uint32_t max_alloc = ESP.getMaxAllocHeap();
  uint32_t min_free = ESP.getMinFreeHeap();
  
  Serial.printf("🔋 总内存: %d KB\n", heap_size / 1024);
  Serial.printf("💚 可用内存: %d KB\n", free_heap / 1024);
  Serial.printf("📦 最大分配: %d KB\n", max_alloc / 1024);
  Serial.printf("📉 历史最低可用: %d KB\n", min_free / 1024);
  Serial.printf("📊 内存使用率: %.1f%%\n", 
                (heap_size - free_heap) * 100.0 / heap_size);
}

void printPartitionInfo() {
  printSeparator("分区表信息");
  
  // 当前运行的分区
  const esp_partition_t* running = esp_ota_get_running_partition();
  if (running) {
    Serial.printf("🏃 运行分区: %s\n", running->label);
    Serial.printf("📍 分区地址: 0x%X\n", running->address);
    Serial.printf("📏 分区大小: %d KB\n", running->size / 1024);
  }
  
  // 查找所有分区
  esp_partition_iterator_t it = esp_partition_find(ESP_PARTITION_TYPE_ANY, 
                                                   ESP_PARTITION_SUBTYPE_ANY, 
                                                   NULL);
  
  Serial.println("\n📋 所有分区:");
  while (it != NULL) {
    const esp_partition_t* partition = esp_partition_get(it);
    
    const char* type_name = "Unknown";
    if (partition->type == ESP_PARTITION_TYPE_APP) {
      type_name = "APP";
    } else if (partition->type == ESP_PARTITION_TYPE_DATA) {
      type_name = "DATA";
    }
    
    Serial.printf("  📄 %s: %s (%s, %d KB)\n", 
                  partition->label, 
                  type_name,
                  (partition->subtype == ESP_PARTITION_SUBTYPE_DATA_SPIFFS) ? "SPIFFS" : "Other",
                  partition->size / 1024);
    
    it = esp_partition_next(it);
  }
  esp_partition_iterator_release(it);
}

void printCompilationInfo() {
  printSeparator("编译信息");
  
  Serial.printf("🛠️  编译时间: %s %s\n", __DATE__, __TIME__);
  Serial.printf("🔧 Arduino 版本: %d.%d.%d\n", 
                ARDUINO / 10000, (ARDUINO / 100) % 100, ARDUINO % 100);
  Serial.printf("📦 ESP-IDF 版本: %s\n", esp_get_idf_version());
  
  // 编译标志
  Serial.println("\n🏗️  编译配置:");
  
#ifdef CAMERA_MODEL_ESP32S3_EYE
  Serial.println("  ✅ CAMERA_MODEL_ESP32S3_EYE");
#endif

#ifdef BOARD_HAS_PSRAM
  Serial.println("  ✅ BOARD_HAS_PSRAM");
#endif

#ifdef CONFIG_SPIRAM_SUPPORT
  Serial.println("  ✅ CONFIG_SPIRAM_SUPPORT");
#endif

#ifdef FORMAT_LITTLEFS_IF_FAILED
  Serial.println("  ✅ FORMAT_LITTLEFS_IF_FAILED");
#endif
}

void setup() {
  Serial.begin(115200);
  delay(2000);  // 等待串口稳定
  
  Serial.println("\n\n🚀 ESP32-S3-EYE 硬件信息检测");
  Serial.println("版本: 1.0");
  Serial.println("时间: " + String(__DATE__) + " " + String(__TIME__));
  
  // 执行各项检测
  printChipInfo();
  printFlashInfo();
  printPSRAMInfo();
  printMemoryInfo();
  printPartitionInfo();
  printCompilationInfo();
  
  // 总结
  printSeparator("检测总结");
  Serial.println("✅ 硬件信息检测完成");
  Serial.println("💡 如需重新检测，请重启设备");
  Serial.println("📖 详细信息请参考上述输出");
  
  // ESP32-S3-EYE 预期规格对比
  Serial.println("\n📋 ESP32-S3-EYE 标准规格对比:");
  Serial.println("  预期 Flash: 16MB");
  Serial.println("  预期 PSRAM: 8MB");
  Serial.println("  预期芯片: ESP32-S3");
  
  uint32_t flash_mb = ESP.getFlashChipSize() / 1024 / 1024;
  uint32_t psram_mb = psramFound() ? ESP.getPsramSize() / 1024 / 1024 : 0;
  
  Serial.printf("  实际 Flash: %dMB %s\n", flash_mb, 
                (flash_mb >= 16) ? "✅" : "⚠️");
  Serial.printf("  实际 PSRAM: %dMB %s\n", psram_mb, 
                (psram_mb >= 8) ? "✅" : "⚠️");
}

void loop() {
  // 每30秒显示一次内存状态
  delay(30000);
  
  Serial.println("\n📊 实时内存状态:");
  Serial.printf("  可用内存: %d KB\n", ESP.getFreeHeap() / 1024);
  if (psramFound()) {
    Serial.printf("  可用PSRAM: %d KB\n", ESP.getFreePsram() / 1024);
  }
}
