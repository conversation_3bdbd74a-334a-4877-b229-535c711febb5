#!/usr/bin/env python3
"""
测试 collecttrain.html 集成的脚本
验证所有修改是否正确实施
"""

import os
import re

def test_collecttrain_html():
    """测试 collecttrain.html 修改"""
    print("🔍 测试 collecttrain.html 修改...")
    
    if not os.path.exists("data/collecttrain.html"):
        print("❌ 未找到 data/collecttrain.html")
        return False
    
    with open("data/collecttrain.html", "r", encoding="utf-8") as f:
        content = f.read()
    
    checks = {
        "img元素替换video": '<img id="webcam"' in content,
        "摄像头状态显示": 'id="camera-status"' in content,
        "导航栏": 'navbar' in content and 'ESP32-S3 AI 摄像头' in content,
        "主页链接": 'href="/"' in content,
        "WebSocket说明": 'WebSocket' in content or 'websocket' in content.lower()
    }
    
    print("📋 collecttrain.html 检查结果:")
    all_good = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def test_collecttrain_js():
    """测试 collecttrain.js 修改"""
    print("\n🔍 测试 collecttrain.js 修改...")
    
    if not os.path.exists("data/js/collecttrain.js"):
        print("❌ 未找到 data/js/collecttrain.js")
        return False
    
    with open("data/js/collecttrain.js", "r", encoding="utf-8") as f:
        content = f.read()
    
    checks = {
        "WebSocket变量定义": "wsCamera" in content and "wsConnected" in content,
        "connectCameraWebSocket函数": "function connectCameraWebSocket" in content,
        "switchToHttpStream函数": "function switchToHttpStream" in content,
        "updateCameraStatus函数": "function updateCameraStatus" in content,
        "imgElement变量": "imgElement" in content,
        "canvas绘制修改": "drawImage(imgElement" in content,
        "WebSocket连接调用": "connectCameraWebSocket" in content and "setTimeout" in content
    }
    
    print("📋 collecttrain.js 检查结果:")
    all_good = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def test_server_routes():
    """测试服务器路由修改"""
    print("\n🔍 测试服务器路由修改...")
    
    if not os.path.exists("src/camera_server.cpp"):
        print("❌ 未找到 src/camera_server.cpp")
        return False
    
    with open("src/camera_server.cpp", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查路由定义
    route_definitions = {
        "collecttrain页面路由": "collecttrain_uri" in content,
        "collecttrain JS路由": "collecttrain_js_uri" in content,
        "jQuery路由": "jquery_uri" in content,
        "JSZip路由": "jszip_uri" in content,
        "FileSaver路由": "filesaver_uri" in content,
        "TensorFlow.js可视化路由": "tfjs_vis_uri" in content
    }
    
    # 检查路由注册
    route_registrations = {
        "collecttrain页面注册": "httpd_register_uri_handler(camera_httpd, &collecttrain_uri)" in content,
        "collecttrain JS注册": "httpd_register_uri_handler(camera_httpd, &collecttrain_js_uri)" in content,
        "jQuery注册": "httpd_register_uri_handler(camera_httpd, &jquery_uri)" in content,
        "JSZip注册": "httpd_register_uri_handler(camera_httpd, &jszip_uri)" in content,
        "FileSaver注册": "httpd_register_uri_handler(camera_httpd, &filesaver_uri)" in content,
        "TensorFlow.js可视化注册": "httpd_register_uri_handler(camera_httpd, &tfjs_vis_uri)" in content
    }
    
    print("📋 服务器路由检查结果:")
    all_good = True
    
    print("  路由定义:")
    for check_name, result in route_definitions.items():
        status = "✅" if result else "❌"
        print(f"    {status} {check_name}")
        if not result:
            all_good = False
    
    print("  路由注册:")
    for check_name, result in route_registrations.items():
        status = "✅" if result else "❌"
        print(f"    {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def test_index_html():
    """测试 index.html 导航链接"""
    print("\n🔍 测试 index.html 导航链接...")
    
    if not os.path.exists("data/index.html"):
        print("❌ 未找到 data/index.html")
        return False
    
    with open("data/index.html", "r", encoding="utf-8") as f:
        content = f.read()
    
    checks = {
        "导航链接容器": "nav-links" in content,
        "主页链接": 'href="/"' in content,
        "数据采集训练链接": 'href="/collecttrain.html"' in content,
        "链接样式": "text-decoration: none" in content
    }
    
    print("📋 index.html 检查结果:")
    all_good = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def test_required_files():
    """测试必需的文件是否存在"""
    print("\n🔍 测试必需文件...")
    
    required_files = [
        "data/collecttrain.html",
        "data/js/collecttrain.js",
        "data/js/jquery-3.6.0.min.js.gz",
        "data/js/jszip.min.js.gz",
        "data/js/file-saver.js.gz",
        "data/js/tfjs-vis.js.gz",
        "data/index.html",
        "src/camera_server.cpp"
    ]
    
    print("📋 必需文件检查:")
    all_good = True
    for file_path in required_files:
        exists = os.path.exists(file_path)
        status = "✅" if exists else "❌"
        print(f"  {status} {file_path}")
        if not exists:
            all_good = False
    
    return all_good

def main():
    """主测试函数"""
    print("🚀 collecttrain.html 集成测试")
    print("=" * 50)
    
    tests = [
        ("必需文件检查", test_required_files),
        ("collecttrain.html 修改", test_collecttrain_html),
        ("collecttrain.js 修改", test_collecttrain_js),
        ("服务器路由修改", test_server_routes),
        ("index.html 导航链接", test_index_html)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            print(f"✅ {test_name} 测试通过")
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！collecttrain.html 集成完成")
        print("\n📝 下一步操作:")
        print("1. 编译并上传固件:")
        print("   pio run -e seeed_xiao_esp32s3_sense -t upload")
        print("2. 上传文件系统:")
        print("   pio run -e seeed_xiao_esp32s3_sense -t uploadfs")
        print("3. 访问页面:")
        print("   http://[设备IP]/collecttrain.html")
        return True
    else:
        print("⚠️  部分测试失败，请检查修改")
        return False

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except Exception as e:
        print(f"💥 测试过程出错: {e}")
        exit(1)
