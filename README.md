# ESP32-S3 Camera Web Server - PlatformIO项目

这是一个从Arduino IDE项目转换而来的PlatformIO项目，实现了基于ESP32-S3的AI自动驾驶小车摄像头Web服务器。

## 项目特性

- 🎥 ESP32-S3摄像头实时视频流
- 🌐 Web界面控制
- 🤖 集成KNN算法的AI分类
- 📡 WebSocket实时通信
- 🔵 蓝牙连接支持
- 📱 响应式Web界面
- ⚙️ 串口命令配置

## 项目结构

```
├── platformio.ini          # PlatformIO配置文件
├── partitions.csv          # ESP32分区表
├── include/                # 头文件目录
│   ├── camera_pins.h       # 摄像头引脚定义
│   └── app_config.h        # 应用配置和全局声明
├── src/                    # 源代码目录
│   ├── main.cpp            # 主程序文件
│   ├── camera_init.cpp     # 摄像头初始化
│   └── camera_server.cpp   # Web服务器
└── README.md               # 项目说明文档
```

## 硬件要求

### 支持的开发板
- **ESP32-S3-DevKitC-1** - 通用ESP32-S3开发板
- **Seeed Studio XIAO ESP32S3 Sense** - 小尺寸开发板，集成摄像头
- **ESP32-S3-EYE** - 乐鑫官方AI开发板，集成摄像头和LCD ⭐ **新增支持**

### 硬件规格要求
- ESP32-S3芯片（双核 Xtensa LX7 @ 240MHz）
- 16MB Flash + 8MB PSRAM（推荐）
- OV2640摄像头模块
- 可选：外部蓝牙设备（小车控制）

## 软件要求

- PlatformIO Core 或 PlatformIO IDE
- 支持Web Bluetooth的现代浏览器

## 安装和配置

### 1. 克隆或下载项目

```bash
git clone <repository-url>
cd esp32-camera-platformio
```

### 2. 使用PlatformIO构建

```bash
# 使用PlatformIO CLI
pio run

# 或者使用VS Code PlatformIO扩展
# 打开项目文件夹，点击构建按钮
```

### 3. 上传到ESP32-S3

```bash
# 上传固件
pio run --target upload

# 查看串口输出
pio device monitor
```

## 配置说明

### WiFi配置

项目支持两种WiFi模式：

#### 方法1：通过串口命令配置

连接串口监视器（波特率115200），使用以下AT命令：

```
AT+CWJAP="你的WiFi名称","你的WiFi密码"    # 设置WiFi凭据
AT+CWMODE=1                              # 客户端模式
AT+CWMODE=2                              # AP模式  
AT+CWJAP?                                # 查询当前设置
AT+RST                                   # 重启ESP32
AT+HELP                                  # 显示帮助
```

#### 方法2：修改代码默认值

编辑 `src/main.cpp` 中的默认WiFi设置：

```cpp
String sta_ssid = "你的WiFi名称";
String sta_password = "你的WiFi密码";
bool USE_AP_MODE = false;  // true为AP模式，false为客户端模式
```

### 摄像头配置

项目支持多种开发板，在 `platformio.ini` 中选择对应的环境：

### 支持的环境配置

#### 1. ESP32-S3-DevKitC-1
```bash
pio run -e esp32-s3-devkitc-1
```

#### 2. Seeed Studio XIAO ESP32S3 Sense
```bash
pio run -e seeed_xiao_esp32s3_sense
```

#### 3. ESP32-S3-EYE (新增支持)
```bash
pio run -e esp32s3_eye
```

### 摄像头模型配置
每个环境都有对应的摄像头模型定义：

```ini
# ESP32-S3-DevKitC-1 和 XIAO ESP32S3
build_flags = -DCAMERA_MODEL_XIAO_ESP32S3

# ESP32-S3-EYE
build_flags = -DCAMERA_MODEL_ESP32S3_EYE
```

## 使用说明

### 1. 启动设备

上传固件后，ESP32会自动启动。通过串口监视器可以看到：

- WiFi连接状态
- IP地址信息
- 摄像头初始化状态
- Web服务器启动信息

### 2. 访问Web界面

#### 客户端模式
- 浏览器访问：`http://设备IP地址`
- 或者：`http://esp32.local`（如果支持mDNS）

#### AP模式
- 连接到WiFi：`ESP32_AI_Car`（密码：`12345678`）
- 浏览器访问：`http://***********`

### 3. 使用AI分类功能

1. 在Web界面中等待TensorFlow.js模型加载
2. 点击方向按钮（go、left、right、stop）添加训练样本
3. 添加足够样本后，系统会自动开始分类
4. 可以通过蓝牙连接小车进行实际控制

## 故障排除

### 摄像头初始化失败

1. 检查摄像头连接
2. 确认引脚定义正确
3. 尝试不同的摄像头型号配置

### WiFi连接问题

1. 检查WiFi凭据是否正确
2. 确认信号强度足够
3. 尝试使用AP模式

### Web界面无法访问

1. 确认ESP32已连接到网络
2. 检查防火墙设置
3. 尝试使用IP地址而非域名

### 构建错误

1. 确保PlatformIO平台和工具链是最新版本
2. 清理构建缓存：`pio run --target clean`
3. 检查库依赖是否正确安装

## 开发说明

### 从Arduino IDE迁移

原始Arduino项目包含以下文件：
- `esp32_one_TM.ino` → `src/main.cpp`
- `camera_init.ino` → `src/camera_init.cpp`
- `camera_server.ino` → `src/camera_server.cpp`
- `camera_pins.h` → `include/camera_pins.h`

主要变化：
1. 将`.ino`文件重命名为`.cpp`
2. 添加适当的头文件包含
3. 在`include/app_config.h`中集中管理配置
4. 在`platformio.ini`中管理库依赖

### 自定义配置

#### 修改摄像头引脚

编辑 `include/app_config.h` 中的引脚定义：

```cpp
#define XCLK_GPIO_NUM 7
#define Y9_GPIO_NUM 6
// ... 其他引脚定义
```

#### 添加新功能

1. 在 `include/app_config.h` 中添加声明
2. 在相应的 `.cpp` 文件中实现功能
3. 在 `src/main.cpp` 中调用

## 技术支持

如遇到问题，请：

1. 检查串口输出中的错误信息
2. 确认硬件连接正确
3. 参考ESP32-S3和ESP32-CAM相关文档
4. 提交issue时请包含详细的错误信息和硬件配置

## 许可证

本项目遵循原始项目的许可证条款。

## 更新日志

### v1.1.0 (多开发板支持版本)
- ✨ 新增 ESP32-S3-EYE 开发板支持
- 📚 添加专门的快速开始指南
- 🔧 优化多开发板环境配置
- 📖 完善文档和使用说明

### v1.0.0 (PlatformIO转换版本)
- 从Arduino IDE项目完全迁移到PlatformIO
- 重构代码结构，改善可维护性
- 添加详细的配置说明和故障排除指南
- 保持所有原始功能完整性

## 快速开始指南

根据您的开发板选择对应的快速开始指南：

- 📘 [XIAO ESP32S3 Sense 快速开始](QUICK_START_XIAO.md)
- 📗 [ESP32-S3-EYE 快速开始](QUICK_START_ESP32S3_EYE.md) ⭐ **新增**

## 技术支持

如遇到问题，请：
1. 查看对应开发板的快速开始指南
2. 检查串口输出日志
3. 参考故障排除部分
4. 提交 Issue 并附上详细信息