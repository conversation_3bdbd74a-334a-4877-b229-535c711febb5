#!/usr/bin/env python3
import gzip
import shutil
import os
import glob

def compress_file(input_path, output_path):
    """压缩文件为gzip格式"""
    try:
        with open(input_path, 'rb') as f_in:
            with gzip.open(output_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        # 获取文件大小信息
        original_size = os.path.getsize(input_path)
        compressed_size = os.path.getsize(output_path)
        compression_ratio = (1 - compressed_size / original_size) * 100
        
        print(f"✓ {os.path.basename(input_path)}: {original_size:,} → {compressed_size:,} bytes ({compression_ratio:.1f}% 压缩)")
        return True
        
    except Exception as e:
        print(f"✗ {os.path.basename(input_path)}: 压缩失败 - {e}")
        return False

def main():
    js_dir = "data/js"
    
    # 查找所有JS文件（排除已经压缩的文件）
    js_files = []
    for file in glob.glob(os.path.join(js_dir, "*.js")):
        if not file.endswith('.gz'):
            js_files.append(file)
    
    print(f"找到 {len(js_files)} 个JS文件需要压缩:")
    print("-" * 60)
    
    total_original = 0
    total_compressed = 0
    success_count = 0
    
    for js_file in js_files:
        gz_file = js_file + ".gz"
        
        # 如果gzip文件已存在且比原文件新，跳过
        if os.path.exists(gz_file) and os.path.getmtime(gz_file) > os.path.getmtime(js_file):
            print(f"⏭  {os.path.basename(js_file)}: 已存在更新的压缩文件")
            continue
        
        original_size = os.path.getsize(js_file)
        total_original += original_size
        
        if compress_file(js_file, gz_file):
            total_compressed += os.path.getsize(gz_file)
            success_count += 1
    
    print("-" * 60)
    if success_count > 0:
        overall_ratio = (1 - total_compressed / total_original) * 100
        print(f"压缩完成! 总计: {total_original:,} → {total_compressed:,} bytes")
        print(f"总体压缩率: {overall_ratio:.1f}%")
        print(f"节省空间: {total_original - total_compressed:,} bytes")
    else:
        print("没有文件需要压缩")

if __name__ == "__main__":
    main() 