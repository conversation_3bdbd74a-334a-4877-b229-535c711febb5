<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>AI自动驾驶小车-KNN算法</title>
    <script src="/js/tfjs.js"></script>
    <script src="/js/knn-classifier.js"></script>
    <link rel="stylesheet" href="/css/style.css">
</head>

<body>
    <div class="container">
        <h3>AI自动驾驶小车-KNN算法</h3>

        <!-- 导航链接 -->
        <div class="nav-links" style="text-align: center; margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
            <a href="/" style="margin: 0 10px; text-decoration: none; color: #007bff; font-weight: bold;">🏠 主页</a>
            <a href="/collecttrain.html" style="margin: 0 10px; text-decoration: none; color: #28a745;">📊 数据采集训练</a>
        </div>

        <div class="image-container">
            <img id="img" crossorigin alt="Camera Stream" width="224" height="224" />
        </div>
        <div class="sample-counts">
            <span class="sample-counter counter-go">go(<span id="count-go">0</span>)</span>
            <span class="sample-counter counter-left">left(<span id="count-left">0</span>)</span>
            <span class="sample-counter counter-right">right(<span id="count-right">0</span>)</span>
            <span class="sample-counter counter-stop">stop(<span id="count-stop">0</span>)</span>
        </div>
        <div id="status">状态: 加载模型中...</div>

        <div class="system-info-container">
            <div id="fps">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"></path></svg>
                <span>帧率: -- FPS</span>
            </div>
            <div id="memory-info">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>
                <span>内存使用: -- MB</span>
            </div>
        </div>

        <div class="k-value-control">
            <label for="k-value">K值:</label>
            <input type="range" id="k-value" min="1" max="10" value="3" step="1">
            <span id="k-value-display">3</span>
        </div>

        <div class="button-group sample-button-group">
            <div class="sample-buttons-container">
                <div class="sample-buttons-row">
                    <button id="class-go">go</button>
                    <button id="class-left">left</button>
                    <button id="class-right">right</button>
                    <button id="class-stop">stop</button>
                </div>
            </div>
        </div>

        <div class="control-section">
            <div class="switch-container">
                <label for="send-command-toggle">发送指令到小车:</label>
                <label class="switch">
                    <input type="checkbox" id="send-command-toggle">
                    <span class="slider round"></span>
                </label>
                <span id="toggle-status">已禁用</span>
            </div>

            <div class="buttons-row">
                <button id="connBle" class="btn" type="button">连接蓝牙</button>
                <button id="disconnBle" class="btn" type="button" disabled="true">断开蓝牙</button>
            </div>

            <div class="button-group">
                <button id="save-data">保存KNN数据</button>
                <button id="load-data">载入KNN数据</button>
            </div>
            
            <div class="file-input-container">
                <label for="knn-file-name">KNN数据文件名:</label>
                <input type="text" id="knn-file-name" value="myKnnDataset">
                <input type="file" id="file-selector" accept=".json" style="display: none;">
                <button onclick="document.getElementById('file-selector').click()">选择文件</button>
                <span id="file-selection-status">未选择文件</span>
            </div>
        </div>

        <div id="console">等待模型加载...</div>
        <div class="button-group reset-button-group">
            <button id="reset">重置分类器</button>
            <button id="clearMobileNetCache" style="background-color: #ff9800;">清除模型缓存</button>
        </div>
    </div>
     <div class="container">
    <div>开发：上海市松江区青少年综合实践教育中心 汤铭</div> 
    </div>

    <script src="/js/app.js"></script>
</body>

</html>