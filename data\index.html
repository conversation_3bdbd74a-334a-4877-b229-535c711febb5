<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>AI自动驾驶小车-KNN算法</title>
    <!-- 智能JS文件加载器 -->
    <script>
        // 智能加载JS文件，优先尝试gzip版本，失败则加载未压缩版本
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.type = 'text/javascript';
                script.async = false; // 确保按顺序加载

                script.onload = function() {
                    console.log(`✅ 成功加载: ${src}`);
                    resolve(src);
                };

                script.onerror = function() {
                    console.log(`❌ 加载失败: ${src}`);
                    reject(new Error(`Failed to load script: ${src}`));
                };

                script.src = src;
                document.head.appendChild(script);
            });
        }

        // 尝试加载gzip版本，失败则加载未压缩版本
        async function loadScriptWithFallback(basePath, filename) {
            const gzipPath = `${basePath}${filename}.gz`;
            const normalPath = `${basePath}${filename}`;

            try {
                // 首先尝试加载gzip版本
                await loadScript(gzipPath);
                console.log(`📦 使用压缩版本: ${filename}.gz`);
            } catch (error) {
                try {
                    // gzip版本失败，尝试未压缩版本
                    await loadScript(normalPath);
                    console.log(`📄 使用未压缩版本: ${filename}`);
                } catch (fallbackError) {
                    console.error(`💥 无法加载文件: ${filename}`, fallbackError);
                    throw fallbackError;
                }
            }
        }

        // 按顺序加载主页所需的JS文件
        async function loadMainPageScripts() {
            console.log('🚀 开始加载主页JS文件...');

            const scripts = [
                { path: '/js/', filename: 'tfjs.js' },
                { path: '/js/', filename: 'knn-classifier.js' }
            ];

            try {
                for (const script of scripts) {
                    await loadScriptWithFallback(script.path, script.filename);
                }
                console.log('✅ 主页JS文件加载完成');
            } catch (error) {
                console.error('💥 主页JS文件加载失败:', error);
                alert('页面资源加载失败，请刷新页面重试');
            }
        }

        // 页面加载完成后开始加载脚本
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadMainPageScripts);
        } else {
            loadMainPageScripts();
        }
    </script>
    <link rel="stylesheet" href="/css/style.css">
</head>

<body>
    <div class="container">
        <h3>AI自动驾驶小车-KNN算法</h3>

        <!-- 导航链接 -->
        <div class="nav-links" style="text-align: center; margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
            <a href="/" style="margin: 0 10px; text-decoration: none; color: #007bff; font-weight: bold;">🏠 主页</a>
            <a href="/collecttrain.html" style="margin: 0 10px; text-decoration: none; color: #28a745;">📊 数据采集训练</a>
        </div>

        <div class="image-container">
            <img id="img" crossorigin alt="Camera Stream" width="224" height="224" />
        </div>
        <div class="sample-counts">
            <span class="sample-counter counter-go">go(<span id="count-go">0</span>)</span>
            <span class="sample-counter counter-left">left(<span id="count-left">0</span>)</span>
            <span class="sample-counter counter-right">right(<span id="count-right">0</span>)</span>
            <span class="sample-counter counter-stop">stop(<span id="count-stop">0</span>)</span>
        </div>
        <div id="status">状态: 加载模型中...</div>

        <div class="system-info-container">
            <div id="fps">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"></path></svg>
                <span>帧率: -- FPS</span>
            </div>
            <div id="memory-info">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>
                <span>内存使用: -- MB</span>
            </div>
        </div>

        <div class="k-value-control">
            <label for="k-value">K值:</label>
            <input type="range" id="k-value" min="1" max="10" value="3" step="1">
            <span id="k-value-display">3</span>
        </div>

        <div class="button-group sample-button-group">
            <div class="sample-buttons-container">
                <div class="sample-buttons-row">
                    <button id="class-go">go</button>
                    <button id="class-left">left</button>
                    <button id="class-right">right</button>
                    <button id="class-stop">stop</button>
                </div>
            </div>
        </div>

        <div class="control-section">
            <div class="switch-container">
                <label for="send-command-toggle">发送指令到小车:</label>
                <label class="switch">
                    <input type="checkbox" id="send-command-toggle">
                    <span class="slider round"></span>
                </label>
                <span id="toggle-status">已禁用</span>
            </div>

            <div class="buttons-row">
                <button id="connBle" class="btn" type="button">连接蓝牙</button>
                <button id="disconnBle" class="btn" type="button" disabled="true">断开蓝牙</button>
            </div>

            <div class="button-group">
                <button id="save-data">保存KNN数据</button>
                <button id="load-data">载入KNN数据</button>
            </div>
            
            <div class="file-input-container">
                <label for="knn-file-name">KNN数据文件名:</label>
                <input type="text" id="knn-file-name" value="myKnnDataset">
                <input type="file" id="file-selector" accept=".json" style="display: none;">
                <button onclick="document.getElementById('file-selector').click()">选择文件</button>
                <span id="file-selection-status">未选择文件</span>
            </div>
        </div>

        <div id="console">等待模型加载...</div>
        <div class="button-group reset-button-group">
            <button id="reset">重置分类器</button>
        </div>
    </div>
     <div class="container">
    <div>开发：上海市松江区青少年综合实践教育中心 汤铭</div> 
    </div>

    <script src="/js/app.js"></script>
</body>

</html>