# 📡 AP模式使用指南

## 🎯 当前配置

项目已强制设置为AP模式，具体配置如下：

### 🔧 AP模式设置
- **SSID**: `ESP32 AI Car XXX` (XXX是设备唯一ID)
- **密码**: `12345678`
- **IP地址**: `***********`
- **网关**: `***********`
- **子网掩码**: `*************`

## 📱 连接步骤

### 1. 连接WiFi网络

1. **手机/平板连接**:
   - 打开WiFi设置
   - 搜索WiFi网络 `ESP32 AI Car XXX`
   - 输入密码: `12345678`
   - 连接成功

2. **电脑连接**:
   - 打开网络设置
   - 连接到 `ESP32 AI Car XXX`
   - 输入密码: `12345678`

### 2. 访问Web界面

连接WiFi后，打开浏览器访问：

```
http://***********
```

或者使用mDNS地址（如果支持）：
```
http://esp32.local
```

## 🎮 功能使用

### 实时视频流
- 页面加载后会自动显示摄像头画面
- 支持240x240分辨率，60fps帧率
- 适合AI分类应用

### AI分类训练
1. 等待TensorFlow.js模型加载完成
2. 点击方向按钮添加训练样本：
   - `go` - 前进
   - `left` - 左转
   - `right` - 右转
   - `stop` - 停止
3. 添加足够样本后自动开始分类

### 蓝牙控制
- 启用"发送指令到小车"开关
- 连接蓝牙设备
- 实时控制小车移动

## 🔧 串口命令

通过串口监视器可以执行以下命令：

```
AT+HELP                    # 显示帮助信息
AT+CWMODE=2               # 强制设置为AP模式
AT+CWMODE=1               # 设置为客户端模式
AT+CWJAP="SSID","PASS"    # 设置WiFi凭据（客户端模式）
AT+RST                    # 重启设备
```

## 🚨 故障排除

### 无法连接WiFi
1. **检查SSID名称**: 确认连接的是正确的网络
2. **检查密码**: 默认密码是 `12345678`
3. **重启设备**: 使用 `AT+RST` 命令重启
4. **检查信号强度**: 确保设备在范围内

### 无法访问Web界面
1. **检查IP地址**: 确认访问的是 `***********`
2. **检查浏览器**: 尝试不同的浏览器
3. **清除缓存**: 清除浏览器缓存
4. **检查防火墙**: 确保没有防火墙阻止

### 摄像头不工作
1. **检查串口输出**: 查看摄像头初始化状态
2. **重启设备**: 使用 `AT+RST` 命令重启
3. **检查硬件连接**: 确认摄像头模块连接正确

## 📊 性能优化

### 网络性能
- **最大连接数**: 支持多个设备同时连接
- **传输速度**: 优化为60fps视频流
- **延迟**: 最小化网络延迟

### 内存使用
- **PSRAM**: 8MB PSRAM用于图像处理
- **内部RAM**: 优化内存分配
- **垃圾回收**: 定期执行内存整理

## 🔒 安全说明

### 网络安全
- **密码保护**: WiFi网络有密码保护
- **本地网络**: 仅在本地网络可访问
- **无外网连接**: AP模式不连接外网

### 数据安全
- **本地处理**: 所有数据在本地处理
- **无数据上传**: 不会上传任何数据
- **隐私保护**: 摄像头数据不会外泄

## 📝 注意事项

1. **首次使用**: 设备启动后会自动进入AP模式
2. **设备ID**: 每个设备有唯一的3字母ID
3. **电池使用**: AP模式会消耗更多电量
4. **温度控制**: 长时间使用注意设备温度
5. **存储空间**: 定期清理不需要的数据

## 🎉 使用提示

- **最佳距离**: 建议在1-5米范围内使用
- **环境光线**: 确保有足够的光线
- **稳定放置**: 将设备放置在稳定位置
- **定期重启**: 建议每天重启一次设备

---

**🎯 现在你的XIAO ESP32S3 Sense已经配置为AP模式，可以直接连接使用！** 