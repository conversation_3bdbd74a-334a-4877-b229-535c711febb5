#!/usr/bin/env python3
"""
ESP32-S3-EYE 硬件信息检测工具
检测 Flash 大小、PSRAM 大小和其他硬件信息
"""

import subprocess
import re
import sys
import json

def run_command(command, description=""):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令超时"
    except Exception as e:
        return False, "", str(e)

def detect_with_esptool():
    """使用 esptool 检测硬件信息"""
    print("🔍 使用 esptool 检测硬件信息...")
    
    # 检测可用端口
    success, stdout, stderr = run_command("pio device list")
    if success and stdout:
        print("📱 检测到的串口设备:")
        lines = stdout.split('\n')
        ports = []
        for line in lines:
            if 'COM' in line or '/dev/tty' in line:
                # 提取端口号
                port_match = re.search(r'(COM\d+|/dev/tty\w+)', line)
                if port_match:
                    port = port_match.group(1)
                    ports.append(port)
                    print(f"  📍 {line.strip()}")
        
        if not ports:
            print("❌ 未检测到串口设备")
            return None
        
        # 使用第一个端口进行检测
        port = ports[0]
        print(f"\n🎯 使用端口 {port} 进行检测...")
    else:
        print("⚠️  无法检测串口，尝试使用 COM11...")
        port = "COM11"
    
    # 使用 esptool 获取芯片信息
    commands = [
        f"esptool.py --port {port} chip_id",
        f"esptool.py --port {port} flash_id",
        f"esptool.py --port {port} --chip esp32s3 get_security_info"
    ]
    
    hardware_info = {}
    
    for cmd in commands:
        print(f"\n📝 执行: {cmd}")
        success, stdout, stderr = run_command(cmd)
        
        if success:
            print("✅ 命令执行成功")
            print(stdout)
            
            # 解析输出
            if "chip_id" in cmd:
                # 解析芯片信息
                chip_match = re.search(r'Chip is (ESP32-S3)', stdout)
                if chip_match:
                    hardware_info['chip'] = chip_match.group(1)
                
                # 解析 MAC 地址
                mac_match = re.search(r'MAC: ([0-9a-f:]+)', stdout)
                if mac_match:
                    hardware_info['mac'] = mac_match.group(1)
            
            elif "flash_id" in cmd:
                # 解析 Flash 信息
                flash_match = re.search(r'Detected flash size: (\d+MB)', stdout)
                if flash_match:
                    hardware_info['flash_size'] = flash_match.group(1)
                
                # 解析制造商信息
                mfg_match = re.search(r'Manufacturer: ([0-9a-f]+)', stdout)
                if mfg_match:
                    hardware_info['flash_manufacturer'] = mfg_match.group(1)
        else:
            print(f"❌ 命令失败: {stderr}")
    
    return hardware_info

def detect_with_platformio():
    """使用 PlatformIO 检测信息"""
    print("\n🔍 使用 PlatformIO 检测信息...")
    
    # 检查 platformio.ini 配置
    try:
        with open('platformio.ini', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("📋 PlatformIO 配置信息:")
        
        # 提取相关配置
        configs = {
            'board': re.search(r'board\s*=\s*(.+)', content),
            'flash_mode': re.search(r'board_build\.flash_mode\s*=\s*(.+)', content),
            'psram_type': re.search(r'board_build\.psram_type\s*=\s*(.+)', content),
            'memory_type': re.search(r'board_build\.arduino\.memory_type\s*=\s*(.+)', content),
            'f_flash': re.search(r'board_build\.f_flash\s*=\s*(.+)', content),
            'f_cpu': re.search(r'board_build\.f_cpu\s*=\s*(.+)', content)
        }
        
        for key, match in configs.items():
            if match:
                value = match.group(1).strip()
                print(f"  {key}: {value}")
        
        return configs
        
    except FileNotFoundError:
        print("❌ 未找到 platformio.ini 文件")
        return None

def create_hardware_test_sketch():
    """创建硬件检测的 Arduino 代码"""
    sketch_content = '''
/*
 * ESP32-S3-EYE 硬件信息检测
 * 编译并上传此代码到设备，通过串口查看详细硬件信息
 */

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("\\n" + String("=").substring(0, 50));
  Serial.println("🔍 ESP32-S3-EYE 硬件信息检测");
  Serial.println(String("=").substring(0, 50));
  
  // 基本芯片信息
  Serial.println("\\n📱 芯片信息:");
  Serial.printf("  芯片型号: %s\\n", ESP.getChipModel());
  Serial.printf("  芯片版本: %d\\n", ESP.getChipRevision());
  Serial.printf("  CPU 频率: %d MHz\\n", ESP.getCpuFreqMHz());
  Serial.printf("  CPU 核心数: %d\\n", ESP.getChipCores());
  
  // Flash 信息
  Serial.println("\\n💾 Flash 信息:");
  Serial.printf("  Flash 大小: %d KB (%.1f MB)\\n", 
                ESP.getFlashChipSize() / 1024, 
                ESP.getFlashChipSize() / 1024.0 / 1024.0);
  Serial.printf("  Flash 速度: %d MHz\\n", ESP.getFlashChipSpeed() / 1000000);
  Serial.printf("  Flash 模式: %d\\n", ESP.getFlashChipMode());
  
  // PSRAM 信息
  Serial.println("\\n🧠 PSRAM 信息:");
  if (psramFound()) {
    Serial.println("  PSRAM: ✅ 检测到");
    Serial.printf("  PSRAM 大小: %d KB (%.1f MB)\\n", 
                  ESP.getPsramSize() / 1024,
                  ESP.getPsramSize() / 1024.0 / 1024.0);
    Serial.printf("  PSRAM 可用: %d KB\\n", ESP.getFreePsram() / 1024);
  } else {
    Serial.println("  PSRAM: ❌ 未检测到");
  }
  
  // 内存信息
  Serial.println("\\n🔋 内存信息:");
  Serial.printf("  总内存: %d KB\\n", ESP.getHeapSize() / 1024);
  Serial.printf("  可用内存: %d KB\\n", ESP.getFreeHeap() / 1024);
  Serial.printf("  最大分配: %d KB\\n", ESP.getMaxAllocHeap() / 1024);
  
  // MAC 地址
  Serial.println("\\n🌐 网络信息:");
  Serial.printf("  WiFi MAC: %s\\n", WiFi.macAddress().c_str());
  
  // 分区信息
  Serial.println("\\n📊 分区信息:");
  const esp_partition_t* running = esp_ota_get_running_partition();
  if (running) {
    Serial.printf("  运行分区: %s\\n", running->label);
    Serial.printf("  分区大小: %d KB\\n", running->size / 1024);
  }
  
  Serial.println("\\n✅ 硬件检测完成");
  Serial.println(String("=").substring(0, 50));
}

void loop() {
  delay(10000);
}
'''
    
    with open('hardware_test.ino', 'w', encoding='utf-8') as f:
        f.write(sketch_content)
    
    print("📝 已创建硬件检测代码: hardware_test.ino")
    print("💡 您可以:")
    print("  1. 将此代码复制到 Arduino IDE")
    print("  2. 编译并上传到 ESP32-S3-EYE")
    print("  3. 打开串口监视器查看详细信息")

def main():
    """主检测流程"""
    print("🚀 ESP32-S3-EYE 硬件信息检测工具")
    print("=" * 50)
    
    # 方法1: 使用 esptool
    hardware_info = detect_with_esptool()
    
    # 方法2: 使用 PlatformIO 配置
    pio_info = detect_with_platformio()
    
    # 方法3: 创建硬件检测代码
    print("\\n🛠️  创建硬件检测代码...")
    create_hardware_test_sketch()
    
    # 汇总信息
    print("\\n📋 检测结果汇总:")
    print("=" * 50)
    
    if hardware_info:
        print("🔍 esptool 检测结果:")
        for key, value in hardware_info.items():
            print(f"  {key}: {value}")
    
    if pio_info:
        print("\\n⚙️  PlatformIO 配置:")
        for key, match in pio_info.items():
            if match:
                print(f"  {key}: {match.group(1).strip()}")
    
    # ESP32-S3-EYE 标准规格
    print("\\n📖 ESP32-S3-EYE 标准规格:")
    print("  芯片: ESP32-S3")
    print("  Flash: 16MB")
    print("  PSRAM: 8MB OPI PSRAM")
    print("  摄像头: OV2640")
    print("  显示屏: 1.3英寸 LCD")
    
    print("\\n💡 获取最准确信息的方法:")
    print("  1. 运行: python quick_fix_esp32s3_eye.py")
    print("  2. 上传 hardware_test.ino 代码")
    print("  3. 查看串口输出获取详细硬件信息")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\\n👋 检测已取消")
    except Exception as e:
        print(f"\\n💥 检测出错: {e}")
