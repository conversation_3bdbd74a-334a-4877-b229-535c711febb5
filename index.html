

<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>AI自动驾驶小车-KNN算法</title>
    <script src="https://fastly.jsdelivr.net/npm/@tensorflow/tfjs"></script>
    <script src="https://fastly.jsdelivr.net/npm/@tensorflow-models/knn-classifier"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 0;
            padding: 10px;
            max-width: 100%;
            box-sizing: border-box;
        }
        h3{
           margin: 0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 10px;
        }

        .image-container {
            margin: 10px auto;
            width: 224px;
            height: 224px;
            overflow: hidden;
        }

        .image-container img {
            width: 224px;
            height: 224px;
            object-fit: cover;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        #console {
            margin: 15px auto;
            padding: 10px;
            border: 1px solid #ddd;
            min-height: 40px;
            background-color: #fafafa;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            word-wrap: break-word;
            color: #333;
            text-align: left;
            font-weight: normal;
            overflow-x: auto;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        #console table {
            width: 100%;
            margin-top: 10px;
            border-collapse: collapse;
            font-size: 0.9em;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        
        #console th {
            background-color: #f0f0f0;
            font-weight: bold;
            padding: 6px 4px;
        }
        
        #console th, #console td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: center;
        }
        
        #console .top-k {
            background-color: #a5d6a7;  /* 更深的绿色，提供更好的对比度 */
            border: 1px solid #4CAF50;  /* 添加绿色边框 */
            font-weight: bold;  /* 加粗文本 */
        }

        /* 预测结果和投票情况样式 */
        #console .prediction-result {
            font-size: 1.1em;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
            color: #e53935;  /* 红色 */
        }
        
        #console .vote-result {
            font-size: 0.95em;
            margin-bottom: 8px;
            color: #e53935;  /* 红色 */
        }
        
        #console .vote-value {
            font-weight: bold;
            color: #b71c1c;  /* 深红色 */
        }

        button {
            margin: 5px 20px;
            padding: 5px 15px;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
        }

        button:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        button:active {
            transform: translateY(0);
        }

        #status {
            margin: 5px auto;
            color: #666;
            width: 90%;
            max-width: 600px;
            padding: 5px;
            font-weight: bold;
            background-color: #f0f8ff;
            border-radius: 5px;
            border: 1px dashed #ccc;
        }

        .sample-counts {
            margin: 5px 5px;
            display: flex;
            justify-content: center;
            flex-wrap: nowrap; /* 改为nowrap防止换行 */
            gap: 5px; /* 减小间距 */
            width: 90%;
            max-width: 600px;
        }

        .sample-counter {
            display: inline-block;
            padding: 5px 5px; /* 减小内边距 */
            border-radius: 15px;
            color: white;
            font-weight: bold;
            margin: 2px; /* 减小外边距 */
            font-size: 0.95em; /* 稍微减小字体大小 */
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin: 10px auto;
            width: 90%;
            max-width: 600px;
        }
        
        /* 添加样本按钮组的特殊样式 */
        .sample-button-group {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px dotted #eee;
        }
        
        /* 重置按钮组的特殊样式 */
        .reset-button-group {
            margin-top: 15px;
        }
        
        /* 样本按钮容器和行样式 */
        .sample-label {
            font-weight: bold;
            margin-right: 10px;
            display: inline-block;
            margin-bottom: 8px;
        }
        
        .sample-buttons-container {
            width: 100%;
            text-align: center;
            padding: 5px;
        }
        
        .sample-buttons-row {
            display: flex;
            justify-content: center;
            gap: 25px; /* 增加默认间距 */
            margin: 12px 0;
        }

        .counter-go {
            background-color: #2196F3;
        }

        .counter-left {
            background-color: #FF9800;
        }

        .counter-right {
            background-color: #9C27B0;
        }

        .counter-stop {
            background-color: #E91E63;
        }

        #class-go,
        #class-left,
        #class-right,
        #class-stop {
            margin: 5px 8px;
            padding: 12px 18px;
            font-size: 1em;
            min-width: 70px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #class-go {
            background-color: #2196F3;
        }

        #class-left {
            background-color: #FF9800;
        }

        #class-right {
            background-color: #9C27B0;
        }

        #class-stop {
            background-color: #E91E63;
        }

        .k-value-control {
            margin: 5px auto;
            width: 90%;
            max-width: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 8px 0 15px 0;
            border-bottom: 1px dotted #eee;
            background-color: #f9f9f9;
            border-radius: 8px;
        }

        .k-value-control label {
            font-weight: bold;
            color: #333;
            font-size: 1em;
        }

        .k-value-control input {
            width: 60%;
            max-width: 300px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            text-align: center;
            background-color: #fff;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .k-value-control span {
            font-weight: bold;
            color: #b71c1c;  /* 深红色，与预测结果匹配 */
            min-width: 30px;
            text-align: center;
            font-size: 1.2em;
            padding: 2px 8px;
            background-color: #ffebee;  /* 浅红色背景，与预测类别匹配 */
            border-radius: 4px;
        }

        /* 响应式设计 */
        @media screen and (max-width: 600px) {
            h3 {
                font-size: 1.2em;
            }

            button {
                padding: 10px 16px;
                font-size: 1em;
            }
            
            #class-go,
            #class-left,
            #class-right,
            #class-stop {
                margin: 8px;
                padding: 15px 20px;
                min-width: 80px;
                font-size: 1.1em;
            }
            
            .sample-buttons-row {
                gap: 16px;
            }

            .sample-counter {
                padding: 4px 8px; /* 在小屏幕上进一步减小内边距 */
                font-size: 0.85em; /* 在小屏幕上进一步减小字体 */
                margin: 1px; /* 减小外边距 */
            }

            .sample-counts {
                gap: 3px; /* 更小的间距 */
            }

            #console,
            #status {
                font-size: 0.9em;
            }
        }

        @media screen and (max-width: 400px) {
            button {
                margin: 5px;
                padding: 10px 15px;
                font-size: 0.9em;
            }
            
            #class-go,
            #class-left,
            #class-right,
            #class-stop {
                margin: 6px;
                padding: 14px 18px;
                min-width: 70px;
                font-size: 1em;
            }

            .button-group {
                width: 100%;
            }
            
            .sample-button-group {
                display: flex;
                flex-direction: column;
                width: 92%;
            }
            
            .sample-buttons-row {
                gap: 10px;
            }

            .sample-counter {
                padding: 3px 6px; /* 在超小屏幕上进一步减小内边距 */
                font-size: 0.8em; /* 在超小屏幕上进一步减小字体 */
                margin: 1px; /* 减小外边距 */
                border-radius: 10px; /* 减小圆角 */
            }

            .sample-counts {
                gap: 2px; /* 更小的间距 */
                width: 98%; /* 增加宽度利用更多空间 */
            }
        }

        /* 预测结果中类别名称的样式 */
        #console .predicted-class {
            font-weight: bold;
            font-size: 1.1em;
            color: #b71c1c;  /* 深红色 */
            background-color: #ffebee;  /* 浅红色背景 */
            padding: 2px 6px;
            border-radius: 4px;
            margin: 0 2px;
        }
        
        #console .probability {
            font-weight: bold;
            color: #b71c1c;  /* 深红色 */
        }

        /* K值的样式 */
        #console .k-value {
            color: #b71c1c;  /* 深红色 */
            font-weight: bold;
        }
        
        /* 投票情况标题 */
        #console .vote-title {
            display: inline-block;
            font-weight: bold;
            margin-right: 8px;
        }

        #reset,
        #connBle,
        #disconnBle {
            background-color: #0C7D40; /* 更深的绿色 */
            padding: 12px 25px;
            font-weight: bold;
            color: white; /* 确保文字是白色 */
            box-shadow: 0 2px 4px rgba(0,0,0,0.2); /* 添加阴影增强可见性 */
        }
        </style>
    </head>

    <body>
    <div class="container">
        <h3>AI自动驾驶小车-KNN算法</h3>
        <div class="image-container">
            <img id="img" crossorigin alt="Camera Stream" width="224" height="224" />
              </div>
        <div class="sample-counts">
            <span class="sample-counter counter-go">go(<span id="count-go">0</span>)</span>
            <span class="sample-counter counter-left">left(<span id="count-left">0</span>)</span>
            <span class="sample-counter counter-right">right(<span id="count-right">0</span>)</span>
            <span class="sample-counter counter-stop">stop(<span id="count-stop">0</span>)</span>
            </div>
        <div id="status">状态: 加载模型中...</div>
        <div class="k-value-control">
            <label for="k-value">K值:</label>
            <input type="range" id="k-value" min="1" max="10" value="3" step="1">
            <span id="k-value-display">3</span>
                        </div>

        <div class="button-group sample-button-group">
            <div class="sample-buttons-container">                
                <div class="sample-buttons-row">
                    <button id="class-go">go</button>
                    <button id="class-left">left</button>
                    <button id="class-right">right</button>
                    <button id="class-stop">stop</button>
                        </div>
                
                        </div>                                              
                        </div>                    
        <div class="button-group reset-button-group">
            <button id="connBle" type="button">连接小车蓝牙</button>
            <button id="disconnBle" type="button" disabled="true">断开小车蓝牙</button>
                        </div>
        <div id="console">等待模型加载...</div>
        <div class="button-group reset-button-group">
            <button id="reset">重置分类器</button>
                        </div>
                        </div>
        
        <script>
        ///////////////////////////////////////////////////////////////
        // WebSocket摄像头流支持
        let wsCamera = null;
        let wsConnected = false;
        let reconnectInterval = 5000; // 5秒重连间隔
        let wsReconnectTimer = null;
        let connectionAttempts = 0;
        const MAX_RECONNECT_ATTEMPTS = 5;
        let usingFallbackStream = false;
        
        // 连接到WebSocket摄像头服务器
        function connectCameraWebSocket() {
            // 清除之前的重连计时器
            if(wsReconnectTimer) {
                clearTimeout(wsReconnectTimer);
                wsReconnectTimer = null;
            }
            
            if(connectionAttempts >= MAX_RECONNECT_ATTEMPTS) {
                console.log('WebSocket重连次数过多，切换到HTTP流');
                switchToHttpStream();
                return;
            }
            
            const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
            const wsUrl = `${protocol}${window.location.hostname}:81`;
            
            console.log(`正在连接WebSocket摄像头: ${wsUrl} (尝试 ${connectionAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`);
            statusElement.innerText = `状态: 正在连接摄像头WebSocket... (${connectionAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`;
            
            try {
                wsCamera = new WebSocket(wsUrl);
                
                // 设置连接超时
                const connectionTimeout = setTimeout(() => {
                    if(wsCamera && wsCamera.readyState !== WebSocket.OPEN) {
                        console.log('WebSocket连接超时');
                        wsCamera.close();
                        connectionAttempts++;
                        retry();
                    }
                }, 5000); // 5秒超时
                
                wsCamera.onopen = function() {
                    clearTimeout(connectionTimeout);
                    console.log('摄像头WebSocket连接成功');
                    statusElement.innerText = "状态: 摄像头WebSocket已连接";
                    wsConnected = true;
                    connectionAttempts = 0; // 连接成功后重置计数
                    
                    // 请求开始视频流
                    wsCamera.send('requestStream');
                };
                
                wsCamera.onclose = function(event) {
                    clearTimeout(connectionTimeout);
                    console.log(`摄像头WebSocket连接关闭: 代码=${event.code}, 原因=${event.reason}`);
                    wsConnected = false;
                    
                    // 清除当前图像URL
                    if(window.lastImageUrl) {
                        URL.revokeObjectURL(window.lastImageUrl);
                        window.lastImageUrl = null;
                    }
                    
                    // 正常关闭不增加重试计数
                    if(event.code !== 1000 && event.code !== 1001) {
                        connectionAttempts++;
                    }
                    
                    retry();
                };
                
                wsCamera.onerror = function(error) {
                    clearTimeout(connectionTimeout);
                    console.error('摄像头WebSocket错误:', error);
                    // 错误事件后通常会触发关闭事件，所以在这里不增加计数
                };
                
                wsCamera.onmessage = function(event) {
                    // 如果收到二进制数据（摄像头帧）
                    if(event.data instanceof Blob) {
                        // 创建一个URL对象表示这个Blob
                        const imageUrl = URL.createObjectURL(event.data);
                        
                        // 更新图像元素
                        const imgElement = document.getElementById('img');
                        imgElement.onload = function() {
                            // 图像加载成功后，释放之前的URL对象
                            if(window.lastImageUrl) {
                                URL.revokeObjectURL(window.lastImageUrl);
                            }
                        };
                        
                        // 设置新图像
                        imgElement.src = imageUrl;
                        window.lastImageUrl = imageUrl;
                    }
                };
            } catch(error) {
                console.error('创建WebSocket连接失败:', error);
                statusElement.innerText = "状态: WebSocket连接失败";
                connectionAttempts++;
                retry();
            }
            
            function retry() {
                if(connectionAttempts >= MAX_RECONNECT_ATTEMPTS) {
                    console.log('WebSocket重连次数过多，切换到HTTP流');
                    switchToHttpStream();
                    return;
                }
                
                // 设置重连计时器
                const retryDelay = reconnectInterval * Math.min(connectionAttempts, 3); // 随着尝试次数增加延迟
                statusElement.innerText = `状态: 摄像头WebSocket断开，${retryDelay/1000}秒后重连...`;
                
                wsReconnectTimer = setTimeout(function() {
                    console.log(`尝试重新连接摄像头WebSocket... (${connectionAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`);
                    connectCameraWebSocket();
                }, retryDelay);
            }
        }
        
        // 切换到传统HTTP流作为备用方案
        function switchToHttpStream() {
            usingFallbackStream = true;
            statusElement.innerText = "状态: 使用HTTP流作为备用 (WebSocket连接失败)";
            
            const imgElement = document.getElementById('img');
            imgElement.onerror = function() {
                console.log('HTTP流加载失败，5秒后重试');
                setTimeout(function() {
                    // 添加时间戳防止缓存
                    imgElement.src = `/stream?t=${new Date().getTime()}`;
                }, 5000);
            };
            
            imgElement.onload = function() {
                statusElement.innerText = "状态: HTTP流连接成功 (备用模式)";
            };
            
            // 添加时间戳防止缓存
            imgElement.src = `/stream?t=${new Date().getTime()}`;
            
            // 定期刷新HTTP流，避免长时间未更新
            setInterval(function() {
                if(usingFallbackStream) {
                    imgElement.src = `/stream?t=${new Date().getTime()}`;
                }
            }, 30000); // 每30秒刷新一次
        }
        
        // 页面卸载时，关闭WebSocket连接
        window.addEventListener('beforeunload', function() {
            if(wsCamera && wsConnected) {
                // 请求停止视频流
                wsCamera.send('stopStream');
                wsCamera.close();
            }
        });
        
        // 添加网络在线/离线事件处理
        window.addEventListener('online', function() {
            console.log('网络已恢复连接，尝试重新连接WebSocket');
            if(!wsConnected && !usingFallbackStream) {
                connectionAttempts = 0; // 重置计数
                connectCameraWebSocket();
            }
        });
        
        window.addEventListener('offline', function() {
            console.log('网络连接已断开');
            statusElement.innerText = "状态: 网络连接已断开";
        });
        ///////////////////////////////////////////////////////////////
        // Support for Web Bluetooth
        // MicroBlocks UUIDs:
        const MICROBLOCKS_SERVICE_UUID = 'bb37a001-b922-4018-8e74-e14824b3a638'
        const MICROBLOCKS_RX_CHAR_UUID = 'bb37a002-b922-4018-8e74-e14824b3a638' // board receive characteristic
        const MICROBLOCKS_TX_CHAR_UUID = 'bb37a003-b922-4018-8e74-e14824b3a638' // board transmit characteristic

        const BLE_PACKET_LEN = 240; // Max BLE attribute length is 512 but 240 gives best performance
        function bleSerialBroadcastCmd(str) {
            function stringToUint8Array(str) {
                const encoder = new TextEncoder();
                return encoder.encode(str);
            }
            //字节命令数组各个元素
            // 251：0xfb Long message format 长消息
            // 27：0x1b Broadcast (OpCode: 0x1B, long message) 广播
            // stringToUint8Array(str)：将字符串转换成Uint8Array字节数组
            //254：0xfe 命令结束标志
            let length = str.length + 1;
            let bytesCmd = new Uint8Array([251, 27, 0, length % 256, parseInt(length / 256), ...stringToUint8Array(str), 254]);
            return bytesCmd;
        }

        class NimBLESerial {
            // Device to communicate over BLE using the Nordic Semiconductor UART service

            constructor() {
                this.device = undefined;
                this.service = undefined;
                this.rx_char = undefined;
                this.connected = false;
                this.sendInProgress = false;
                this.deviceName = "未知设备"; // 添加设备名称属性
            }

            handle_disconnected(event) {
                console.log("BLE disconnected");
                this.rx_char = undefined;
                this.connected = false;
                this.sendInProgress = false;
                this.deviceName = "未知设备"; // 重置设备名称
            }

            handle_read(event) {
                let data = new Uint8Array(event.target.value.buffer);
                // GP_serialInputBuffers.push(data);
            }

            async connect() {
                // Connect to a microBit
                this.device = await navigator.bluetooth.requestDevice({
                    filters: [{ services: [MICROBLOCKS_SERVICE_UUID] }]
                })
                // 保存设备名称
                this.deviceName = this.device.name || "未知设备";
                console.log(`连接到蓝牙设备: ${this.deviceName}`);
                
                this.device.addEventListener('gattserverdisconnected', this.handle_disconnected.bind(this));
                const server = await this.device.gatt.connect();
                this.service = await server.getPrimaryService(MICROBLOCKS_SERVICE_UUID);
                const tx_char = await this.service.getCharacteristic(MICROBLOCKS_TX_CHAR_UUID);
                this.rx_char = await this.service.getCharacteristic(MICROBLOCKS_RX_CHAR_UUID);
                await tx_char.startNotifications();
                // bind overrides the default this=tx_char to this=the NimBLESerial
                tx_char.addEventListener("characteristicvaluechanged", this.handle_read.bind(this));
                this.connected = true;
                this.sendInProgress = false;
                console.log("BLE connected");
            }

            disconnect() {
                if (this.device != undefined) {
                    this.device.gatt.disconnect();
                }
            }

            isConnected() {
                return this.connected;
            }

            write_data(data) {
                // Write the given data (a Uint8Array) and return the number of bytes written.
                // Detail: if not busy, start write_loop with as much data as we can send.

                if (this.rx_char == undefined) {
                    throw TypeError("Not connected");
                }
                if (this.sendInProgress || !this.connected) {
                    return 0;
                }
                let byteCount = (data.length > BLE_PACKET_LEN) ? BLE_PACKET_LEN : data.length;
                this.write_loop(data.subarray(0, byteCount));
                return byteCount;
            }

            async write_loop(data) {
                this.sendInProgress = true;
                while (true) {
                    // try to send the given data until success
                    try {
                        await this.rx_char.writeValueWithoutResponse(data);
                        this.sendInProgress = false;
                        return;
                    } catch (error) {
                        // print the error; give up if BLE has been disconnected
                        console.log("BLE write failed:\n ", error);
                        if (!this.isConnected()) {
                            this.sendInProgress = false;
                            return;
                        }
                    }
                }
            }

            getDeviceName() {
                return this.deviceName;
            }
        }

        const bleSerial = new NimBLESerial();
        ///////////////////////////////////////////////////////////////
        let net;
        let isRunning = false;
        const classifier = knnClassifier.create();
        const imgElement = document.getElementById('img');
        const statusElement = document.getElementById('status');
        const consoleElement = document.getElementById('console');
        const kValueInput = document.getElementById('k-value');
        const kValueDisplay = document.getElementById('k-value-display');
        const classes = ['go', 'left', 'right', 'stop'];
        
        // 存储上一次的预测结果，用于判断是否需要发送新命令
        let lastPredictedClass = null;

        // 样本计数器元素
        const sampleCounters = {
            0: document.getElementById('count-go'),
            1: document.getElementById('count-left'),
            2: document.getElementById('count-right'),
            3: document.getElementById('count-stop')
        };

        // 样本计数
        const sampleCounts = { 0: 0, 1: 0, 2: 0, 3: 0 };

        // 确保控制台可以显示HTML内容
        consoleElement.innerHTML = "等待模型加载...";

        // 更新样本计数显示
        function updateSampleCountDisplay() {
            for (let i = 0; i < 4; i++) {
                sampleCounters[i].textContent = sampleCounts[i];
            }
        }

        // 确保图像元素已加载
        imgElement.onload = function () {
            statusElement.innerText = "图像加载成功";
        };

        imgElement.onerror = function () {
            statusElement.innerText = "图像加载失败，请检查摄像头连接";
        };

        // 手动加载MobileNet模型
        async function loadMobileNetModel() {
            statusElement.innerText = "正在加载MobileNet模型...";
            try {
                // 加载MobileNet模型 - 使用TensorFlow.js的loadLayersModel
                // 使用预训练的MobileNet v1模型
                const modelUrl = 'https://storage.googleapis.com/tfjs-models/tfjs/mobilenet_v1_0.50_224/model.json';
                const mobilenet = await tf.loadLayersModel(modelUrl);

                // 打印模型结构摘要
                console.log("原始MobileNet模型结构摘要:");
                mobilenet.summary();

                // 打印模型层，帮助调试
                console.log("模型层详情:");
                mobilenet.layers.forEach((layer, i) => {
                    console.log(`${i}: ${layer.name} - 类型: ${layer.getClassName()} - 输入形状: ${JSON.stringify(layer.inputShape)} - 输出形状: ${JSON.stringify(layer.outputShape)}`);
                });

                // 检查是否存在global_average_pooling2d层
                let featureLayer;
                for (let i = mobilenet.layers.length - 1; i >= 0; i--) {
                    const layer = mobilenet.layers[i];
                    if (layer.name.includes('global_average_pooling2d') ||
                        layer.name.includes('global_pool') ||
                        layer.name.includes('avg_pool')) {
                        featureLayer = layer;
                        console.log(`找到特征层: ${layer.name}`);
                        break;
                    }

                    // 如果找不到pooling层，尝试找最后一个卷积层
                    if (!featureLayer && (i === mobilenet.layers.length - 2 ||
                        layer.name.includes('conv') ||
                        layer.getClassName().includes('Conv'))) {
                        featureLayer = layer;
                        console.log(`使用卷积层作为特征层: ${layer.name}`);
                    }
                }

                // 如果没有找到适合的层，使用倒数第二层
                if (!featureLayer && mobilenet.layers.length > 1) {
                    featureLayer = mobilenet.layers[mobilenet.layers.length - 2];
                    console.log(`使用倒数第二层作为特征层: ${featureLayer.name}`);
                }

                // 如果仍然没有找到适合的层，使用最后一层的输入
                if (!featureLayer) {
                    console.log("没有找到合适的特征层，使用模型作为特征提取器");
                    net = mobilenet;
              } else {
                    // 创建一个新模型，输出是特征提取层
                    net = tf.model({
                        inputs: mobilenet.inputs,
                        outputs: featureLayer.output
                    });
                    console.log("特征提取模型创建成功");
                }

                // 打印特征提取模型结构摘要
                console.log("特征提取模型结构摘要:");
                net.summary();

                statusElement.innerText = "MobileNet模型加载成功!";
                return net;
            } catch (error) {
                statusElement.innerText = "模型加载失败: " + error.message;
                console.error("模型加载错误:", error);
                throw error;
            }
        }

        // 使用加载的模型进行特征提取
        function extractFeatures(img, isTraining = false) {
            // 预处理图像
            const tensorImg = tf.browser.fromPixels(img);

            // 打印图像信息进行调试
            console.log(`图像尺寸: ${tensorImg.shape}`);

            // 调整大小为MobileNet输入大小 (224x224)
            const resized = tf.image.resizeBilinear(tensorImg, [224, 224]);

            // 归一化到[-1, 1]区间
            const normalized = resized.div(tf.scalar(127.5)).sub(tf.scalar(1));

            // 调整为批处理格式 [1, 224, 224, 3]
            const batched = normalized.expandDims(0);

            // 使用特征提取模型获取特征
            const features = net.predict(batched);
            
            // 打印特征信息以进行调试
            console.log(`特征形状: ${features.shape}`);
            
            // 给同一图像添加一些随机性，以区分不同时间的调用
            // 这可以帮助验证余弦相似度计算是否正常工作
            if (isTraining) {
                // 只在训练时添加小的随机扰动
                const noise = tf.randomNormal(features.shape, 0, 0.01);
                const noisyFeatures = features.add(noise);
                
                // 释放临时张量
                tensorImg.dispose();
                resized.dispose();
                normalized.dispose();
                batched.dispose();
                features.dispose();
                noise.dispose();
                
                return noisyFeatures;
            } else {
                // 释放临时张量
                tensorImg.dispose();
                resized.dispose();
                normalized.dispose();
                batched.dispose();
                
                return features;
            }
        }

        async function app() {
            try {
                // 加载MobileNet模型
                await loadMobileNetModel();
                consoleElement.innerText = "模型已准备就绪，请添加样本开始分类";

                // 开始分类
                startClassification();
            } catch (error) {
                statusElement.innerText = "应用启动失败: " + error.message;
                console.error("应用启动错误:", error);
            }
        }

        // 添加样本到分类器
        async function addExample(classId) {
            if (!net) {
                statusElement.innerText = "MobileNet模型尚未加载完成，请稍候";
            return;
          }
    
            try {
                // 获取图像特征
                const activation = extractFeatures(imgElement, true);

                // 添加到分类器
                classifier.addExample(activation, classId);

                // 释放特征张量
                activation.dispose();

                // 更新样本计数
                sampleCounts[classId]++;
                updateSampleCountDisplay();

                // 显示状态
                statusElement.innerText = `状态: 已添加样本到分类 ${classes[classId]} (总计: ${sampleCounts[classId]}个)`;

                // 如果之前没有运行，现在开始运行
                if (!isRunning) {
                    startClassification();
                }
            } catch (error) {
                statusElement.innerText = "状态: 添加样本失败: " + error.message;
                console.error("添加样本错误:", error);
            }
        }

        // 重置分类器
        function resetClassifier() {
            try {
                classifier.clearAllClasses();

                // 重置所有样本计数
                for (let i = 0; i < 4; i++) {
                    sampleCounts[i] = 0;
                }
                updateSampleCountDisplay();

                statusElement.innerText = "状态: 分类器已重置";
                consoleElement.innerText = "请添加新的样本进行分类";
            } catch (error) {
                statusElement.innerText = "状态: 重置分类器失败: " + error.message;
            }
        }

        // 添加K值更改监听器
        kValueInput.addEventListener('input', function () {
            const kValue = parseInt(this.value);
            kValueDisplay.textContent = kValue;
        });

        // 开始分类过程
        async function startClassification() {
            isRunning = true;

            while (isRunning) {
                try {
                    if (classifier.getNumClasses() > 0) {
                        // 获取图像特征
                        const activation = extractFeatures(imgElement, false);

                        // 获取当前K值
                        const kValue = parseInt(kValueInput.value);

                        // 访问内部数据并计算余弦相似度
                        const cosineDistances = [];
                        const dataset = classifier.getClassifierDataset();
                        for (const className in dataset) {
                            if (dataset.hasOwnProperty(className)) {
                                const classExamples = dataset[className];
                                // 获取该类别的所有样本
                                const numExamples = classExamples.shape[0];
                                
                                // 对每个样本计算余弦相似度
                                for (let i = 0; i < numExamples; i++) {
                                    try {
                                        // 提取当前样本
                                        const example = tf.slice(classExamples, [i, 0], [1, -1]);
                                        
                                        // 使用点积计算余弦相似度
                                        let similarity;
                                        try {
                                            // 将特征向量扁平化
                                            const exampleFlat = example.flatten();
                                            const activationFlat = activation.flatten();
                                            
                                            // 检查特征向量是否有足够的变化
                                            const exampleData = await exampleFlat.data();
                                            const activationData = await activationFlat.data();
                                            
                                            // 检查第一个和最后一个特征值，仅用于调试
                                            if (i === 0) {
                                                console.log(`样本${i} 特征[0]=${exampleData[0].toFixed(6)}, 特征[-1]=${exampleData[exampleData.length-1].toFixed(6)}`);
                                                console.log(`当前特征[0]=${activationData[0].toFixed(6)}, 特征[-1]=${activationData[activationData.length-1].toFixed(6)}`);
                                            }
                                            
                                            // 计算向量的L2范数
                                            const exampleNorm = tf.norm(exampleFlat);
                                            const activationNorm = tf.norm(activationFlat);
                                            
                                            // 计算点积
                                            const dotProduct = tf.sum(tf.mul(exampleFlat, activationFlat));
                                            
                                            // 计算余弦相似度: cosine = dot(A,B)/(||A||*||B||)
                                            const normProduct = tf.mul(exampleNorm, activationNorm);
                                            similarity = tf.div(dotProduct, tf.maximum(normProduct, 1e-9));
                                            
                                            // 获取相似度值并确保在有效范围内
                                            let simValue = await similarity.data();
                                            simValue = simValue[0];
                                            
                                            // 限制在-1到1范围内
                                            if (isNaN(simValue) || !isFinite(simValue)) {
                                                simValue = 0;
                                            }
                                            simValue = Math.max(-1, Math.min(1, simValue));
                                            
                                            // 清理临时张量
                                            exampleFlat.dispose();
                                            activationFlat.dispose();
                                            exampleNorm.dispose();
                                            activationNorm.dispose();
                                            dotProduct.dispose();
                                            normProduct.dispose();
                                            similarity.dispose();
                                            
                                            cosineDistances.push({
                                                class: parseInt(className),
                                                index: i,
                                                similarity: simValue
                                            });
                                        } catch (calcError) {
                                            console.error("计算余弦相似度出错:", calcError);
                                            cosineDistances.push({
                                                class: parseInt(className),
                                                index: i,
                                                similarity: 0
                                            });
                                        }
                                        
                                        // 清理样本张量
                                        example.dispose();
                                    } catch (error) {
                                        console.error("计算样本 " + i + " 的相似度时出错:", error);
                                        // 添加一个默认值以保持索引一致性
                                        cosineDistances.push({
                                            class: parseInt(className),
                                            index: i,
                                            similarity: 0
                                        });
                                    }
                                }
                            }
                        }
                        
                        // 排序余弦相似度（降序，因为越高表示越相似）
                        cosineDistances.sort((a, b) => b.similarity - a.similarity);
                        
                        // 记录相似度范围，帮助调试
                        let minSim = 1, maxSim = -1;
                        for (const item of cosineDistances) {
                            minSim = Math.min(minSim, item.similarity);
                            maxSim = Math.max(maxSim, item.similarity);
                        }
                        console.log(`相似度范围: 最小=${minSim.toFixed(4)}, 最大=${maxSim.toFixed(4)}`);
                        
                        // 取前K个
                        const topK = cosineDistances.slice(0, kValue);
                        
                        // 统计投票结果
                        const votes = {0: 0, 1: 0, 2: 0, 3: 0}; // 初始化所有类别的投票为0
                        for (const item of topK) {
                            votes[item.class]++;
                            console.log(`样本 ${item.index+1} (${classes[item.class]}) 相似度: ${item.similarity.toFixed(4)}`);
                        }
                        
                        // 打印投票信息
                        console.log(`投票结果: go=${votes[0]}, left=${votes[1]}, right=${votes[2]}, stop=${votes[3]}`);
                        
                        // 找出得票最多的类别
                        let maxVotes = 0;
                        let predictedClass = 0;
                        for (const className in votes) {
                            if (votes[className] > maxVotes) {
                                maxVotes = votes[className];
                                predictedClass = parseInt(className);
                            }
                        }
                        
                        // 如果存在票数相同的情况，使用相似度总和作为决胜
                        if (Object.values(votes).filter(v => v === maxVotes).length > 1) {
                            console.log("发现票数相同的类别，使用相似度总和决胜");
                            const simSums = {0: 0, 1: 0, 2: 0, 3: 0};
                            for (const item of topK) {
                                simSums[item.class] += item.similarity;
                            }
                            
                            let maxSimSum = -Infinity;
                            for (const className in votes) {
                                if (votes[className] === maxVotes && simSums[className] > maxSimSum) {
                                    maxSimSum = simSums[className];
                                    predictedClass = parseInt(className);
                                }
                            }
                            console.log(`使用相似度总和决胜: ${classes[predictedClass]} (总和=${maxSimSum.toFixed(4)})`);
                        }
                        
                        // 计算置信度（该类别得票数 / K）
                        const confidence = maxVotes / kValue;

                        // 如果蓝牙连接，发送分类结果
                        if (bleSerial.isConnected()) {
                            try {
                                // 发送当前预测的指令（每次循环都发送）
                                const directionCommand = classes[predictedClass]; // 获取预测的方向命令
                                const cmdData = bleSerialBroadcastCmd(directionCommand); // 编码命令
                                bleSerial.write_data(cmdData); // 发送给蓝牙设备
                                console.log(`发送指令到蓝牙设备: ${directionCommand}`);
                                
                                // 仍然更新上一次的预测类别（用于显示或记录目的）
                                lastPredictedClass = predictedClass;
                            } catch (error) {
                                console.error("发送蓝牙命令错误:", error);
                            }
                        }

                        // 构建详细的结果表格HTML
                        let tableHtml = '<table>';
                        tableHtml += '<tr><th>排名</th><th>类别</th><th>样本</th><th>相似度</th><th>原始值</th></tr>';
                        
                        for (let i = 0; i < Math.min(10, cosineDistances.length); i++) {
                            const item = cosineDistances[i];
                            const rowClass = i < kValue ? 'top-k' : '';
                            
                            // 将相似度值（-1到1）转换为百分比显示（0%到100%）
                            const similarityPercent = ((item.similarity + 1) / 2 * 100).toFixed(2);
                            
                            tableHtml += `<tr class="${rowClass}">
                                <td>${i+1}</td>
                                <td>${classes[item.class]}</td>
                                <td>${item.index+1}</td>
                                <td>${similarityPercent}%</td>
                                <td>${item.similarity.toFixed(4)}</td>
                            </tr>`;
                        }
                        
                        tableHtml += '</table>';

                        // 释放特征张量
                        activation.dispose();

                        // 构建HTML输出
                        let outputHtml = '';
                        outputHtml += `<div class="prediction-result">预测结果: <span class="predicted-class">${classes[predictedClass]}</span>, K值: <span class="k-value">${kValue}</span>, 概率: <span class="probability">${(confidence * 100).toFixed(2)}%</span></div>`;
                        outputHtml += `<div class="vote-result"><span class="vote-title">投票情况:</span> go: <span class="vote-value">${votes[0]}</span>, left: <span class="vote-value">${votes[1]}</span>, right: <span class="vote-value">${votes[2]}</span>, stop: <span class="vote-value">${votes[3]}</span></div>`;
                        
                        // 添加蓝牙状态信息
                        if (bleSerial.isConnected()) {
                            outputHtml += `<div style="margin-top:8px; color:#2196F3;">
                                蓝牙设备 "${bleSerial.getDeviceName()}" 已连接
                                <br>当前指令: <b>${classes[predictedClass]}</b> (持续发送中...)
                            </div>`;
                        } else {
                            outputHtml += `<div style="margin-top:8px; color:#E91E63;">
                                未连接蓝牙设备，无法发送指令
                            </div>`;
                        }
                        
                        // 添加相似度表格和说明
                        outputHtml += `<div style="font-size:0.9em; color:#666; margin-top:5px;">余弦相似度计算结果（前${Math.min(10, cosineDistances.length)}个）：</div>`;
                        outputHtml += tableHtml;
                        outputHtml += `<div style="font-size:0.9em; color:#666; margin-top:5px;">
                            深绿色背景表示参与投票的K个最近邻。<br>
                            相似度：0%（不相似）到100%（完全相同）<br>
                            原始值：余弦相似度的原始值，范围从-1到1
                        </div>`;

                        // 显示结果
                        // 首先检查innerHTML属性是否可用
                        try {
                            consoleElement.innerHTML = outputHtml;
                        } catch (e) {
                            // 如果innerHTML不可用，回退到文本
                            consoleElement.textContent = `预测结果: ${classes[predictedClass]}, 概率: ${(confidence * 100).toFixed(2)}% (持续发送中)`;
                            console.error("无法使用innerHTML:", e);
                        }
                    }
                } catch (error) {
                    console.error("分类错误:", error);
                }

                // 等待下一帧，避免浏览器挂起
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }

        // 事件监听器
        document.getElementById('class-go').addEventListener('click', () => addExample(0));
        document.getElementById('class-left').addEventListener('click', () => addExample(1));
        document.getElementById('class-right').addEventListener('click', () => addExample(2));
        document.getElementById('class-stop').addEventListener('click', () => addExample(3));
        document.getElementById('reset').addEventListener('click', resetClassifier);

        // 蓝牙连接与断开事件
        document.getElementById('connBle').addEventListener('click', async () => {
            try {
                statusElement.innerText = "状态: 正在连接蓝牙设备...";
                await bleSerial.connect();

                if (bleSerial.isConnected()) {
                    statusElement.innerText = `状态: 蓝牙设备"${bleSerial.getDeviceName()}"连接成功`;
                    document.getElementById('connBle').disabled = true;
                    document.getElementById('disconnBle').disabled = false;
                    
                    // 确保连接后立即发送当前指令
                    lastPredictedClass = null;
                } else {
                    statusElement.innerText = "状态: 蓝牙连接过程中断";
                }
            } catch (error) {
                console.error("蓝牙连接错误:", error);
                statusElement.innerText = "状态: 蓝牙连接失败 - " + (error.message || "未知错误");
            }
        });

        document.getElementById('disconnBle').addEventListener('click', () => {
            try {
                // 在断开前获取设备名称
                const deviceName = bleSerial.getDeviceName();
                
                // 发送停止命令
                if (bleSerial.isConnected()) {
                    try {
                        // 先发送停止命令确保小车停止
                        console.log("断开前发送停止命令...");
                        statusElement.innerText = `状态: 正在发送停止命令...`;
                        const stopCommand = "stop";
                        const cmdData = bleSerialBroadcastCmd(stopCommand);
                        bleSerial.write_data(cmdData);
                        
                        // 短暂延迟确保命令被执行
                        setTimeout(() => {
                            // 然后断开连接
                            bleSerial.disconnect();
                            statusElement.innerText = `状态: 蓝牙设备"${deviceName}"已断开连接`;
                            document.getElementById('connBle').disabled = false;
                            document.getElementById('disconnBle').disabled = true;
                            // 重置上一次预测结果
                            lastPredictedClass = null;
                        }, 100); // 100毫秒延迟
                    } catch (cmdError) {
                        console.error("发送停止命令失败:", cmdError);
                        // 即使发送停止命令失败，也尝试断开连接
                        bleSerial.disconnect();
                        statusElement.innerText = `状态: 蓝牙设备"${deviceName}"已断开连接 (停止命令发送失败)`;
                        document.getElementById('connBle').disabled = false;
                        document.getElementById('disconnBle').disabled = true;
                        lastPredictedClass = null;
                    }
                } else {
                    // 如果已经断开了，只更新UI状态
                    statusElement.innerText = `状态: 蓝牙设备"${deviceName}"已断开连接`;
                    document.getElementById('connBle').disabled = false;
                    document.getElementById('disconnBle').disabled = true;
                    lastPredictedClass = null;
                }
            } catch (error) {
                console.error("蓝牙断开连接错误:", error);
                statusElement.innerText = "状态: 蓝牙断开连接失败 - " + (error.message || "未知错误");
            }
        });

        // 启动应用
        app();
        
        // 建立WebSocket摄像头连接
        window.addEventListener('load', function() {
            setTimeout(connectCameraWebSocket, 1000); // 页面加载1秒后连接WebSocket
        });
        </script>
    </body>

</html>
