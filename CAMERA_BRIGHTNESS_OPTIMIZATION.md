# 📷 摄像头亮度优化指南

## 🎯 问题描述

摄像头画面偏暗是OV2640摄像头的常见问题，特别是在光线不足的环境下。我们已经进行了以下优化：

## 🔧 已实施的优化

### 1. 默认参数优化
- **亮度**: 从0提升到1
- **对比度**: 从0提升到1
- **曝光等级**: 从0提升到1
- **曝光值**: 从300提升到400
- **增益**: 从0提升到5
- **增益上限**: 从0提升到2

### 2. 自动功能启用
- ✅ 自动曝光控制 (AEC)
- ✅ 自动增益控制 (AGC)
- ✅ 自动白平衡 (AWB)
- ✅ 图像质量校正

## 🎮 实时调整方法

### 通过串口命令调整

#### 1. 查询当前参数
```
AT+CAM?
```

#### 2. 调整摄像头参数
```
AT+CAM=亮度,对比度,饱和度,曝光等级,增益
```

**参数范围**:
- 亮度: -2 到 2
- 对比度: -2 到 2
- 饱和度: -2 到 2
- 曝光等级: -2 到 2
- 增益: 0 到 30

#### 3. 常用调整示例

**提高亮度**:
```
AT+CAM=2,1,0,1,10
```

**增强对比度**:
```
AT+CAM=1,2,0,1,5
```

**提高增益**:
```
AT+CAM=1,1,0,1,15
```

**最大亮度设置**:
```
AT+CAM=2,2,1,2,30
```

## 🌟 推荐配置

### 室内光线充足
```
AT+CAM=1,1,0,1,5
```

### 室内光线一般
```
AT+CAM=1,1,0,1,10
```

### 室内光线较暗
```
AT+CAM=2,1,0,2,15
```

### 夜间/极暗环境
```
AT+CAM=2,2,1,2,25
```

## 🔍 参数说明

### 亮度 (Brightness)
- **作用**: 调整整体画面亮度
- **范围**: -2 到 2
- **推荐**: 1-2 (提高亮度)

### 对比度 (Contrast)
- **作用**: 增强明暗对比
- **范围**: -2 到 2
- **推荐**: 1-2 (增强对比度)

### 饱和度 (Saturation)
- **作用**: 调整色彩饱和度
- **范围**: -2 到 2
- **推荐**: 0-1 (保持适中)

### 曝光等级 (AE Level)
- **作用**: 调整自动曝光等级
- **范围**: -2 到 2
- **推荐**: 1-2 (提高曝光)

### 增益 (Gain)
- **作用**: 数字增益，提高感光度
- **范围**: 0 到 30
- **推荐**: 5-20 (根据光线调整)

## 🚨 注意事项

### 1. 增益过高的影响
- 可能产生噪点
- 影响图像质量
- 建议不超过25

### 2. 曝光过高的影响
- 可能过曝
- 失去细节
- 建议不超过2

### 3. 对比度过高的影响
- 可能丢失中间色调
- 建议不超过2

## 🎯 调试步骤

### 1. 基础调试
1. 连接串口监视器
2. 输入 `AT+CAM?` 查看当前参数
3. 根据环境光线选择合适的配置

### 2. 逐步调整
1. 先调整亮度到1-2
2. 再调整对比度到1-2
3. 最后调整增益到合适值

### 3. 效果验证
1. 观察Web界面中的画面
2. 检查是否还有偏暗问题
3. 确认图像质量是否可接受

## 📊 性能对比

| 配置 | 亮度 | 对比度 | 增益 | 适用环境 | 图像质量 |
|------|------|--------|------|----------|----------|
| 默认 | 0 | 0 | 0 | 强光 | 优秀 |
| 优化1 | 1 | 1 | 5 | 正常光 | 良好 |
| 优化2 | 1 | 1 | 10 | 弱光 | 良好 |
| 优化3 | 2 | 1 | 15 | 暗光 | 一般 |
| 最大 | 2 | 2 | 25 | 极暗 | 较差 |

## 🔧 高级技巧

### 1. 环境自适应
根据使用环境动态调整：
- 白天: 降低增益，提高对比度
- 夜晚: 提高增益，降低对比度

### 2. 应用场景优化
- **AI分类**: 优先保证清晰度
- **视频流**: 平衡亮度和帧率
- **拍照**: 优先保证图像质量

### 3. 温度控制
长时间高增益运行可能发热，建议：
- 定期重启设备
- 监控设备温度
- 避免长时间最大增益

## 📝 故障排除

### 画面仍然偏暗
1. 检查环境光线
2. 尝试最大亮度设置
3. 检查摄像头硬件连接
4. 重启设备

### 画面过曝
1. 降低亮度设置
2. 降低曝光等级
3. 降低增益值

### 画面有噪点
1. 降低增益值
2. 改善环境光线
3. 检查摄像头质量

---

**💡 提示**: 建议从默认优化配置开始，根据实际效果逐步调整参数。 