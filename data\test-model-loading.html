<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型文件加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.info { background-color: #e3f2fd; color: #1976d2; }
        .status.success { background-color: #e8f5e8; color: #2e7d32; }
        .status.warning { background-color: #fff3e0; color: #f57c00; }
        .status.error { background-color: #ffebee; color: #d32f2f; }
        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .file-item {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .file-item.success { background-color: #e8f5e8; border-color: #4caf50; }
        .file-item.error { background-color: #ffebee; border-color: #f44336; }
        .file-item.pending { background-color: #fff3e0; border-color: #ff9800; }
        button {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #1565c0; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #4caf50;
            transition: width 0.3s ease;
        }
        .log {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 MobileNet 模型文件加载测试</h1>
        
        <div class="status info" id="mainStatus">
            点击"开始测试"按钮开始检查模型文件加载情况
        </div>
        
        <div>
            <button onclick="startTest()" id="startBtn">开始测试</button>
            <button onclick="testProblemFiles()" id="problemBtn">测试问题文件</button>
            <button onclick="clearLog()" id="clearBtn">清空日志</button>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
        <div id="progressText">准备就绪</div>
        
        <h3>📁 文件加载状态</h3>
        <div class="file-list" id="fileList"></div>
        
        <h3>📝 详细日志</h3>
        <div class="log" id="logArea"></div>
    </div>

    <script>
        let testResults = {};
        let totalFiles = 0;
        let completedFiles = 0;

        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function updateProgress() {
            const progress = totalFiles > 0 ? (completedFiles / totalFiles) * 100 : 0;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('progressText').textContent = 
                `进度: ${completedFiles}/${totalFiles} (${progress.toFixed(1)}%)`;
        }

        function updateFileStatus(filename, status, details = '') {
            const fileList = document.getElementById('fileList');
            let fileItem = document.getElementById(`file-${filename.replace(/[^a-zA-Z0-9]/g, '_')}`);
            
            if (!fileItem) {
                fileItem = document.createElement('div');
                fileItem.className = 'file-item pending';
                fileItem.id = `file-${filename.replace(/[^a-zA-Z0-9]/g, '_')}`;
                fileList.appendChild(fileItem);
            }
            
            fileItem.className = `file-item ${status}`;
            fileItem.innerHTML = `
                <strong>${filename}</strong><br>
                状态: ${status}<br>
                ${details}
            `;
        }

        async function testSingleFile(url, delay = 0) {
            if (delay > 0) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
            
            const filename = url.split('/').pop();
            updateFileStatus(filename, 'pending', '正在测试...');
            log(`🔄 测试文件: ${filename}`);
            
            try {
                const startTime = Date.now();
                const response = await fetch(url, {
                    method: 'GET',
                    cache: 'no-cache'
                });
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (response.ok) {
                    const size = response.headers.get('content-length') || 'unknown';
                    const details = `✅ 成功 (${response.status}) - ${size} bytes - ${duration}ms`;
                    updateFileStatus(filename, 'success', details);
                    log(`✅ ${filename}: ${details}`);
                    testResults[filename] = { status: 'success', response: response.status, size, duration };
                } else {
                    const details = `❌ 失败 (${response.status}) - ${duration}ms`;
                    updateFileStatus(filename, 'error', details);
                    log(`❌ ${filename}: ${details}`);
                    testResults[filename] = { status: 'error', response: response.status, duration };
                }
            } catch (error) {
                const details = `💥 异常: ${error.message}`;
                updateFileStatus(filename, 'error', details);
                log(`💥 ${filename}: ${details}`);
                testResults[filename] = { status: 'error', error: error.message };
            }
            
            completedFiles++;
            updateProgress();
        }

        async function startTest() {
            document.getElementById('startBtn').disabled = true;
            document.getElementById('problemBtn').disabled = true;
            document.getElementById('mainStatus').textContent = '正在测试所有模型文件...';
            document.getElementById('mainStatus').className = 'status info';
            
            testResults = {};
            completedFiles = 0;
            
            // 生成所有模型文件URL
            const modelFiles = ['/models/mobilenet/model.json'];
            for (let i = 1; i <= 55; i++) {
                modelFiles.push(`/models/mobilenet/group${i}-shard1of1`);
            }
            
            totalFiles = modelFiles.length;
            updateProgress();
            
            log('🚀 开始测试所有模型文件...');
            log(`📊 总文件数: ${totalFiles}`);
            
            // 逐个测试文件，添加延迟
            for (let i = 0; i < modelFiles.length; i++) {
                await testSingleFile(modelFiles[i], i > 0 ? 200 : 0); // 200ms延迟
            }
            
            // 分析结果
            analyzeResults();
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('problemBtn').disabled = false;
        }

        async function testProblemFiles() {
            document.getElementById('startBtn').disabled = true;
            document.getElementById('problemBtn').disabled = true;
            document.getElementById('mainStatus').textContent = '正在测试问题文件 (group50-55)...';
            document.getElementById('mainStatus').className = 'status warning';
            
            testResults = {};
            completedFiles = 0;
            
            // 只测试问题文件
            const problemFiles = [];
            for (let i = 50; i <= 55; i++) {
                problemFiles.push(`/models/mobilenet/group${i}-shard1of1`);
            }
            
            totalFiles = problemFiles.length;
            updateProgress();
            
            log('🎯 专门测试问题文件 (group50-55)...');
            
            // 测试问题文件，使用更长延迟
            for (let i = 0; i < problemFiles.length; i++) {
                await testSingleFile(problemFiles[i], 500); // 500ms延迟
            }
            
            analyzeResults();
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('problemBtn').disabled = false;
        }

        function analyzeResults() {
            const successCount = Object.values(testResults).filter(r => r.status === 'success').length;
            const errorCount = Object.values(testResults).filter(r => r.status === 'error').length;
            
            log(`\n📊 测试结果统计:`);
            log(`✅ 成功: ${successCount}`);
            log(`❌ 失败: ${errorCount}`);
            log(`📈 成功率: ${((successCount / totalFiles) * 100).toFixed(1)}%`);
            
            if (errorCount === 0) {
                document.getElementById('mainStatus').textContent = '🎉 所有文件测试通过！';
                document.getElementById('mainStatus').className = 'status success';
            } else {
                document.getElementById('mainStatus').textContent = `⚠️ 发现 ${errorCount} 个文件加载失败`;
                document.getElementById('mainStatus').className = 'status error';
                
                log(`\n❌ 失败的文件:`);
                Object.entries(testResults).forEach(([filename, result]) => {
                    if (result.status === 'error') {
                        log(`  - ${filename}: ${result.error || result.response}`);
                    }
                });
            }
        }

        function clearLog() {
            document.getElementById('logArea').textContent = '';
            document.getElementById('fileList').innerHTML = '';
            testResults = {};
            completedFiles = 0;
            totalFiles = 0;
            updateProgress();
            document.getElementById('mainStatus').textContent = '日志已清空，可以重新开始测试';
            document.getElementById('mainStatus').className = 'status info';
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            log('🔧 模型文件加载测试工具已就绪');
            log('💡 这个工具可以帮助诊断 MobileNet 模型文件的加载问题');
            log('📝 建议先运行"开始测试"来检查所有文件');
        };
    </script>
</body>
</html>
