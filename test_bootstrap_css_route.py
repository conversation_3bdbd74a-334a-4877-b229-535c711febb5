#!/usr/bin/env python3
"""
测试 Bootstrap CSS 路由配置
验证 /css/bootstrap.min.css 路由是否正确添加
"""

import os
import re

def test_bootstrap_css_files():
    """测试 Bootstrap CSS 文件是否存在"""
    print("🔍 检查 Bootstrap CSS 文件...")
    
    files_to_check = [
        "data/css/bootstrap.min.css",
        "data/css/bootstrap.min.css.gz"
    ]
    
    print("📋 文件存在检查:")
    all_files_exist = True
    for file_path in files_to_check:
        exists = os.path.exists(file_path)
        status = "✅" if exists else "❌"
        print(f"  {status} {file_path}")
        if not exists:
            all_files_exist = False
    
    if all_files_exist:
        print("💡 服务器将优先使用压缩版本 bootstrap.min.css.gz")
    
    return all_files_exist

def test_server_route_definition():
    """测试服务器路由定义"""
    print("\n🔍 检查服务器路由定义...")
    
    if not os.path.exists("src/camera_server.cpp"):
        print("❌ 未找到 src/camera_server.cpp")
        return False
    
    with open("src/camera_server.cpp", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查路由定义
    checks = {
        "Bootstrap CSS 路由定义": 'bootstrap_css_uri' in content,
        "Bootstrap CSS URI": '"/css/bootstrap.min.css"' in content,
        "Bootstrap CSS 处理器": 'static_file_handler' in content and 'bootstrap_css_uri' in content
    }
    
    print("📋 路由定义检查:")
    all_good = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def test_server_route_registration():
    """测试服务器路由注册"""
    print("\n🔍 检查服务器路由注册...")
    
    if not os.path.exists("src/camera_server.cpp"):
        print("❌ 未找到 src/camera_server.cpp")
        return False
    
    with open("src/camera_server.cpp", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查路由注册
    registration_check = "httpd_register_uri_handler(camera_httpd, &bootstrap_css_uri)" in content
    
    print("📋 路由注册检查:")
    status = "✅" if registration_check else "❌"
    print(f"  {status} Bootstrap CSS 路由注册")
    
    return registration_check

def test_html_reference():
    """测试 HTML 文件中的引用"""
    print("\n🔍 检查 HTML 文件中的 Bootstrap CSS 引用...")
    
    if not os.path.exists("data/collecttrain.html"):
        print("❌ 未找到 data/collecttrain.html")
        return False
    
    with open("data/collecttrain.html", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查 Bootstrap CSS 引用
    bootstrap_ref = 'href="/css/bootstrap.min.css"' in content
    
    print("📋 HTML 引用检查:")
    status = "✅" if bootstrap_ref else "❌"
    print(f"  {status} collecttrain.html 中的 Bootstrap CSS 引用")
    
    if bootstrap_ref:
        print("💡 HTML 正确引用了 /css/bootstrap.min.css")
    
    return bootstrap_ref

def test_css_route_completeness():
    """测试 CSS 路由的完整性"""
    print("\n🔍 检查所有 CSS 路由...")
    
    if not os.path.exists("src/camera_server.cpp"):
        print("❌ 未找到 src/camera_server.cpp")
        return False
    
    with open("src/camera_server.cpp", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查所有 CSS 相关路由
    css_routes = {
        "style.css 路由": '"/css/style.css"' in content,
        "bootstrap.min.css 路由": '"/css/bootstrap.min.css"' in content
    }
    
    print("📋 所有 CSS 路由检查:")
    all_good = True
    for route_name, result in css_routes.items():
        status = "✅" if result else "❌"
        print(f"  {status} {route_name}")
        if not result:
            all_good = False
    
    return all_good

def generate_recommendations():
    """生成建议"""
    print("\n💡 Bootstrap CSS 加载机制说明:")
    print("1. HTML 引用: /css/bootstrap.min.css")
    print("2. 服务器检查: bootstrap.min.css.gz 是否存在")
    print("3. 存在压缩版本: 返回 .gz 文件 (节省带宽)")
    print("4. 不存在压缩版本: 返回未压缩文件")
    print("5. 浏览器自动解压并应用样式")
    
    print("\n🎯 当前配置优势:")
    print("- ✅ 自动选择最佳文件版本")
    print("- ✅ 节省带宽 (使用 gzip 压缩)")
    print("- ✅ 向后兼容 (未压缩版本备用)")
    print("- ✅ 对前端透明 (无需特殊处理)")

def main():
    """主测试函数"""
    print("🚀 Bootstrap CSS 路由配置测试")
    print("=" * 50)
    
    tests = [
        ("Bootstrap CSS 文件检查", test_bootstrap_css_files),
        ("服务器路由定义", test_server_route_definition),
        ("服务器路由注册", test_server_route_registration),
        ("HTML 引用检查", test_html_reference),
        ("CSS 路由完整性", test_css_route_completeness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            print(f"✅ {test_name} 测试通过")
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    # 生成建议
    generate_recommendations()
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 Bootstrap CSS 路由配置正确!")
        print("\n📝 下一步操作:")
        print("1. 编译并上传固件:")
        print("   pio run -e seeed_xiao_esp32s3_sense -t upload")
        print("2. 访问数据采集训练页面:")
        print("   http://[设备IP]/collecttrain.html")
        print("3. 验证 Bootstrap 样式正确加载")
        return True
    else:
        print("⚠️  发现问题，请根据测试结果进行修复")
        return False

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except Exception as e:
        print(f"💥 测试过程出错: {e}")
        exit(1)
