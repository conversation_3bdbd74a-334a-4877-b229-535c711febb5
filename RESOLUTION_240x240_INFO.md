# 240x240分辨率配置说明

## 🎯 为什么选择240x240？

### **1. AI分类优势**
- **正方形格式**: 与AI模型输入要求完美匹配
- **标准尺寸**: 接近常用的224x224 AI输入尺寸
- **无需裁剪**: 减少图像预处理步骤
- **特征保持**: 保持图像的长宽比，避免变形

### **2. 性能平衡**
- **适中分辨率**: 比QVGA(320x240)稍小，比QQVGA(160x120)更清晰
- **内存友好**: 240x240 = 57,600像素，内存占用合理
- **处理速度**: 比VGA(640x480)快4倍以上
- **传输效率**: 数据量适中，网络传输流畅

### **3. 视觉效果**
- **清晰度**: 足够的像素密度保证图像清晰
- **细节保留**: 能够识别小物体和细节特征
- **色彩还原**: RGB565格式下色彩表现良好

## 📊 分辨率对比

| 分辨率 | 像素数 | 内存占用 | AI适配性 | 处理速度 | 推荐用途 |
|--------|--------|----------|----------|----------|----------|
| 96x96 | 9,216 | 很小 | 差 | 很快 | 测试用 |
| 160x120 | 19,200 | 小 | 一般 | 快 | 简单检测 |
| **240x240** | **57,600** | **中等** | **优秀** | **中等** | **AI分类** |
| 320x240 | 76,800 | 中等 | 好 | 中等 | 通用应用 |
| 640x480 | 307,200 | 大 | 好 | 慢 | 高质量图像 |

## ⚙️ 当前配置详情

### **主配置** (PSRAM可用时)
```cpp
config.frame_size = FRAMESIZE_240X240;  // 240x240分辨率
config.jpeg_quality = 15;               // 中等质量
config.fb_count = 2;                    // 双缓冲
config.fb_location = CAMERA_FB_IN_PSRAM; // 使用PSRAM
```

### **备用配置** (无PSRAM时)
```cpp
config.frame_size = FRAMESIZE_240X240;  // 240x240分辨率
config.jpeg_quality = 20;               // 稍低质量
config.fb_count = 1;                    // 单缓冲
config.fb_location = CAMERA_FB_IN_DRAM; // 使用内部RAM
```

## 🔧 优化建议

### **如需更高帧率**
```cpp
config.jpeg_quality = 25;  // 降低质量
config.xclk_freq_hz = 24000000;  // 提高时钟频率
```

### **如需更高质量**
```cpp
config.jpeg_quality = 10;  // 提高质量
config.fb_count = 3;       // 三缓冲(需要PSRAM)
```

### **如需更小内存占用**
```cpp
config.frame_size = FRAMESIZE_QQVGA;  // 降到160x120
config.fb_count = 1;  // 单缓冲
```

## 📱 Web界面适配

HTML中的图像显示已经设置为224x224，与240x240分辨率很好匹配：

```html
<img id="img" width="224" height="224" />
```

图像会自动缩放到224x224，保持正方形比例，非常适合AI分类应用。

## 🎮 AI分类性能

- **MobileNet输入**: 224x224 (自动缩放)
- **特征提取**: 无变形，保持原始比例
- **分类精度**: 最佳平衡点
- **响应速度**: 实时分类能力

## 📈 预期效果

1. **更准确的AI分类**: 正方形格式减少预处理误差
2. **稳定的帧率**: 平衡的分辨率确保流畅性能
3. **清晰的图像**: 足够的像素密度保证视觉效果
4. **高效的传输**: 适中的数据量确保网络流畅 