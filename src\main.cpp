// ESP32-S3 XIAO Camera Web Server
// 适用于ESP32-S3 XIAO摄像头模块
// 基于测试成功的摄像头配置

#include "app_config.h"

// 全局变量定义
httpd_handle_t stream_httpd = NULL;
httpd_handle_t camera_httpd = NULL;

// 添加BLE ID相关变量
char BLE_ThreeLetterID[4] = {0};
char ap_ssid_buffer[32] = {0};  // 用于存储生成的SSID
const char* ap_ssid = NULL;  // 声明为全局变量

// 创建Preferences对象，用于读写NVS存储
Preferences preferences;

// WebSocket服务器和客户端
websockets::WebsocketsServer wsServer;
websockets::WebsocketsClient wsClient;
websockets::WebsocketsClient activeClient;
bool clientConnected = false;
int connectedClients = 0;
unsigned long lastPingTime = 0;

// ===== WiFi配置 =====
bool USE_AP_MODE = true;

// 修改接入点模式配置部分
// 接入点模式配置
const char* ap_ssid_prefix = "ESP32 AI Car";
const char* ap_password = "12345678";
IPAddress ap_ip(192, 168, 4, 1);
IPAddress ap_gateway(192, 168, 4, 1);
IPAddress ap_subnet(255, 255, 255, 0);

// 客户端模式配置
String sta_ssid = "UIOT";
String sta_password = "uiot12345";

// 通用配置
const char* hostname = "esp32";

// 串口命令缓冲区
String serialCommand = "";
bool commandComplete = false;

// mDNS功能标志
bool mdnsStarted = false;

// WebSocket帧发送标志和计时器
bool ws_send_frame = false;
unsigned long last_frame_time = 0;
const unsigned long frame_interval = 40;  // 设置为40ms以实现约25fps的帧率
bool ws_streaming = false;  // 添加流状态控制

// 添加内存监控和性能统计
struct WebSocketStats {
  unsigned long totalFramesSent = 0;
  unsigned long failedFrames = 0;
  unsigned long lastMemoryCheck = 0;
  size_t minFreeHeap = SIZE_MAX;
  unsigned long connectionDrops = 0;
} wsStats;

// 内存监控函数
void checkMemoryStatus() {
  size_t freeHeap = ESP.getFreeHeap();
  size_t freePsram = ESP.getFreePsram();
  
  if (freeHeap < wsStats.minFreeHeap) {
    wsStats.minFreeHeap = freeHeap;
  }
  
  if (millis() - wsStats.lastMemoryCheck > 10000) { // 每10秒检查一次
    Serial.printf("内存状态 - 堆内存: %d KB, PSRAM: %d KB, 最小堆内存: %d KB\n", 
                  freeHeap / 1024, freePsram / 1024, wsStats.minFreeHeap / 1024);
    Serial.printf("WebSocket统计 - 发送帧: %lu, 失败帧: %lu, 连接中断: %lu\n", 
                  wsStats.totalFramesSent, wsStats.failedFrames, wsStats.connectionDrops);
    wsStats.lastMemoryCheck = millis();
    
    // 内存不足警告
    if (freeHeap < 50000) { // 小于50KB时警告
      Serial.println("⚠️ 警告: 内存不足，可能影响WebSocket稳定性");
    }
  }
}

// 检查分区信息
void printPartitionInfo() {
  Serial.println("📊 分区信息:");

  // 获取当前运行的分区信息
  const esp_partition_t* running = esp_ota_get_running_partition();
  if (running) {
    Serial.printf("  🏃 运行分区: %s (类型: %d, 子类型: %d)\n",
                  running->label, running->type, running->subtype);
    Serial.printf("  📍 地址: 0x%x, 大小: %d KB\n",
                  running->address, running->size / 1024);
  }

  // 查找数据分区
  const esp_partition_t* data_partition = esp_partition_find_first(
    ESP_PARTITION_TYPE_DATA, ESP_PARTITION_SUBTYPE_DATA_SPIFFS, NULL);

  if (data_partition) {
    Serial.printf("  💾 数据分区: %s\n", data_partition->label);
    Serial.printf("  📍 地址: 0x%x, 大小: %d KB\n",
                  data_partition->address, data_partition->size / 1024);
  } else {
    Serial.println("  ❌ 未找到数据分区!");
    Serial.println("  💡 请检查 partitions.csv 配置");
  }
}

// 文件系统使用情况监控
void printFileSystemInfo() {
  Serial.println("💾 文件系统状态:");

  size_t total = LittleFS.totalBytes();
  size_t used = LittleFS.usedBytes();

  Serial.printf("  总容量: %d KB\n", total / 1024);
  Serial.printf("  已使用: %d KB\n", used / 1024);
  Serial.printf("  可用空间: %d KB\n", (total - used) / 1024);
  Serial.printf("  使用率: %.1f%%\n", (float)used / total * 100);

  if (total == 0) {
    Serial.println("  ❌ 错误: 文件系统容量为0");
    Serial.println("  💡 可能原因: 分区表配置错误或文件系统未格式化");
  } else if (used == 0) {
    Serial.println("  ⚠️  警告: 文件系统为空");
    Serial.println("  💡 请运行: pio run -e esp32s3_eye -t uploadfs");
  } else if (used > total * 0.8) {
    Serial.println("  ⚠️  警告: 文件系统使用率超过80%");
  }
}

// 测试文件可读性
void testFileReadability(const char* path) {
  Serial.printf("  测试文件: %s ... ", path);

  if (!LittleFS.exists(path)) {
    Serial.println("❌ 文件不存在");
    return;
  }

  File file = LittleFS.open(path, "r");
  if (!file) {
    Serial.println("❌ 无法打开文件");
    return;
  }

  size_t fileSize = file.size();
  if (fileSize == 0) {
    Serial.println("⚠️  文件为空");
    file.close();
    return;
  }

  // 读取前100个字符进行验证
  String preview = file.readString();
  file.close();

  if (preview.length() > 0) {
    Serial.printf("✅ 可读 (%d bytes)\n", fileSize);
    if (preview.length() > 50) {
      Serial.printf("    预览: %s...\n", preview.substring(0, 50).c_str());
    } else {
      Serial.printf("    内容: %s\n", preview.c_str());
    }
  } else {
    Serial.println("❌ 读取失败");
  }
}

// 文件系统初始化函数
bool initFileSystem() {
  Serial.println("=== 文件系统初始化开始 ===");

  // 首先检查分区信息
  printPartitionInfo();

  // 检查文件系统状态
  Serial.println("\n🔍 检查LittleFS状态...");

  if (!LittleFS.begin(true)) {
    Serial.println("❌ LittleFS挂载失败!");
    Serial.println("🔧 尝试格式化文件系统...");

    // 尝试格式化文件系统
    if (LittleFS.format()) {
      Serial.println("✅ 文件系统格式化成功");
      Serial.println("🔄 重新尝试挂载...");

      if (LittleFS.begin(true)) {
        Serial.println("✅ 文件系统挂载成功 (已格式化)");
        Serial.println("⚠️  注意: 文件系统已被格式化，需要重新上传文件");
        Serial.println("📝 请运行: pio run -e esp32s3_eye -t uploadfs");
      } else {
        Serial.println("❌ 格式化后仍无法挂载文件系统");
        Serial.println("🚨 可能的硬件问题:");
        Serial.println("  1. Flash 存储器损坏");
        Serial.println("  2. 分区表配置错误");
        Serial.println("  3. 电源不稳定");
        Serial.println("📖 请参考: FILESYSTEM_REPAIR_GUIDE.md");
        return false;
      }
    } else {
      Serial.println("❌ 文件系统格式化失败!");
      Serial.println("🚨 严重错误 - 可能的原因:");
      Serial.println("  1. Flash 硬件故障");
      Serial.println("  2. 分区表严重错误");
      Serial.println("  3. 固件损坏");
      Serial.println("🛠️  建议解决方案:");
      Serial.println("  1. 完全擦除 Flash: pio run -e esp32s3_eye -t erase");
      Serial.println("  2. 重新上传固件: pio run -e esp32s3_eye -t upload");
      Serial.println("  3. 检查硬件连接和电源");
      Serial.println("📖 详细指南: FILESYSTEM_REPAIR_GUIDE.md");
      return false;
    }
  }
  Serial.println("✅ LittleFS挂载成功");

  // 显示文件系统使用情况
  printFileSystemInfo();

  // 列出文件系统中的文件
  Serial.println("\n📁 扫描文件系统内容...");
  File root = LittleFS.open("/");
  if (!root) {
    Serial.println("❌ 无法打开根目录!");
    return false;
  }

  File file = root.openNextFile();
  size_t totalFileSize = 0;
  int fileCount = 0;

  Serial.println("文件列表:");
  while (file) {
    size_t fileSize = file.size();
    totalFileSize += fileSize;
    fileCount++;

    String fileName = file.name();
    Serial.printf("  📄 %s (%d bytes)\n", fileName.c_str(), fileSize);

    // 特别检查关键文件
    if (fileName == "/index.html") {
      Serial.println("    ✅ 找到主页文件!");
    } else if (fileName.endsWith(".html")) {
      Serial.printf("    📝 HTML文件: %s\n", fileName.c_str());
    } else if (fileName.endsWith(".js")) {
      Serial.printf("    📜 JavaScript文件: %s\n", fileName.c_str());
    } else if (fileName.endsWith(".css")) {
      Serial.printf("    🎨 CSS文件: %s\n", fileName.c_str());
    }

    file = root.openNextFile();
  }

  Serial.printf("\n📊 文件系统统计:\n");
  Serial.printf("  文件总数: %d\n", fileCount);
  Serial.printf("  文件总大小: %d bytes (%.1f KB)\n", totalFileSize, totalFileSize / 1024.0);

  if (fileCount == 0) {
    Serial.println("⚠️  文件系统为空!");
    Serial.println("请运行以下命令上传文件系统:");
    Serial.println("  pio run -e esp32s3_eye -t uploadfs");
    return false;
  }

  // 测试关键文件的可读性
  Serial.println("\n🔍 测试关键文件可读性...");
  testFileReadability("/index.html");
  testFileReadability("/css/style.css");
  testFileReadability("/js/app.js");

  Serial.println("=== 文件系统初始化完成 ===\n");
  return true;
}



// 从文件系统读取文件内容
String readFile(const char* path) {
  Serial.printf("📖 读取文件: %s\n", path);

  File file = LittleFS.open(path, "r");
  if (!file) {
    Serial.printf("❌ 无法打开文件: %s\n", path);
    return String();
  }

  size_t fileSize = file.size();
  Serial.printf("  文件大小: %d bytes\n", fileSize);

  if (fileSize == 0) {
    Serial.printf("⚠️  文件为空: %s\n", path);
    file.close();
    return String();
  }

  String content = file.readString();
  file.close();

  if (content.length() > 0) {
    Serial.printf("✅ 文件读取成功: %s (%d bytes)\n", path, content.length());
  } else {
    Serial.printf("❌ 文件读取失败: %s\n", path);
  }

  return content;
}

// 获取文件MIME类型
const char* getMimeType(const char* path) {
  if (strstr(path, ".html")) return "text/html";
  if (strstr(path, ".css")) return "text/css";
  if (strstr(path, ".js")) return "application/javascript";
  if (strstr(path, ".json")) return "application/json";
  if (strstr(path, ".bin")) return "application/octet-stream";
  if (strstr(path, ".png")) return "image/png";
  if (strstr(path, ".jpg") || strstr(path, ".jpeg")) return "image/jpeg";
  if (strstr(path, ".ico")) return "image/x-icon";
  return "text/plain";
}

// 404错误处理函数
esp_err_t error_404_handler(httpd_req_t *req, httpd_err_code_t err) {
  const char* not_found_html = 
    "<html><body><h1>404 Not Found</h1>"
    "<p>The requested file was not found.</p>"
    "<p><a href='/'>Return to Home</a></p>"
    "<p>Debug: Trying to serve static files from filesystem...</p></body></html>";
  
  httpd_resp_set_type(req, "text/html");
  httpd_resp_set_status(req, "404 Not Found");
  httpd_resp_send(req, not_found_html, strlen(not_found_html));
  return ESP_OK;
}

// 静态文件处理函数 - 优化版本
esp_err_t static_file_handler(httpd_req_t *req) {
  char filepath[128];
  char gzip_filepath[128];
  bool is_gzip = false;

  Serial.println("\n=== 静态文件请求处理 ===");

  // 构建文件路径
  if (strcmp(req->uri, "/") == 0) {
    strcpy(filepath, "/index.html");
    Serial.printf("🏠 根路径请求，映射到: %s\n", filepath);
  } else {
    strcpy(filepath, req->uri);
    Serial.printf("📄 直接文件请求: %s\n", filepath);
  }

  // 检查文件系统状态
  if (!LittleFS.begin()) {
    Serial.println("❌ 文件系统未挂载!");
    httpd_resp_send_404(req);
    return ESP_FAIL;
  }

  // 检查是否存在对应的gzip压缩文件
  snprintf(gzip_filepath, sizeof(gzip_filepath), "%s.gz", filepath);
  Serial.printf("🔍 检查压缩文件: %s\n", gzip_filepath);

  if (LittleFS.exists(gzip_filepath)) {
    Serial.printf("✅ 找到gzip压缩文件: %s\n", gzip_filepath);
    is_gzip = true;
  } else {
    Serial.printf("📝 压缩文件不存在，检查原始文件: %s\n", filepath);
    if (!LittleFS.exists(filepath)) {
      Serial.printf("❌ 文件不存在: %s\n", filepath);
      Serial.println("📋 当前文件系统内容:");

      // 列出当前文件系统内容用于调试
      File root = LittleFS.open("/");
      if (root) {
        File file = root.openNextFile();
        while (file) {
          Serial.printf("  📄 %s (%d bytes)\n", file.name(), file.size());
          file = root.openNextFile();
        }
      } else {
        Serial.println("  ❌ 无法打开根目录");
      }

      httpd_resp_send_404(req);
      return ESP_FAIL;
    } else {
      Serial.printf("✅ 找到原始文件: %s\n", filepath);
    }
  }
  
  // 打开文件（优先使用gzip压缩文件）
  const char* actual_filepath = is_gzip ? gzip_filepath : filepath;
  Serial.printf("📂 尝试打开文件: %s\n", actual_filepath);

  File file = LittleFS.open(actual_filepath, "r");
  if (!file) {
    Serial.printf("❌ 无法打开文件: %s\n", actual_filepath);
    Serial.println("可能的原因:");
    Serial.println("  1. 文件权限问题");
    Serial.println("  2. 文件系统损坏");
    Serial.println("  3. 内存不足");
    httpd_resp_send_404(req);
    return ESP_FAIL;
  }

  size_t file_size = file.size();
  Serial.printf("✅ 文件打开成功: %d bytes (%s)\n", file_size, is_gzip ? "gzip压缩" : "原始文件");

  if (file_size == 0) {
    Serial.printf("⚠️  警告: 文件为空: %s\n", actual_filepath);
  }
  
  // 设置响应头
  const char* mime_type = getMimeType(filepath); // 使用原始文件名获取MIME类型
  httpd_resp_set_type(req, mime_type);

  // 增强缓存策略：添加更多缓存控制头
  if (strstr(filepath, "/models/") != NULL) {
    // MobileNet模型文件：长期缓存（24小时）
    httpd_resp_set_hdr(req, "Cache-Control", "public, max-age=86400, immutable");
    httpd_resp_set_hdr(req, "ETag", "\"mobilenet-v1.0\"");
    httpd_resp_set_hdr(req, "Expires", "Thu, 31 Dec 2025 23:59:59 GMT");
    httpd_resp_set_hdr(req, "Last-Modified", "Mon, 01 Jan 2024 00:00:00 GMT");
    Serial.println("📦 设置模型文件长期缓存 (24小时)");
  } else if (strstr(filepath, ".js") != NULL || strstr(filepath, ".css") != NULL) {
    // JS/CSS文件：长期缓存（24小时）
    httpd_resp_set_hdr(req, "Cache-Control", "public, max-age=86400, immutable");
    httpd_resp_set_hdr(req, "ETag", "\"static-v1.0\"");
    httpd_resp_set_hdr(req, "Expires", "Thu, 31 Dec 2025 23:59:59 GMT");
    httpd_resp_set_hdr(req, "Last-Modified", "Mon, 01 Jan 2024 00:00:00 GMT");
    Serial.println("📦 设置JS/CSS文件长期缓存 (24小时)");
  } else if (strstr(filepath, ".html") != NULL) {
    // HTML文件：中期缓存（6小时）
    httpd_resp_set_hdr(req, "Cache-Control", "public, max-age=21600");
    httpd_resp_set_hdr(req, "ETag", "\"html-v1.0\"");
    Serial.println("📦 设置HTML文件中期缓存 (6小时)");
  } else {
    // 其他文件：默认缓存（1小时）
    httpd_resp_set_hdr(req, "Cache-Control", "public, max-age=3600");
    httpd_resp_set_hdr(req, "ETag", "\"default-v1.0\"");
    Serial.println("📦 设置默认缓存 (1小时)");
  }

  httpd_resp_set_hdr(req, "Connection", "close");  // 强制关闭连接以释放资源
  
  // 如果是gzip文件，设置Content-Encoding头
  if (is_gzip) {
    httpd_resp_set_hdr(req, "Content-Encoding", "gzip");
    Serial.println("设置Content-Encoding: gzip");
  }
  
  // 分块读取和发送文件（避免大文件占用过多内存）
  const size_t chunk_size = 1024;  // 1KB块
  uint8_t* buffer = (uint8_t*)malloc(chunk_size);
  if (!buffer) {
    Serial.println("内存分配失败");
    file.close();
    httpd_resp_send_500(req);
    return ESP_FAIL;
  }
  
  esp_err_t result = ESP_OK;
  size_t bytes_sent = 0;
  
  while (file.available() && result == ESP_OK) {
    size_t bytes_read = file.read(buffer, chunk_size);
    if (bytes_read > 0) {
      result = httpd_resp_send_chunk(req, (const char*)buffer, bytes_read);
      bytes_sent += bytes_read;
      
      // 给其他任务一些时间
      vTaskDelay(1);
    }
  }
  
  // 发送结束块
  if (result == ESP_OK) {
    result = httpd_resp_send_chunk(req, NULL, 0);
  }
  
  free(buffer);
  file.close();
  
  if (result == ESP_OK) {
    Serial.printf("成功发送文件: %s (%d bytes)\n", filepath, bytes_sent);
  } else {
    Serial.printf("文件发送失败: %s\n", filepath);
  }
  
  return result;
}

// 备用HTML页面内容（如果文件系统失败时使用）
const char FALLBACK_INDEX_HTML[] PROGMEM = R"rawliteral(
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>AI自动驾驶小车-KNN算法</title>
    <script src="https://fastly.jsdelivr.net/npm/@tensorflow/tfjs"></script>
    <script src="https://fastly.jsdelivr.net/npm/@tensorflow-models/knn-classifier"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 0;
            padding: 10px;
            max-width: 100%;
            box-sizing: border-box;
        }
        h3{
           margin: 0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 10px;
        }

        .image-container {
            margin: 10px auto;
            width: 224px;
            height: 224px;
            overflow: hidden;
        }

        .image-container img {
            width: 224px;
            height: 224px;
            object-fit: cover;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        #console {
            margin: 15px auto;
            padding: 10px;
            border: 1px solid #ddd;
            min-height: 40px;
            background-color: #fafafa;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            word-wrap: break-word;
            color: #333;
            text-align: left;
            font-weight: normal;
            overflow-x: auto;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        #console table {
            width: 100%;
            margin-top: 10px;
            border-collapse: collapse;
            font-size: 0.9em;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        
        #console th {
            background-color: #f0f0f0;
            font-weight: bold;
            padding: 6px 4px;
        }
        
        #console th, #console td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: center;
        }
        
        #console .top-k {
            background-color: #a5d6a7;
            border: 1px solid #4CAF50;
            font-weight: bold;
        }

        #console .prediction-result {
            font-size: 1.1em;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
            color: #e53935;
        }
        
        #console .vote-result {
            font-size: 0.95em;
            margin-bottom: 8px;
            color: #e53935;
        }
        
        #console .vote-value {
            font-weight: bold;
            color: #b71c1c;
        }

        button {
            margin: 5px 20px;
            padding: 5px 15px;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
        }

        button:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        button:active {
            transform: translateY(0);
        }

        #status {
            margin: 5px auto;
            color: #666;
            width: 90%;
            max-width: 600px;
            padding: 5px;
            font-weight: bold;
            background-color: #f0f8ff;
            border-radius: 5px;
            border: 1px dashed #ccc;
        }

        .sample-counts {
            margin: 5px 5px;
            display: flex;
            justify-content: center;
            flex-wrap: nowrap;
            gap: 5px;
            width: 90%;
            max-width: 600px;
        }

        .sample-counter {
            display: inline-block;
            padding: 5px 5px;
            border-radius: 15px;
            color: white;
            font-weight: bold;
            margin: 2px;
            font-size: 0.95em;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin: 10px auto;
            width: 90%;
            max-width: 600px;
        }
        
        .sample-button-group {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px dotted #eee;
        }
        
        .reset-button-group {
            margin-top: 15px;
        }
        
        .sample-label {
            font-weight: bold;
            margin-right: 10px;
            display: inline-block;
            margin-bottom: 8px;
        }
        
        .sample-buttons-container {
            width: 100%;
            text-align: center;
            padding: 5px;
        }
        
        .sample-buttons-row {
            display: flex;
            justify-content: center;
            gap: 25px;
            margin: 12px 0;
        }

        .counter-go { background-color: #2196F3; }
        .counter-left { background-color: #FF9800; }
        .counter-right { background-color: #9C27B0; }
        .counter-stop { background-color: #E91E63; }

        #class-go, #class-left, #class-right, #class-stop {
            margin: 5px 8px;
            padding: 12px 18px;
            font-size: 1em;
            min-width: 70px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #class-go { background-color: #2196F3; }
        #class-left { background-color: #FF9800; }
        #class-right { background-color: #9C27B0; }
        #class-stop { background-color: #E91E63; }

        .k-value-control {
            margin: 5px auto;
            width: 90%;
            max-width: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 8px 0 15px 0;
            border-bottom: 1px dotted #eee;
            background-color: #f9f9f9;
            border-radius: 8px;
        }

        .k-value-control label {
            font-weight: bold;
            color: #333;
            font-size: 1em;
        }

        .k-value-control input {
            width: 60%;
            max-width: 300px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            text-align: center;
            background-color: #fff;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .k-value-control span {
            font-weight: bold;
            color: #b71c1c;
            min-width: 30px;
            text-align: center;
            font-size: 1.2em;
            padding: 2px 8px;
            background-color: #ffebee;
            border-radius: 4px;
        }

        #reset, #connBle, #disconnBle {
            background-color: #0C7D40;
            padding: 12px 25px;
            font-weight: bold;
            color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        @media screen and (max-width: 600px) {
            h3 { font-size: 1.2em; }
            button { padding: 10px 16px; font-size: 1em; }
            #class-go, #class-left, #class-right, #class-stop {
                margin: 8px; padding: 15px 20px; min-width: 80px; font-size: 1.1em;
            }
            .sample-buttons-row { gap: 16px; }
            .sample-counter { padding: 4px 8px; font-size: 0.85em; margin: 1px; }
            .sample-counts { gap: 3px; }
            #console, #status { font-size: 0.9em; }
        }

        @media screen and (max-width: 400px) {
            button { margin: 5px; padding: 10px 15px; font-size: 0.9em; }
            #class-go, #class-left, #class-right, #class-stop {
                margin: 6px; padding: 14px 18px; min-width: 70px; font-size: 1em;
            }
            .button-group { width: 100%; }
            .sample-button-group { display: flex; flex-direction: column; width: 92%; }
            .sample-buttons-row { gap: 10px; }
            .sample-counter { padding: 3px 6px; font-size: 0.8em; margin: 1px; border-radius: 10px; }
            .sample-counts { gap: 2px; width: 98%; }
        }

        #console .predicted-class {
            font-weight: bold;
            font-size: 1.1em;
            color: #b71c1c;
            background-color: #ffebee;
            padding: 2px 6px;
            border-radius: 4px;
            margin: 0 2px;
        }
        
        #console .probability {
            font-weight: bold;
            color: #b71c1c;
        }

        #console .k-value {
            color: #b71c1c;
            font-weight: bold;
        }
        
        #console .vote-title {
            display: inline-block;
            font-weight: bold;
            margin-right: 8px;
        }
        </style>
    </head>

    <body>
    <div class="container">
        <h3>AI自动驾驶小车-KNN算法</h3>
        <div class="image-container">
            <img id="img" crossorigin alt="Camera Stream" width="224" height="224" />
              </div>
        <div class="sample-counts">
            <span class="sample-counter counter-go">go(<span id="count-go">0</span>)</span>
            <span class="sample-counter counter-left">left(<span id="count-left">0</span>)</span>
            <span class="sample-counter counter-right">right(<span id="count-right">0</span>)</span>
            <span class="sample-counter counter-stop">stop(<span id="count-stop">0</span>)</span>
            </div>
        <div id="status">状态: 加载模型中...</div>
        <div class="k-value-control">
            <label for="k-value">K值:</label>
            <input type="range" id="k-value" min="1" max="10" value="3" step="1">
            <span id="k-value-display">3</span>
                        </div>

        <div class="button-group sample-button-group">
            <div class="sample-buttons-container">                
                <div class="sample-buttons-row">
                    <button id="class-go">go</button>
                    <button id="class-left">left</button>
                    <button id="class-right">right</button>
                    <button id="class-stop">stop</button>
                        </div>
                
                        </div>                                              
                        </div>                    
        <div class="button-group reset-button-group">
            <button id="connBle" type="button">连接小车蓝牙</button>
            <button id="disconnBle" type="button" disabled="true">断开小车蓝牙</button>
                        </div>
        <div id="console">等待模型加载...</div>
        <div class="button-group reset-button-group">
            <button id="reset">重置分类器</button>
                        </div>
                        </div>
        
                 <script>
         ///////////////////////////////////////////////////////////////
         // WebSocket摄像头流支持 - 优化版本
         let wsCamera = null;
         let wsConnected = false;
         let reconnectInterval = 3000; // 降低重连间隔到3秒
         let wsReconnectTimer = null;
         let connectionAttempts = 0;
         const MAX_RECONNECT_ATTEMPTS = 10; // 增加最大重连次数
         let usingFallbackStream = false;
         let lastFrameTime = 0;
         let frameCount = 0;
         let connectionQuality = 'unknown';
         
         // 连接到WebSocket摄像头服务器
         function connectCameraWebSocket() {
             if(connectionAttempts >= MAX_RECONNECT_ATTEMPTS) {
                 console.log('WebSocket重连次数过多，切换到HTTP流');
                 switchToHttpStream();
                 return;
             }
             
             // 根据连接质量调整重连间隔
             let retryDelay = reconnectInterval;
             if (connectionQuality === 'poor') {
                 retryDelay = reconnectInterval * 1.5;
             }
             
             // 尝试重连
             wsReconnectTimer = setTimeout(function() {
                 console.log(`尝试重新连接摄像头WebSocket... (${connectionAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`);
                 connectCameraWebSocket();
             }, retryDelay);
         }
         
         // 切换到传统HTTP流作为备用方案
         function switchToHttpStream() {
             usingFallbackStream = true;
             statusElement.innerText = "状态: 使用HTTP流作为备用 (WebSocket连接失败)";
             
             const imgElement = document.getElementById('img');
             imgElement.onerror = function() {
                 console.log('HTTP流加载失败，5秒后重试');
                 setTimeout(function() {
                     // 添加时间戳防止缓存
                     imgElement.src = `/stream?t=${new Date().getTime()}`;
                 }, 5000);
             };
             
             imgElement.onload = function() {
                 statusElement.innerText = "状态: HTTP流连接成功 (备用模式)";
             };
             
             // 添加时间戳防止缓存
             imgElement.src = `/stream?t=${new Date().getTime()}`;
             
             // 定期刷新HTTP流，避免长时间未更新
             setInterval(function() {
                 if(usingFallbackStream) {
                     imgElement.src = `/stream?t=${new Date().getTime()}`;
                 }
             }, 30000); // 每30秒刷新一次
         }
         
         // 页面卸载时，关闭WebSocket连接
         window.addEventListener('beforeunload', function() {
             if(wsCamera && wsConnected) {
                 // 请求停止视频流
                 wsCamera.send('stopStream');
                 wsCamera.close();
             }
         });
         
         // 添加网络在线/离线事件处理
         window.addEventListener('online', function() {
             console.log('网络已恢复连接，尝试重新连接WebSocket');
             if(!wsConnected && !usingFallbackStream) {
                 connectionAttempts = 0; // 重置计数
                 connectCameraWebSocket();
             }
         });
         
         window.addEventListener('offline', function() {
             console.log('网络连接已断开');
             statusElement.innerText = "状态: 网络连接已断开";
         });
         ///////////////////////////////////////////////////////////////
         // Support for Web Bluetooth
         // MicroBlocks UUIDs:
         const MICROBLOCKS_SERVICE_UUID = 'bb37a001-b922-4018-8e74-e14824b3a638'
         const MICROBLOCKS_RX_CHAR_UUID = 'bb37a002-b922-4018-8e74-e14824b3a638' // board receive characteristic
         const MICROBLOCKS_TX_CHAR_UUID = 'bb37a003-b922-4018-8e74-e14824b3a638' // board transmit characteristic

         const BLE_PACKET_LEN = 240; // Max BLE attribute length is 512 but 240 gives best performance
         function bleSerialBroadcastCmd(str) {
             function stringToUint8Array(str) {
                 const encoder = new TextEncoder();
                 return encoder.encode(str);
             }
             //字节命令数组各个元素
             // 251：0xfb Long message format 长消息
             // 27：0x1b Broadcast (OpCode: 0x1B, long message) 广播
             // stringToUint8Array(str)：将字符串转换成Uint8Array字节数组
             //254：0xfe 命令结束标志
             let length = str.length + 1;
             let bytesCmd = new Uint8Array([251, 27, 0, length % 256, parseInt(length / 256), ...stringToUint8Array(str), 254]);
             return bytesCmd;
         }

         class NimBLESerial {
             // Device to communicate over BLE using the Nordic Semiconductor UART service

             constructor() {
                 this.device = undefined;
                 this.service = undefined;
                 this.rx_char = undefined;
                 this.connected = false;
                 this.sendInProgress = false;
                 this.deviceName = "未知设备"; // 添加设备名称属性
             }

             handle_disconnected(event) {
                 console.log("BLE disconnected");
                 this.rx_char = undefined;
                 this.connected = false;
                 this.sendInProgress = false;
                 this.deviceName = "未知设备"; // 重置设备名称
             }

             handle_read(event) {
                 let data = new Uint8Array(event.target.value.buffer);
                 // GP_serialInputBuffers.push(data);
             }

             async connect() {
                 // Connect to a microBit
                 this.device = await navigator.bluetooth.requestDevice({
                     filters: [{ services: [MICROBLOCKS_SERVICE_UUID] }]
                 })
                 // 保存设备名称
                 this.deviceName = this.device.name || "未知设备";
                 console.log(`连接到蓝牙设备: ${this.deviceName}`);
                 
                 this.device.addEventListener('gattserverdisconnected', this.handle_disconnected.bind(this));
                 const server = await this.device.gatt.connect();
                 this.service = await server.getPrimaryService(MICROBLOCKS_SERVICE_UUID);
                 const tx_char = await this.service.getCharacteristic(MICROBLOCKS_TX_CHAR_UUID);
                 this.rx_char = await this.service.getCharacteristic(MICROBLOCKS_RX_CHAR_UUID);
                 await tx_char.startNotifications();
                 // bind overrides the default this=tx_char to this=the NimBLESerial
                 tx_char.addEventListener("characteristicvaluechanged", this.handle_read.bind(this));
                 this.connected = true;
                 this.sendInProgress = false;
                 console.log("BLE connected");
             }

             disconnect() {
                 if (this.device != undefined) {
                     this.device.gatt.disconnect();
                 }
             }

             isConnected() {
                 return this.connected;
             }

             write_data(data) {
                 // Write the given data (a Uint8Array) and return the number of bytes written.
                 // Detail: if not busy, start write_loop with as much data as we can send.

                 if (this.rx_char == undefined) {
                     throw TypeError("Not connected");
                 }
                 if (this.sendInProgress || !this.connected) {
                     return 0;
                 }
                 let byteCount = (data.length > BLE_PACKET_LEN) ? BLE_PACKET_LEN : data.length;
                 this.write_loop(data.subarray(0, byteCount));
                 return byteCount;
             }

             async write_loop(data) {
                 this.sendInProgress = true;
                 while (true) {
                     // try to send the given data until success
                     try {
                         await this.rx_char.writeValueWithoutResponse(data);
                         this.sendInProgress = false;
                         return;
                     } catch (error) {
                         // print the error; give up if BLE has been disconnected
                         console.log("BLE write failed:\n ", error);
                         if (!this.isConnected()) {
                             this.sendInProgress = false;
                             return;
                         }
                     }
                 }
             }

             getDeviceName() {
                 return this.deviceName;
             }
         }

         const bleSerial = new NimBLESerial();
         ///////////////////////////////////////////////////////////////
         let net;
         let isRunning = false;
         const classifier = knnClassifier.create();
         const imgElement = document.getElementById('img');
         const statusElement = document.getElementById('status');
         const consoleElement = document.getElementById('console');
         const kValueInput = document.getElementById('k-value');
         const kValueDisplay = document.getElementById('k-value-display');
         const classes = ['go', 'left', 'right', 'stop'];
         
         // 存储上一次的预测结果，用于判断是否需要发送新命令
         let lastPredictedClass = null;

         // 样本计数器元素
         const sampleCounters = {
             0: document.getElementById('count-go'),
             1: document.getElementById('count-left'),
             2: document.getElementById('count-right'),
             3: document.getElementById('count-stop')
         };

         // 样本计数
         const sampleCounts = { 0: 0, 1: 0, 2: 0, 3: 0 };

         // 确保控制台可以显示HTML内容
         consoleElement.innerHTML = "等待模型加载...";

         // 更新样本计数显示
         function updateSampleCountDisplay() {
             for (let i = 0; i < 4; i++) {
                 sampleCounters[i].textContent = sampleCounts[i];
             }
         }

         // 确保图像元素已加载
         imgElement.onload = function () {
             statusElement.innerText = "图像加载成功";
         };

         imgElement.onerror = function () {
             statusElement.innerText = "图像加载失败，请检查摄像头连接";
         };

         // 手动加载MobileNet模型
         async function loadMobileNetModel() {
             statusElement.innerText = "正在加载MobileNet模型...";
             try {
                 // 加载MobileNet模型 - 使用TensorFlow.js的loadLayersModel
                 // 使用预训练的MobileNet v1模型
                 const modelUrl = 'https://storage.googleapis.com/tfjs-models/tfjs/mobilenet_v1_0.50_224/model.json';
                 const mobilenet = await tf.loadLayersModel(modelUrl);

                 // 打印模型结构摘要
                 console.log("原始MobileNet模型结构摘要:");
                 mobilenet.summary();

                 // 打印模型层，帮助调试
                 console.log("模型层详情:");
                 mobilenet.layers.forEach((layer, i) => {
                     console.log(`${i}: ${layer.name} - 类型: ${layer.getClassName()} - 输入形状: ${JSON.stringify(layer.inputShape)} - 输出形状: ${JSON.stringify(layer.outputShape)}`);
                 });

                 // 检查是否存在global_average_pooling2d层
                 let featureLayer;
                 for (let i = mobilenet.layers.length - 1; i >= 0; i--) {
                     const layer = mobilenet.layers[i];
                     if (layer.name.includes('global_average_pooling2d') ||
                         layer.name.includes('global_pool') ||
                         layer.name.includes('avg_pool')) {
                         featureLayer = layer;
                         console.log(`找到特征层: ${layer.name}`);
                         break;
                     }

                     // 如果找不到pooling层，尝试找最后一个卷积层
                     if (!featureLayer && (i === mobilenet.layers.length - 2 ||
                         layer.name.includes('conv') ||
                         layer.getClassName().includes('Conv'))) {
                         featureLayer = layer;
                         console.log(`使用卷积层作为特征层: ${layer.name}`);
                     }
                 }

                 // 如果没有找到适合的层，使用倒数第二层
                 if (!featureLayer && mobilenet.layers.length > 1) {
                     featureLayer = mobilenet.layers[mobilenet.layers.length - 2];
                     console.log(`使用倒数第二层作为特征层: ${featureLayer.name}`);
                 }

                 // 如果仍然没有找到适合的层，使用最后一层的输入
                 if (!featureLayer) {
                     console.log("没有找到合适的特征层，使用模型作为特征提取器");
                     net = mobilenet;
               } else {
                     // 创建一个新模型，输出是特征提取层
                     net = tf.model({
                         inputs: mobilenet.inputs,
                         outputs: featureLayer.output
                     });
                     console.log("特征提取模型创建成功");
                 }

                 // 打印特征提取模型结构摘要
                 console.log("特征提取模型结构摘要:");
                 net.summary();

                 statusElement.innerText = "MobileNet模型加载成功!";
                 return net;
             } catch (error) {
                 statusElement.innerText = "模型加载失败: " + error.message;
                 console.error("模型加载错误:", error);
                 throw error;
             }
         }

         // 使用加载的模型进行特征提取
         function extractFeatures(img, isTraining = false) {
             // 预处理图像
             const tensorImg = tf.browser.fromPixels(img);

             // 打印图像信息进行调试
             console.log(`图像尺寸: ${tensorImg.shape}`);

             // 调整大小为MobileNet输入大小 (224x224)
             const resized = tf.image.resizeBilinear(tensorImg, [224, 224]);

             // 归一化到[-1, 1]区间
             const normalized = resized.div(tf.scalar(127.5)).sub(tf.scalar(1));

             // 调整为批处理格式 [1, 224, 224, 3]
             const batched = normalized.expandDims(0);

             // 使用特征提取模型获取特征
             const features = net.predict(batched);
             
             // 打印特征信息以进行调试
             console.log(`特征形状: ${features.shape}`);
             
             // 给同一图像添加一些随机性，以区分不同时间的调用
             // 这可以帮助验证余弦相似度计算是否正常工作
             if (isTraining) {
                 // 只在训练时添加小的随机扰动
                 const noise = tf.randomNormal(features.shape, 0, 0.01);
                 const noisyFeatures = features.add(noise);
                 
                 // 释放临时张量
                 tensorImg.dispose();
                 resized.dispose();
                 normalized.dispose();
                 batched.dispose();
                 features.dispose();
                 noise.dispose();
                 
                 return noisyFeatures;
             } else {
                 // 释放临时张量
                 tensorImg.dispose();
                 resized.dispose();
                 normalized.dispose();
                 batched.dispose();
                 
                 return features;
             }
         }

         async function app() {
             try {
                 // 加载MobileNet模型
                 await loadMobileNetModel();
                 consoleElement.innerText = "模型已准备就绪，请添加样本开始分类";

                 // 开始分类
                 startClassification();
             } catch (error) {
                 statusElement.innerText = "应用启动失败: " + error.message;
                 console.error("应用启动错误:", error);
             }
         }

         // 添加样本到分类器
         async function addExample(classId) {
             if (!net) {
                 statusElement.innerText = "MobileNet模型尚未加载完成，请稍候";
             return;
           }
     
             try {
                 // 获取图像特征
                 const activation = extractFeatures(imgElement, true);

                 // 添加到分类器
                 classifier.addExample(activation, classId);

                 // 释放特征张量
                 activation.dispose();

                 // 更新样本计数
                 sampleCounts[classId]++;
                 updateSampleCountDisplay();

                 // 显示状态
                 statusElement.innerText = `状态: 已添加样本到分类 ${classes[classId]} (总计: ${sampleCounts[classId]}个)`;

                 // 如果之前没有运行，现在开始运行
                 if (!isRunning) {
                     startClassification();
                 }
             } catch (error) {
                 statusElement.innerText = "状态: 添加样本失败: " + error.message;
                 console.error("添加样本错误:", error);
             }
         }

         // 重置分类器
         function resetClassifier() {
             try {
                 classifier.clearAllClasses();

                 // 重置所有样本计数
                 for (let i = 0; i < 4; i++) {
                     sampleCounts[i] = 0;
                 }
                 updateSampleCountDisplay();

                 statusElement.innerText = "状态: 分类器已重置";
                 consoleElement.innerText = "请添加新的样本进行分类";
             } catch (error) {
                 statusElement.innerText = "状态: 重置分类器失败: " + error.message;
             }
         }

         // 添加K值更改监听器
         kValueInput.addEventListener('input', function () {
             const kValue = parseInt(this.value);
             kValueDisplay.textContent = kValue;
         });

         // 开始分类过程（简化版，避免过度复杂）
         async function startClassification() {
             isRunning = true;

             while (isRunning) {
                 try {
                     if (classifier.getNumClasses() > 0) {
                         // 获取图像特征
                         const activation = extractFeatures(imgElement, false);

                         // 获取当前K值
                         const kValue = parseInt(kValueInput.value);

                         // 使用KNN分类器进行预测
                         const result = await classifier.predictClass(activation, kValue);
                         
                         if (result.label !== undefined) {
                             const predictedClass = parseInt(result.label);
                             const confidence = result.confidences[result.label];

                             // 如果蓝牙连接，发送分类结果
                             if (bleSerial.isConnected()) {
                                 try {
                                     const directionCommand = classes[predictedClass];
                                     const cmdData = bleSerialBroadcastCmd(directionCommand);
                                     bleSerial.write_data(cmdData);
                                     console.log(`发送指令到蓝牙设备: ${directionCommand}`);
                                     lastPredictedClass = predictedClass;
                                 } catch (error) {
                                     console.error("发送蓝牙命令错误:", error);
                                 }
                             }

                             // 构建HTML输出
                             let outputHtml = '';
                             outputHtml += `<div class="prediction-result">预测结果: <span class="predicted-class">${classes[predictedClass]}</span>, K值: <span class="k-value">${kValue}</span>, 概率: <span class="probability">${(confidence * 100).toFixed(2)}%</span></div>`;
                             
                             // 添加蓝牙状态信息
                             if (bleSerial.isConnected()) {
                                 outputHtml += `<div style="margin-top:8px; color:#2196F3;">
                                     蓝牙设备 "${bleSerial.getDeviceName()}" 已连接
                                     <br>当前指令: <b>${classes[predictedClass]}</b> (持续发送中...)
                                 </div>`;
                             } else {
                                 outputHtml += `<div style="margin-top:8px; color:#E91E63;">
                                     未连接蓝牙设备，无法发送指令
                                 </div>`;
                             }

                             // 显示结果
                             try {
                                 consoleElement.innerHTML = outputHtml;
                             } catch (e) {
                                 consoleElement.textContent = `预测结果: ${classes[predictedClass]}, 概率: ${(confidence * 100).toFixed(2)}%`;
                                 console.error("无法使用innerHTML:", e);
                             }
                         }

                         // 释放特征张量
                         activation.dispose();
                     }
                 } catch (error) {
                     console.error("分类错误:", error);
                 }

                 // 等待下一帧，避免浏览器挂起
                 await new Promise(resolve => setTimeout(resolve, 200));
             }
         }

         // 事件监听器
         document.getElementById('class-go').addEventListener('click', () => addExample(0));
         document.getElementById('class-left').addEventListener('click', () => addExample(1));
         document.getElementById('class-right').addEventListener('click', () => addExample(2));
         document.getElementById('class-stop').addEventListener('click', () => addExample(3));
         document.getElementById('reset').addEventListener('click', resetClassifier);

         // 蓝牙连接与断开事件
         document.getElementById('connBle').addEventListener('click', async () => {
             try {
                 statusElement.innerText = "状态: 正在连接蓝牙设备...";
                 await bleSerial.connect();

                 if (bleSerial.isConnected()) {
                     statusElement.innerText = `状态: 蓝牙设备"${bleSerial.getDeviceName()}"连接成功`;
                     document.getElementById('connBle').disabled = true;
                     document.getElementById('disconnBle').disabled = false;
                     
                     // 确保连接后立即发送当前指令
                     lastPredictedClass = null;
                 } else {
                     statusElement.innerText = "状态: 蓝牙连接过程中断";
                 }
             } catch (error) {
                 console.error("蓝牙连接错误:", error);
                 statusElement.innerText = "状态: 蓝牙连接失败 - " + (error.message || "未知错误");
             }
         });

         document.getElementById('disconnBle').addEventListener('click', () => {
             try {
                 // 在断开前获取设备名称
                 const deviceName = bleSerial.getDeviceName();
                 
                 // 发送停止命令
                 if (bleSerial.isConnected()) {
                     try {
                         // 先发送停止命令确保小车停止
                         console.log("断开前发送停止命令...");
                         statusElement.innerText = `状态: 正在发送停止命令...`;
                         const stopCommand = "stop";
                         const cmdData = bleSerialBroadcastCmd(stopCommand);
                         bleSerial.write_data(cmdData);
                         
                         // 短暂延迟确保命令被执行
                         setTimeout(() => {
                             // 然后断开连接
                             bleSerial.disconnect();
                             statusElement.innerText = `状态: 蓝牙设备"${deviceName}"已断开连接`;
                             document.getElementById('connBle').disabled = false;
                             document.getElementById('disconnBle').disabled = true;
                             // 重置上一次预测结果
                             lastPredictedClass = null;
                         }, 100); // 100毫秒延迟
                     } catch (cmdError) {
                         console.error("发送停止命令失败:", cmdError);
                         // 即使发送停止命令失败，也尝试断开连接
                         bleSerial.disconnect();
                         statusElement.innerText = `状态: 蓝牙设备"${deviceName}"已断开连接 (停止命令发送失败)`;
                         document.getElementById('connBle').disabled = false;
                         document.getElementById('disconnBle').disabled = true;
                         lastPredictedClass = null;
                     }
                 } else {
                     // 如果已经断开了，只更新UI状态
                     statusElement.innerText = `状态: 蓝牙设备"${deviceName}"已断开连接`;
                     document.getElementById('connBle').disabled = false;
                     document.getElementById('disconnBle').disabled = true;
                     lastPredictedClass = null;
                 }
             } catch (error) {
                 console.error("蓝牙断开连接错误:", error);
                 statusElement.innerText = "状态: 蓝牙断开连接失败 - " + (error.message || "未知错误");
             }
         });

         // 启动应用
         app();
         
         // 建立WebSocket摄像头连接
         window.addEventListener('load', function() {
             setTimeout(connectCameraWebSocket, 1000); // 页面加载1秒后连接WebSocket
         });

         // 监控连接质量
         function monitorConnectionQuality() {
             if (now - lastFrameTime > 2000) {
                 const fps = frameCount / ((now - lastFrameTime) / 1000);
                 
                 // 根据帧率评估连接质量
                 if (fps > 20) {
                     connectionQuality = 'excellent';
                 } else if (fps > 15) {
                     connectionQuality = 'good';
                 } else if (fps > 10) {
                     connectionQuality = 'fair';
                 } else {
                     connectionQuality = 'poor';
                 }
                 
                 // 根据质量调整参数
                 adjustParametersBasedOnQuality();
             }
         }
         </script>
</body>

</html>
)rawliteral";

// 初始化mDNS服务
bool setupMDNS() {
  if (MDNS.begin(hostname)) {
    Serial.println("mDNS响应器已启动");
    Serial.printf("您可以通过 http://%s.local 访问此设备\n", hostname);
    MDNS.addService("http", "tcp", 80);
    return true;
  } else {
    Serial.println("mDNS响应器启动失败");
    return false;
  }
}

// 处理串口命令
void processSerialCommand() {
  serialCommand.trim();
  if (serialCommand.length() == 0) {
    return;
  }

  Serial.println("收到命令: " + serialCommand);

  // 处理查询当前WiFi设置
  if (serialCommand.startsWith("AT+CWJAP?")) {
    if (USE_AP_MODE) {
      Serial.println("当前模式: AP模式");
      Serial.println("AP SSID: " + String(ap_ssid));
      Serial.println("AP PASSWORD: " + String(ap_password));
    } else {
      Serial.println("当前模式: 客户端模式");
      Serial.println("WiFi SSID: " + sta_ssid);
      Serial.println("WiFi PASSWORD: " + sta_password);
    }
    Serial.println("OK");
    return;
  }

  // 处理设置WiFi名称和密码
  if (serialCommand.startsWith("AT+CWJAP=")) {
    int firstQuote = serialCommand.indexOf('"');
    int secondQuote = serialCommand.indexOf('"', firstQuote + 1);
    int thirdQuote = serialCommand.indexOf('"', secondQuote + 1);
    int fourthQuote = serialCommand.indexOf('"', thirdQuote + 1);

    if (firstQuote == -1 || secondQuote == -1 || thirdQuote == -1 || fourthQuote == -1) {
      Serial.println("ERROR: 格式错误，正确格式为: AT+CWJAP=\"SSID\",\"PASSWORD\"");
      return;
    }

    String newSSID = serialCommand.substring(firstQuote + 1, secondQuote);
    String newPassword = serialCommand.substring(thirdQuote + 1, fourthQuote);

    if (newSSID.length() == 0) {
      Serial.println("ERROR: SSID不能为空");
      return;
    }

    preferences.begin("wifi", false);
    preferences.putString("ssid", newSSID);
    preferences.putString("password", newPassword);
    preferences.end();

    sta_ssid = newSSID;
    sta_password = newPassword;

    Serial.println("WiFi设置已保存");
    Serial.println("SSID: " + newSSID);
    Serial.println("PASSWORD: " + newPassword);
    Serial.println("重启ESP32后生效");
    Serial.println("OK");
    return;
  }

  // 处理设置模式命令
  if (serialCommand.startsWith("AT+CWMODE=")) {
    String modeStr = serialCommand.substring(10);
    modeStr.trim();

    int mode = modeStr.toInt();
    if (mode == 1) {
      preferences.begin("wifi", false);
      preferences.putBool("ap_mode", false);
      preferences.end();
      USE_AP_MODE = false;
      Serial.println("已设置为客户端模式，重启后生效");
      Serial.println("OK");
    } else if (mode == 2) {
      preferences.begin("wifi", false);
      preferences.putBool("ap_mode", true);
      preferences.end();
      USE_AP_MODE = true;
      Serial.println("已设置为AP模式，重启后生效");
      Serial.println("OK");
    } else {
      Serial.println("ERROR: 不支持的模式，使用1(客户端模式)或2(AP模式)");
    }
    return;
  }

  // 处理重启命令
  if (serialCommand.equals("AT+RST")) {
    Serial.println("重启ESP32...");
    Serial.println("OK");
    delay(1000);
    ESP.restart();
    return;
  }

  // 处理扫描WiFi命令
  if (serialCommand.equals("AT+CWLAP")) {
    Serial.println("扫描WiFi网络...");
    WiFi.mode(WIFI_STA);
    int n = WiFi.scanNetworks();

    if (n == 0) {
      Serial.println("没有找到WiFi网络");
    } else {
      Serial.println("找到 " + String(n) + " 个网络:");
      for (int i = 0; i < n; ++i) {
        Serial.print(i + 1);
        Serial.print(": ");
        Serial.print(WiFi.SSID(i));
        Serial.print(" (");
        Serial.print(WiFi.RSSI(i));
        Serial.print("dBm) ");
        Serial.println((WiFi.encryptionType(i) == WIFI_AUTH_OPEN) ? "开放" : "加密");
        delay(10);
      }
    }
    Serial.println("OK");
    return;
  }

  // 处理摄像头参数调整命令
  if (serialCommand.startsWith("AT+CAM=")) {
    // 格式: AT+CAM=亮度,对比度,饱和度,曝光等级,增益
    // 例如: AT+CAM=1,1,0,1,5
    String params = serialCommand.substring(7);
    int values[5] = {0, 0, 0, 0, 0};
    int paramIndex = 0;
    int lastComma = -1;
    
    for (int i = 0; i <= params.length(); i++) {
      if (i == params.length() || params.charAt(i) == ',') {
        if (paramIndex < 5) {
          String valueStr = params.substring(lastComma + 1, i);
          values[paramIndex] = valueStr.toInt();
        }
        paramIndex++;
        lastComma = i;
      }
    }
    
    if (paramIndex == 5) {
      adjustCameraSettings(values[0], values[1], values[2], values[3], values[4]);
      Serial.println("OK");
    } else {
      Serial.println("ERROR: 参数格式错误，正确格式为: AT+CAM=亮度,对比度,饱和度,曝光等级,增益");
      Serial.println("例如: AT+CAM=1,1,0,1,5");
    }
    return;
  }

  // 处理查询摄像头参数命令
  if (serialCommand.equals("AT+CAM?")) {
    printCameraSettings();
    Serial.println("OK");
    return;
  }

  // 处理帮助命令
  if (serialCommand.equals("AT+HELP")) {
    Serial.println("支持的命令:");
    Serial.println("AT+CWJAP=\"SSID\",\"PASSWORD\" - 设置WiFi名称和密码");
    Serial.println("AT+CWMODE=1 - 设置为客户端模式(连接到现有WiFi)");
    Serial.println("AT+CWMODE=2 - 设置为AP模式(创建自己的WiFi网络)");
    Serial.println("AT+CWJAP? - 查询当前WiFi设置");
    Serial.println("AT+RST - 重启ESP32");
    Serial.println("AT+CWLAP - 扫描可用WiFi网络");
    Serial.println("AT+CAM=亮度,对比度,饱和度,曝光等级,增益 - 调整摄像头参数");
    Serial.println("AT+CAM? - 查询当前摄像头参数");
    Serial.println("AT+HELP - 显示帮助信息");
    Serial.println("OK");
    return;
  }

  Serial.println("ERROR: 未知命令，输入AT+HELP查看支持的命令");
}

// WebSocket回调函数
void onWebSocketEvent(websockets::WebsocketsClient& client, websockets::WebsocketsEvent event, String data) {
  if (event == websockets::WebsocketsEvent::ConnectionOpened) {
    Serial.println("WebSocket连接已建立");
    clientConnected = true;
    connectedClients++;
    ws_streaming = false; // 重置流状态
    Serial.printf("当前连接的客户端数: %d\n", connectedClients);
    
    // 检查内存状态
    checkMemoryStatus();
  } else if (event == websockets::WebsocketsEvent::ConnectionClosed) {
    Serial.println("WebSocket连接已关闭");
    wsStats.connectionDrops++;
    connectedClients--;
    if (connectedClients <= 0) {
      connectedClients = 0;
      clientConnected = false;
      ws_streaming = false;
    }
    Serial.printf("当前连接的客户端数: %d\n", connectedClients);
  } else if (event == websockets::WebsocketsEvent::GotPing) {
    Serial.println("收到WebSocket Ping");
    client.pong();
  } else if (event == websockets::WebsocketsEvent::GotPong) {
    Serial.println("收到WebSocket Pong");
  }
}

// WebSocket消息处理函数
void onWebSocketMessage(websockets::WebsocketsClient& client, websockets::WebsocketsMessage message) {
  Serial.print("WebSocket收到消息: ");
  Serial.println(message.data());

  String msg = message.data();
  if (msg == "requestStream") {
    Serial.println("收到视频流请求");
    ws_streaming = true;
    client.send("视频流已启动");
  } else if (msg == "stopStream") {
    Serial.println("收到停止视频流请求");
    ws_streaming = false;
    client.send("视频流已停止");
  } else if (msg == "ping") {
    client.send("pong");
  } else {
    client.send("服务器已收到: " + msg);
  }
}

// 通过WebSocket发送摄像头图像 - 优化版本
void sendCameraFrameViaWebSocket() {
  static unsigned long last_frame_time = 0;
  static unsigned long last_debug_time = 0;
  static unsigned long consecutive_failures = 0;
  unsigned long current_time = millis();

  // 检查内存状态
  checkMemoryStatus();

  // 每10秒打印一次调试信息
  if (current_time - last_debug_time > 10000) {
    Serial.printf("WebSocket状态: 连接=%s, 流启用=%s, 客户端可用=%s\n", 
                  clientConnected ? "是" : "否",
                  ws_streaming ? "是" : "否",
                  activeClient.available() ? "是" : "否");
    last_debug_time = current_time;
  }

  // 检查是否应该发送帧
  if (!clientConnected || !ws_streaming || !activeClient.available()) {
    return;
  }

  // 帧率控制
  if (current_time - last_frame_time < frame_interval) {
    return;
  }

  // 连续失败过多时暂停发送
  if (consecutive_failures > 3) {
    if (current_time - last_frame_time > 3000) { // 3秒后重试
      consecutive_failures = 0;
      Serial.println("重置WebSocket连续失败计数，尝试恢复发送");
    } else {
      return;
    }
  }

  last_frame_time = current_time;

  // 检查内存是否足够
  size_t freeHeap = ESP.getFreeHeap();
  if (freeHeap < 80000) { // 小于80KB时跳过这一帧
    Serial.printf("内存不足 (%d KB)，跳过此帧\n", freeHeap / 1024);
    wsStats.failedFrames++;
    consecutive_failures++;
    return;
  }

  // 获取摄像头帧
  camera_fb_t* fb = esp_camera_fb_get();
  if (!fb) {
    Serial.println("获取相机帧缓冲区失败");
    wsStats.failedFrames++;
    consecutive_failures++;
    return;
  }

  bool sent = false;
  size_t frame_size = 0;
  
  // 处理不同格式的图像
  if (fb->format != PIXFORMAT_JPEG) {
    // 需要转换为JPEG
    size_t jpg_buf_len = 0;
    uint8_t* jpg_buf = NULL;
    
    // 使用较低的质量设置以减少内存使用和转换时间
    bool jpg_converted = frame2jpg(fb, 40, &jpg_buf, &jpg_buf_len);  // 质量40，平衡大小和画质
    
    // 立即释放原始帧缓冲区
    esp_camera_fb_return(fb);
    fb = NULL;

    if (!jpg_converted || !jpg_buf) {
      Serial.println("JPEG转换失败");
      if (jpg_buf) free(jpg_buf);
      wsStats.failedFrames++;
      consecutive_failures++;
      return;
    }

    frame_size = jpg_buf_len;
    
    // 检查转换后的大小是否合理
    if (jpg_buf_len > 100000) { // 大于100KB的帧可能过大
      Serial.printf("JPEG帧过大 (%d bytes)，跳过发送\n", jpg_buf_len);
      free(jpg_buf);
      wsStats.failedFrames++;
      consecutive_failures++;
      return;
    }

    // 发送JPEG数据
    try {
      if (activeClient.available()) {
        sent = activeClient.sendBinary((const char*)jpg_buf, jpg_buf_len);
      }
    } catch (...) {
      Serial.println("WebSocket发送异常");
      sent = false;
    }
    
    // 释放JPEG缓冲区
    free(jpg_buf);
  } else {
    // 直接发送JPEG数据
    frame_size = fb->len;
    
    // 检查帧大小
    if (fb->len > 100000) {
      Serial.printf("原始JPEG帧过大 (%d bytes)，跳过发送\n", fb->len);
      esp_camera_fb_return(fb);
      wsStats.failedFrames++;
      consecutive_failures++;
      return;
    }

    // 发送数据
    try {
      if (activeClient.available()) {
        sent = activeClient.sendBinary((const char*)fb->buf, fb->len);
      }
    } catch (...) {
      Serial.println("WebSocket发送异常");
      sent = false;
    }
    
    // 释放帧缓冲区
    esp_camera_fb_return(fb);
  }

  // 更新统计信息
  if (sent) {
    wsStats.totalFramesSent++;
    consecutive_failures = 0;
    
    // 减少成功日志频率
    static unsigned long last_success_log = 0;
    if (current_time - last_success_log > 30000) { // 每30秒最多打印一次成功日志
      Serial.printf("WebSocket: 帧发送成功 (%d bytes)\n", frame_size);
      last_success_log = current_time;
    }
  } else {
    wsStats.failedFrames++;
    consecutive_failures++;
    Serial.printf("WebSocket: 帧发送失败 (%d bytes)，连续失败: %lu\n", frame_size, consecutive_failures);
    
    // 如果连续失败次数过多，关闭连接让客户端重连
    if (consecutive_failures > 10) {
      Serial.println("连续失败次数过多，关闭WebSocket连接");
      if (activeClient.available()) {
        activeClient.close();
      }
      clientConnected = false;
      ws_streaming = false;
      consecutive_failures = 0;
    }
  }
}

// 添加获取MAC地址的函数
void getMACAddress(uint8_t* mac) {
    esp_read_mac(mac, ESP_MAC_BT);
}

// 添加生成三字母ID的函数
void BLE_initThreeLetterID() {
    uint8_t mac[6] = {0, 0, 0, 0, 0, 0};
    getMACAddress(mac);
    int machineNum = (mac[4] << 8) | mac[5]; // 16 least significant bits

    BLE_ThreeLetterID[0] = 65 + (machineNum % 26);
    machineNum = machineNum / 26;
    BLE_ThreeLetterID[1] = 65 + (machineNum % 26);
    machineNum = machineNum / 26;
    BLE_ThreeLetterID[2] = 65 + (machineNum % 26);
    BLE_ThreeLetterID[3] = 0;
}

// 添加初始化设备名称的函数
void initDeviceName(const char* prefix) {
    BLE_initThreeLetterID();
    snprintf(ap_ssid_buffer, sizeof(ap_ssid_buffer), "%s %s", prefix, BLE_ThreeLetterID);
}

void setup() {
  // 初始化串口
  Serial.begin(115200);
  delay(1000);

  Serial.println("\n\n");
  Serial.println("ESP32-S3  Camera Web Server");
  Serial.println("--------------------------------");

  // 从NVS读取WiFi设置
  preferences.begin("wifi", false);  // 读写模式

  // 强制清除旧的WiFi设置，确保使用AP模式
  Serial.println("清除NVS中的旧WiFi设置...");
  preferences.remove("ap_mode");
  preferences.remove("ssid");
  preferences.remove("password");
  
  // 强制设置为AP模式
  USE_AP_MODE = true;
  preferences.putBool("ap_mode", USE_AP_MODE);
  
  Serial.println("强制设置为AP模式");
  Serial.println("USE_AP_MODE: " + String(USE_AP_MODE ? "true" : "false"));

  preferences.end();

  // 打印串口命令帮助
  Serial.println("\n您可以通过串口命令设置WiFi:");
  Serial.println("AT+CWJAP=\"SSID\",\"PASSWORD\" - 设置WiFi名称和密码");
  Serial.println("AT+CWMODE=1/2 - 设置为客户端/AP模式");
  Serial.println("输入AT+HELP查看所有命令\n");

  // 完全清除Wi-Fi设置和NVS存储的凭据
  Serial.println("正在彻底清除存储的WiFi凭据...");
  WiFi.disconnect(true, true);  // 断开连接并清除配置
  WiFi.mode(WIFI_OFF);          // 关闭WiFi

  delay(1000);
  Serial.println("WiFi已重置，准备使用新设置连接");

  // 检查PSRAM - 详细信息
  Serial.println("检查PSRAM...");
  if (psramFound()) {
    Serial.println("PSRAM找到!");
    Serial.printf("PSRAM总大小: %d KB\n", ESP.getPsramSize() / 1024);
    Serial.printf("PSRAM可用大小: %d KB\n", ESP.getFreePsram() / 1024);
    Serial.printf("内部RAM可用: %d KB\n", ESP.getFreeHeap() / 1024);
    Serial.println("将使用PSRAM进行高分辨率摄像头缓冲");
  } else {
    Serial.println("未找到PSRAM! 摄像头高分辨率模式将不可用");
    Serial.println("请检查PSRAM配置或硬件连接");
    Serial.printf("内部RAM可用: %d KB\n", ESP.getFreeHeap() / 1024);
  }

  // 初始化文件系统
  Serial.println("\n==================================================");
  Serial.println("🗂️  开始初始化文件系统...");
  Serial.println("==================================================");

  bool fileSystemOK = initFileSystem();

  if (!fileSystemOK) {
    Serial.println("\n❌ 文件系统初始化失败!");
    Serial.println("⚠️  将使用内置备用HTML页面");
    Serial.println("📝 要解决此问题，请:");
    Serial.println("   1. 确保 data/ 目录包含所需文件");
    Serial.println("   2. 运行: pio run -e esp32s3_eye -t uploadfs");
    Serial.println("   3. 检查分区表配置");
  } else {
    Serial.println("\n✅ 文件系统初始化成功!");
    Serial.println("🎉 Web界面将使用文件系统中的资源");
  }

  Serial.println("==================================================\n");

  // 初始化摄像头
  if (!initCamera()) {
    Serial.println("摄像头初始化失败，程序将继续但摄像头功能不可用");
  } else {
    Serial.println("摄像头完全配置并初始化成功");
  }

  // 在WiFi初始化之前生成设备名称
  initDeviceName(ap_ssid_prefix);
  ap_ssid = ap_ssid_buffer;  // 使用生成的SSID

  // 连接WiFi或启动接入点
  if (USE_AP_MODE) {
    // 设置接入点模式
    Serial.println("正在设置接入点模式...");
    WiFi.mode(WIFI_AP);
    WiFi.softAPConfig(ap_ip, ap_gateway, ap_subnet);

    if (WiFi.softAP(ap_ssid, ap_password)) {
      Serial.println("接入点模式设置成功");
      Serial.print("接入点SSID: ");
      Serial.println(ap_ssid);
      Serial.print("接入点密码: ");
      Serial.println(ap_password);
      Serial.print("接入点IP地址: ");
      Serial.println(WiFi.softAPIP());

      // 设置mDNS响应器
      mdnsStarted = setupMDNS();

      // 启动摄像头Web服务器
      startCameraServer();

      Serial.println("摄像头Web服务器启动完成");
      Serial.println("使用浏览器访问:");
      if (mdnsStarted) {
        Serial.printf(" - http://%s.local\n", hostname);
      }
      Serial.printf(" - http://%s\n", WiFi.softAPIP().toString().c_str());
      Serial.println("\n注意：接入点模式下，您可以连接到ESP32的WiFi网络，通过localhost访问从而使用Web Bluetooth功能。");
    } else {
      Serial.println("接入点模式设置失败");
    }
  } else {
    // 设置客户端模式，连接到现有WiFi
    Serial.println("正在设置客户端模式...");
    WiFi.mode(WIFI_STA);
    delay(500);

    Serial.printf("\n连接到WiFi: %s\n", sta_ssid.c_str());

    WiFi.begin(sta_ssid.c_str(), sta_password.c_str());

    // 等待WiFi连接
    bool connected = false;
    int connectionAttempts = 0;
    while (!connected && connectionAttempts < 20) {
      delay(500);
      Serial.print(".");
      connectionAttempts++;
      if (WiFi.status() == WL_CONNECTED) {
        connected = true;
      }
    }

    Serial.println("");
    if (connected) {
      Serial.println("WiFi连接成功");
      Serial.print("IP地址: ");
      Serial.println(WiFi.localIP());

      // 设置mDNS响应器
      mdnsStarted = setupMDNS();

      // 启动摄像头Web服务器
      startCameraServer();

      Serial.println("摄像头Web服务器启动完成");
      Serial.println("使用浏览器访问:");
      if (mdnsStarted) {
        Serial.printf(" - http://%s.local\n", hostname);
      }
      Serial.printf(" - http://%s\n", WiFi.localIP().toString().c_str());
      Serial.println("\n注意：如要使用蓝牙功能，请将USE_AP_MODE设置为true并重新编译，或使用支持https或localhost的浏览器。");
    } else {
      Serial.println("WiFi连接失败");
    }
  }
}

void loop() {
  // 处理串口命令
  while (Serial.available()) {
    char inChar = (char)Serial.read();

    // 如果是回车或换行，则完成命令
    if (inChar == '\n' || inChar == '\r') {
      if (serialCommand.length() > 0) {
        commandComplete = true;
      }
    } else {
      serialCommand += inChar;
    }
  }

  // 如果命令完成，处理它
  if (commandComplete) {
    processSerialCommand();
    serialCommand = "";
    commandComplete = false;
  }

  if (USE_AP_MODE) {
    // AP模式下不需要重连，确保保持AP模式
    if (WiFi.getMode() != WIFI_AP) {
      Serial.println("检测到WiFi模式异常，重新设置为AP模式...");
      WiFi.mode(WIFI_AP);
      WiFi.softAPConfig(ap_ip, ap_gateway, ap_subnet);
      WiFi.softAP(ap_ssid, ap_password);
    }
  } else {
    // 客户端模式下，如果WiFi断开，尝试重新连接
    if (WiFi.status() != WL_CONNECTED) {
      Serial.println("WiFi连接断开，尝试重新连接...");
      WiFi.reconnect();
    }
  }

  // 处理WebSocket连接
  if (wsServer.poll()) {
    // 检查新连接
    websockets::WebsocketsClient newClient = wsServer.accept();
    if (newClient.available()) {
      Serial.println("接受新的WebSocket客户端连接");

      // 如果已有活动客户端，先关闭旧连接
      if (activeClient.available()) {
        Serial.println("关闭旧的WebSocket连接");
        activeClient.close();
      }

      // 设置事件处理
      newClient.onEvent(onWebSocketEvent);
      newClient.onMessage(onWebSocketMessage);

      // 保存新的活动客户端
      activeClient = newClient;
      clientConnected = true;
      connectedClients = 1; // 重置为1，因为我们只支持一个活动连接
      ws_streaming = false; // 新连接需要重新请求流

      // 发送欢迎消息
      newClient.send("WebSocket连接成功，发送'requestStream'开始视频流");
    }
  }

  // 处理现有客户端的消息和连接状态
  if (clientConnected && activeClient.available()) {
    // 处理消息
    activeClient.poll();

    // 定期发送心跳包保持连接活跃
    static unsigned long lastHeartbeat = 0;
    if (millis() - lastHeartbeat > 30000) {  // 每30秒发送心跳
      try {
        activeClient.ping();
        lastHeartbeat = millis();
      } catch (...) {
        Serial.println("发送心跳失败，连接可能已断开");
        clientConnected = false;
        ws_streaming = false;
      }
    }
  } else if (clientConnected) {
    // 客户端状态不一致，重置连接状态
    Serial.println("检测到WebSocket连接状态不一致，重置连接");
    clientConnected = false;
    ws_streaming = false;
    connectedClients = 0;
  }

  // 发送摄像头帧
  sendCameraFrameViaWebSocket();

  // 定期执行垃圾回收和内存整理
  static unsigned long lastGC = 0;
  if (millis() - lastGC > 60000) { // 每分钟执行一次
    // 内存维护
    delay(1);
    lastGC = millis();
    Serial.println("执行内存维护");
  }

  delay(10);  // 增加延迟以减少CPU占用，提高稳定性
} 