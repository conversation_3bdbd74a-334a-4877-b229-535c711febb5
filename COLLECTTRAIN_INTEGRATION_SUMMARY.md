# collecttrain.html WebSocket 视频流集成总结

## 🎯 项目目标

将 `data/collecttrain.html` 页面从使用本地网络摄像头改为使用 ESP32-S3 的 WebSocket 视频流，实现与主页相同的摄像头源。

## ✅ 已完成的修改

### 1. HTML 页面修改 (`data/collecttrain.html`)

#### 视频元素替换
- ❌ 原来：`<video autoplay playsinline="" muted id="webcam" width="224" height="224"></video>`
- ✅ 现在：`<img id="webcam" width="224" height="224" style="border: 1px solid #ccc; background-color: #f0f0f0;" alt="ESP32 摄像头流">`

#### 添加状态显示
```html
<div id="camera-status" style="text-align: center; margin-top: 5px; font-size: 12px; color: #666;">
    正在连接摄像头...
</div>
```

#### 添加导航栏
```html
<nav class="navbar navbar-expand-lg navbar-light bg-light mb-3">
    <div class="container-fluid">
        <a class="navbar-brand" href="/">ESP32-S3 AI 摄像头</a>
        <div class="navbar-nav">
            <a class="nav-link" href="/">主页</a>
            <a class="nav-link active" href="/collecttrain.html">数据采集训练</a>
        </div>
    </div>
</nav>
```

### 2. JavaScript 修改 (`data/js/collecttrain.js`)

#### 添加 WebSocket 支持变量
```javascript
let wsCamera = null;
let wsConnected = false;
let reconnectInterval = 3000;
let wsReconnectTimer = null;
let connectionAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 3;
let usingFallbackStream = false;
```

#### 修改摄像头元素引用
- ❌ 原来：`const video = document.getElementById('webcam');`
- ✅ 现在：`const imgElement = document.getElementById('webcam');`

#### 添加 WebSocket 连接函数
- `connectCameraWebSocket()` - 连接 WebSocket 摄像头流
- `switchToHttpStream()` - 切换到 HTTP 流作为备用
- `updateCameraStatus()` - 更新摄像头状态显示

#### 修改图像捕获函数
```javascript
function captureImage(direction) {
    // 检查摄像头是否有图像
    if (!imgElement.src || imgElement.src === '') {
        console.warn('摄像头图像未准备好');
        updateCameraStatus("摄像头图像未准备好");
        return;
    }
    
    // 从img元素绘制到canvas
    const ctx = canvas.getContext('2d');
    ctx.drawImage(imgElement, 0, 0, canvas.width, canvas.height);
    // ... 其余逻辑保持不变
}
```

#### 修改摄像头设置函数
```javascript
async function setupWebcam() {
    try {
        // 设置img元素尺寸
        imgElement.width = 224;
        imgElement.height = 224;
        
        // 设置canvas尺寸用于图像处理
        canvas.width = 224;
        canvas.height = 224;
        
        console.log('摄像头设置完成，等待WebSocket连接...');
        updateCameraStatus("摄像头设置完成");
        
        return Promise.resolve(imgElement);
    } catch (error) {
        console.error('摄像头设置错误:', error);
        updateCameraStatus("摄像头设置失败");
    }
}
```

### 3. 服务器路由添加 (`src/camera_server.cpp`)

#### 新增路由定义
```cpp
// 数据采集训练页面路由
httpd_uri_t collecttrain_uri = {
  .uri = "/collecttrain.html",
  .method = HTTP_GET,
  .handler = static_file_handler,
  .user_ctx = NULL
};

// 数据采集训练JS文件路由
httpd_uri_t collecttrain_js_uri = {
  .uri = "/js/collecttrain.js",
  .method = HTTP_GET,
  .handler = static_file_handler,
  .user_ctx = NULL
};

// jQuery库文件路由（未压缩版本，服务器会自动选择gzip版本如果存在）
httpd_uri_t jquery_uri = {
  .uri = "/js/jquery-3.6.0.min.js",
  .method = HTTP_GET,
  .handler = static_file_handler,
  .user_ctx = NULL
};

// JSZip库文件路由（未压缩版本，服务器会自动选择gzip版本如果存在）
httpd_uri_t jszip_uri = {
  .uri = "/js/jszip.min.js",
  .method = HTTP_GET,
  .handler = static_file_handler,
  .user_ctx = NULL
};

// FileSaver库文件路由（未压缩版本，服务器会自动选择gzip版本如果存在）
httpd_uri_t filesaver_uri = {
  .uri = "/js/file-saver.js",
  .method = HTTP_GET,
  .handler = static_file_handler,
  .user_ctx = NULL
};

// TensorFlow.js可视化库文件路由（未压缩版本，服务器会自动选择gzip版本如果存在）
httpd_uri_t tfjs_vis_uri = {
  .uri = "/js/tfjs-vis.js",
  .method = HTTP_GET,
  .handler = static_file_handler,
  .user_ctx = NULL
};
```

#### 路由注册
```cpp
// 注册数据采集训练相关路由
httpd_register_uri_handler(camera_httpd, &collecttrain_uri);    // 数据采集训练页面
httpd_register_uri_handler(camera_httpd, &collecttrain_js_uri); // 数据采集训练JS文件
httpd_register_uri_handler(camera_httpd, &jquery_uri);          // jQuery库文件
httpd_register_uri_handler(camera_httpd, &jszip_uri);           // JSZip库文件
httpd_register_uri_handler(camera_httpd, &filesaver_uri);       // FileSaver库文件
httpd_register_uri_handler(camera_httpd, &tfjs_vis_uri);        // TensorFlow.js可视化库文件
```

### 4. 主页导航链接 (`data/index.html`)

#### 添加导航链接
```html
<!-- 导航链接 -->
<div class="nav-links" style="text-align: center; margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
    <a href="/" style="margin: 0 10px; text-decoration: none; color: #007bff; font-weight: bold;">🏠 主页</a>
    <a href="/collecttrain.html" style="margin: 0 10px; text-decoration: none; color: #28a745;">📊 数据采集训练</a>
</div>
```

## 🔧 技术实现要点

### WebSocket 连接逻辑
1. **自动重连机制**：最多尝试 3 次 WebSocket 连接
2. **备用方案**：WebSocket 失败后自动切换到 HTTP 流
3. **状态显示**：实时显示连接状态和错误信息
4. **资源管理**：正确释放 Blob URL 避免内存泄漏

### 图像处理适配
1. **元素替换**：从 `<video>` 元素改为 `<img>` 元素
2. **Canvas 绘制**：使用 `drawImage(imgElement, ...)` 替代 `drawImage(video, ...)`
3. **尺寸设置**：确保 img 和 canvas 都设置为 224x224 像素
4. **错误处理**：检查图像是否加载完成再进行捕获

### 智能文件加载机制 ⭐ **核心特性**
1. **服务器端智能选择**：`static_file_handler` 自动检查并优先使用 gzip 压缩版本
2. **HTML 简化引用**：HTML 中直接引用未压缩路径，如 `/js/tfjs.js`
3. **自动回退**：如果 `.gz` 文件不存在，自动使用未压缩版本
4. **透明处理**：浏览器自动解压 gzip 内容，对前端透明

#### 智能加载工作流程：
```
1. HTML 请求: /js/tfjs.js
2. 服务器检查: /js/tfjs.js.gz 是否存在？
3. 如果存在: 返回 tfjs.js.gz (设置正确的 Content-Encoding)
4. 如果不存在: 返回 tfjs.js
5. 浏览器自动处理 gzip 解压
```

### 服务器路由管理
1. **静态文件服务**：复用现有的 `static_file_handler`
2. **路由组织**：按功能分组注册路由
3. **日志输出**：显示新注册的路由信息
4. **智能文件选择**：自动选择最佳文件版本（压缩/未压缩）

## 🚀 使用方法

### 编译和上传
```bash
# 编译项目
pio run -e seeed_xiao_esp32s3_sense

# 上传固件
pio run -e seeed_xiao_esp32s3_sense -t upload

# 上传文件系统
pio run -e seeed_xiao_esp32s3_sense -t uploadfs
```

### 访问页面
1. **主页**：`http://[设备IP]/`
2. **数据采集训练页面**：`http://[设备IP]/collecttrain.html`

### 功能验证
1. ✅ WebSocket 摄像头流正常显示
2. ✅ 图像捕获功能正常工作
3. ✅ 数据采集和训练功能完整
4. ✅ 页面间导航正常
5. ✅ 备用 HTTP 流自动切换

## 📝 注意事项

1. **文件依赖**：确保所有 gzip 压缩的 JS 库文件都存在于 `data/js/` 目录
2. **WebSocket 端口**：WebSocket 服务器运行在端口 81
3. **图像格式**：支持 WebSocket 的 Blob 数据和 HTTP 的 MJPEG 流
4. **浏览器兼容性**：现代浏览器都支持 WebSocket 和 Canvas API

## 🎉 集成完成

现在 `collecttrain.html` 页面已经完全集成了 ESP32-S3 的 WebSocket 视频流，与主页使用相同的摄像头源，提供了一致的用户体验和稳定的视频流连接。
