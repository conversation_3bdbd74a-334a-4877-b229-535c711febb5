// 原始index.js内容开始
const SERVICE_UUID = 'bb37a001-b922-4018-8e74-e14824b3a638';
const CHARACTERISTIC_UUID_RX = 'bb37a002-b922-4018-8e74-e14824b3a638';
const CHARACTERISTIC_UUID_TX = 'bb37a003-b922-4018-8e74-e14824b3a638';

let device = null;
let txCharacteristic = null;
const bleStatus = document.getElementById('bleDiv');
const connectBtn = document.getElementById('connect');
const disConnBtn = document.getElementById('disconnect');

const goCollectBtn = document.getElementById('goCollectBtn');
const leftCollectBtn = document.getElementById('leftCollectBtn');
const rightCollectBtn = document.getElementById('rightCollectBtn');
const stopCollectBtn = document.getElementById('stopCollectBtn');

const video = document.getElementById('webcam');
const canvas = document.createElement('canvas');
let imageData = { go: [], left: [], right: [], stop: [] };

let autoCollectInterval = null;
let lastDirection = null;
let totalImagesCount = 0;
let maxImagesCount = $('#maxImagesCount').val();

canvas.width = 224;
canvas.height = 224;
let continueCollect = false;

$(document).ready(() => {
    setupWebcam();
    $('#maxImagesCount').on('change', () => {
        maxImagesCount = $('#maxImagesCount').val();
        console.log(maxImagesCount);
    });

    $('#download').click(downloadImages);
    $('#clear').click(clearImages);

    // 设置toggle默认状态为关闭
    $('#toggleSwitch').prop('checked', false);
    $('#toggleSwitch').change(function () {
        if (this.checked) {
            continueCollect = true;

        } else {
            continueCollect = false;

        }
    });

    $('#trueFalseToggleSwitch').change(function () {
        if (this.checked) {
            // $('#status').text('Toggle switch is ON');
            // 获取开关打开时的值
            console.log('Toggle switch value: true');
            continueCollect = true;
        } else {
            // $('#status').text('Toggle switch is OFF');
            // 获取开关关闭时的值
            clearInterval(autoCollectInterval);
            continueCollect = false;
            console.log('Toggle switch value: false');
        }
    });

    // 初始化导入数据集功能
    initializeImportDataset();
});
// 
async function connectBLE() {
    try {
        device = await navigator.bluetooth.requestDevice({
            filters: [{ namePrefix: 'MicroBlocks' }],
            optionalServices: [SERVICE_UUID]
        });
        const server = await device.gatt.connect();
        const service = await server.getPrimaryService(SERVICE_UUID);
        const characteristics = await Promise.all([
            service.getCharacteristic(CHARACTERISTIC_UUID_RX),
            service.getCharacteristic(CHARACTERISTIC_UUID_TX)
        ]);
        txCharacteristic = characteristics[1];
        await characteristics[1].startNotifications();
        characteristics[1].addEventListener('characteristicvaluechanged', handleNotifications);

        connectBtn.disabled = true;
        disConnBtn.disabled = false;
        bleStatus.textContent = `已连接蓝牙设备: ${device.name}`;
        console.log('连接BLE设备');
    } catch (error) {
        console.error('连接错误：' + error);
    }
}

async function sendCommand(cmd) {
    if (device && device.gatt.connected) {
        try {
            const service = await device.gatt.getPrimaryService(SERVICE_UUID);
            const rxCharacteristic = await service.getCharacteristic(CHARACTERISTIC_UUID_RX);
            await rxCharacteristic.writeValue(new TextEncoder().encode(cmd));
            console.log(`${cmd} 命令已发送`);
        } catch (error) {
            console.error('发送数据错误：', error);
        }
    } else {
        console.log('设备未连接');
    }
}

function handleNotifications(event) {
    const value = new TextDecoder().decode(event.target.value);
    const command = value.replace(/[^A-Za-z]/g, '');
    bleStatus.textContent = `收到指令: ${command}`;
    // console.log(`收到数据: ${command}`);

    const commands = ['go', 'left', 'right', 'stop', 'tingzhicaiji'];
    const direction = commands.find(cmd => command.includes(cmd));

    if (totalImagesCount < maxImagesCount) {
        captureImage(direction);

    }

    if (continueCollect && direction && lastDirection !== direction) {
        clearInterval(autoCollectInterval);
        lastDirection = direction;
        if (totalImagesCount < maxImagesCount) {
            if (lastDirection == 'left' || lastDirection == 'right') {
                autoCollectInterval = setInterval(() => {
                    if (totalImagesCount < maxImagesCount) {
                        captureImage(direction);
                    } else {
                        clearInterval(autoCollectInterval);
                    }
                }, 20);

            }
            if (lastDirection == 'go' || lastDirection == 'stop') {
                autoCollectInterval = setInterval(() => {
                    if (totalImagesCount < maxImagesCount) {
                        captureImage(direction);
                    } else {
                        clearInterval(autoCollectInterval);
                    }
                }, 130);

            }

        }
    }
}



// 确保每个方向在使用之前都已被初始化
function initializeDirection(direction) {
    if (!imageData) {
        imageData = {}; // 确保imageData对象存在
    }

    if (!imageData[direction]) {
        imageData[direction] = [];
    }
}

function captureImage(direction) {
    // 初始化方向
    if (direction === 'go' || direction === 'left' || direction === 'right' || direction === 'stop') {
        initializeDirection(direction);
        canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
        const img = $('<img>').attr('src', canvas.toDataURL());
        // var category = $(this).data('category');
        // console.log(category);
        let count = document.getElementById(`count-${direction}`);

        img.on('click', function () {
            const imgSrc = $(this).attr('src');
            const index = imageData[direction].indexOf(imgSrc);
            if (index > -1) {
                imageData[direction].splice(index, 1);
                $(this).remove();
                totalImagesCount--;
                updateCount();
            }
            count.innerText = `样本数: ${imageData[direction].length}`;
        });
        // 设置鼠标悬停提示信息
        img.attr('alt', '点击图片可以删除');
        $('#' + direction + 'Images').prepend(img);//后进的图片显示在最前面
        // imageData[direction].push(canvas.toDataURL());
        imageData[direction].push(canvas.toDataURL());
        count.innerText = `样本数: ${imageData[direction].length}`;
        totalImagesCount++;
        updateCount();
        // console.log('Direction is valid');
    } else {

        // console.log('Direction is invalid');
    }

}


function updateCount() {
    const totalCount = imageData.go.length + imageData.left.length + imageData.right.length + imageData.stop.length;
    const imageCountElement = document.getElementById('imageCount');
    if (imageCountElement) {
        imageCountElement.textContent = `Go: ${imageData.go.length}, Left: ${imageData.left.length}, Right: ${imageData.right.length}, Stop: ${imageData.stop.length}, 总计采集:${totalCount}`;
    }

    // 同时更新各个类别的计数
    ['go', 'left', 'right', 'stop'].forEach(category => {
        const countElement = document.getElementById(`count-${category}`);
        if (countElement) {
            countElement.textContent = `样本数: ${imageData[category].length}`;
        }
    });

    // 更新总图片计数变量
    totalImagesCount = totalCount;
}

async function setupWebcam() {

    try {

        video.width = 224;
        video.height = 224;
        const cam = await tf.data.webcam(video, { facingMode: 'environment' });
        const img = await cam.capture();
        return img
    } catch (error) {
        console.error('Error accessing the camera', error);
    }
}

async function downloadImages() {
    const zip = new JSZip();

    // 添加每个分类的图像到相应文件夹
    const categories = ['go', 'left', 'right', 'stop'];
    const date = new Date();
    const offset = 8; // UTC+8 东八区
    const localDate = new Date(date.getTime() + offset * 60 * 60 * 1000);
    const formattedDate = localDate.toISOString()
        .replace(/T/, '-') // 将T替换为-
        .replace(/\..+/, '') // 去除小数部分及后面的内容
        .replace(/:/g, '-'); // 将冒号替换为-

    try {
        for (const category of categories) {
            if (imageData[category] && imageData[category].length > 0) {
                const folder = zip.folder(category);

                // 处理每个图像
                for (let i = 0; i < imageData[category].length; i++) {
                    const imgData = imageData[category][i];

                    // 检查是否为base64格式
                    if (imgData.startsWith('data:image/')) {
                        // 处理base64格式图片
                        const data = imgData.replace(/^data:image\/(png|jpg|jpeg);base64,/, '');
                        folder.file(`${category}_${formattedDate}_${i}.png`, data, { base64: true });
                    } else {
                        // 处理相对路径或URL格式的图片
                        try {
                            // 通过创建图像元素加载图片
                            const img = new Image();
                            const imgLoaded = await new Promise((resolve, reject) => {
                                img.onload = () => resolve(true);
                                img.onerror = () => reject(new Error(`Failed to load image: ${imgData}`));
                                img.src = imgData;
                            });

                            // 创建canvas来转换图片为base64
                            const canvas = document.createElement('canvas');
                            canvas.width = img.width;
                            canvas.height = img.height;
                            const ctx = canvas.getContext('2d');
                            ctx.drawImage(img, 0, 0);

                            // 获取base64数据
                            const base64Data = canvas.toDataURL('image/png')
                                .replace(/^data:image\/(png|jpg|jpeg);base64,/, '');

                            // 添加到zip
                            folder.file(`${category}_${formattedDate}_${i}.png`, base64Data, { base64: true });
                        } catch (error) {
                            console.error(`Error processing image ${i} in ${category}:`, error);
                            // 显示错误但继续处理其他图片
                            document.getElementById('warn').textContent =
                                `处理图片时出错: ${error.message}。已跳过问题图片，继续处理其他图片。`;
                        }
                    }
                }
            }
        }

        // 生成并下载压缩文件
        const content = await zip.generateAsync({ type: 'blob' });
        saveAs(content, `dataset-${formattedDate}-go${imageData.go.length}-left${imageData.left.length}-right${imageData.right.length}-stop${imageData.stop.length}.zip`);

        document.getElementById('status').textContent = '数据集已成功下载！';
    } catch (error) {
        console.error('生成数据集时出错:', error);
        document.getElementById('warn').textContent = `下载数据集失败: ${error.message}`;
    }
}

async function downloadDataset() {
    // 直接调用改进后的downloadImages函数
    await downloadImages();
}

function clearImages() {
    $(".preview img").remove();

    for (const category in imageData) {
        let count = document.getElementById(`count-${category}`);
        imageData[category].length = 0;
        count.innerText = `样本数: ${imageData[category].length}`;
    }
    totalImagesCount = 0;
    updateCount();
}

$('.clear-category').click(function () {

    var category = $(this).data('category');
    var imageCountToRemove = imageData[category].length;
    let count = document.getElementById(`count-${category}`);

    // 清空分类的图片
    $('#' + category + 'Images').empty();
    // console.log(category);
    imageData[category].length = 0;
    count.innerText = `样本数: ${imageData[category].length}`;
    // 更新 totalImagesCount
    totalImagesCount -= imageCountToRemove;
    // 确保 totalImagesCount 不会变成负数
    if (totalImagesCount < 0) {
        totalImagesCount = 0;
    }
    // console.log(totalImagesCount);
    updateCount();
});
$('.collect-btn').click(function () {

    var category = $(this).data('category');
    var imageCountToRemove = imageData[category].length;
    let count = document.getElementById(`count-${category}`);

    captureImage(category);

    // 清空分类的图片
    // $('#' + category + 'Images').empty();
    // console.log(category);
    // imageData[category].length = 0;
    count.innerText = `样本数: ${imageData[category].length}`;
    // 更新 totalImagesCount
    totalImagesCount -= imageCountToRemove;
    // 确保 totalImagesCount 不会变成负数
    // if (totalImagesCount < 0) {
    //     totalImagesCount = 0;
    // }
    // console.log(totalImagesCount);
    updateCount();
});
$('#connect').click(connectBLE);
$('#disconnect').click(() => {
    try {
        clearInterval(autoCollectInterval);
        if (device && device.gatt.connected) {
            device.gatt.disconnect();
            connectBtn.disabled = false;
            disConnBtn.disabled = true;
            clearInterval(autoCollectInterval);
            bleStatus.textContent = `蓝牙设备: ${device.name} 已断开`;
            console.log('断开蓝牙设备');
        } else {
            console.log('设备未连接');
        }

    } catch {
        console.log('设备未连接');
    }

});

////////////////////////////////////////////////////////////////////////////////////

let truncatedMobileNet;
let model;
let denseUnits;
let numClasses;


let classNames = ['go', 'left', 'right', 'stop'];


const learningRateInput = document.getElementById('learningRate');
const batchSizeInput = document.getElementById('batchSize');
const epochsInput = document.getElementById('epochs');
const denseUnitsInput = document.getElementById('dense-units');
const validationSplitInput = document.getElementById('validationSplit');

const trainModelButton = document.getElementById('trainModel');
const saveModelButton = document.getElementById('downloadModel');
const modelSummary = document.getElementById('model-summary');


trainModelButton.addEventListener('click', () => {
    trainModel();
});
saveModelButton.addEventListener('click', () => {
    saveModel();
});

document.getElementById('downloadDataset').addEventListener('click', downloadImages);
async function loadTruncatedMobileNet() {
    document.getElementById('status').innerText = 'MobileNet预训练模型加载中...';
    try {

        const mobilenet = await tf.loadLayersModel('https://storage.googleapis.com/tfjs-models/tfjs/mobilenet_v1_0.25_224/model.json');

        // Return a model that outputs an internal activation.
        const layer = mobilenet.getLayer('conv_pw_13_relu');
        document.getElementById('status').innerText = 'MobileNet 预训练模型加载完成';

        return tf.model({ inputs: mobilenet.inputs, outputs: layer.output });

    } catch (error) {
        console.error('Error loading model:', error);
    }
    document.getElementById('status').innerText = 'MobileNet预训练模型加载完成';

}
function preprocessImage(image) {
    return tf.browser.fromPixels(image) // 创建张量
        .resizeNearestNeighbor([224, 224]) // 调整大小
        .toFloat()// 转换数据类型
        .div(tf.scalar(127))// 归一化
        .sub(tf.scalar(1));// 进一步归一化

}
////////////////////////////////////////////////////////
//加载页面中的代码
function loadCode(scriptId, textAreaId) {
    let scriptNode = document.getElementById(scriptId);
    let textArea = document.getElementById(textAreaId);
    if (scriptNode.type !== 'text/code-snippet') {
        throw Error('Unknown code snippet type');
    }
    textArea.value = scriptNode.text.replace(/^\n/, '');
}

function executeCode(textAreaId) {
    try {
        clearError();
        let code = document.getElementById(textAreaId).value;
        // 使用 Function 构造函数执行代码
        const func = new Function('tf', 'initializeModel', 'truncatedMobileNet', 'numClasses', 'denseUnits', code);
        func(tf, initializeModel, truncatedMobileNet, numClasses, denseUnits); // 传递实际的变量
        console.log('模型初始化代码执行完成');
    } catch (err) {
        printError(err);
    }
}

function initializeModel(newModel, tf, truncatedMobileNet, numClasses, denseUnits) {
    model = newModel;
    tf = tf;
    truncatedMobileNet = truncatedMobileNet;
    numClasses = numClasses;
    denseUnits = denseUnits;
}

function clearError() {
    document.getElementById('warn').innerText = '';
}

function printError(err) {
    document.getElementById('warn').innerText = '错误: ' + err.message;
}

loadCode('codeSnippet', 'codeEditor');
//加载页面中的代码
////////////////////////////////////////////////////////

// async function trainModel() {
//     numClasses = Object.keys(imageData).filter(category => imageData[category].length > 0).length;
//     console.log(numClasses);

//     if (numClasses < 3) {
//         document.getElementById('warn').innerText = '请至少上传go left right三个类别的图像数据集。';
//         return;
//     }



//     const learningRate = parseFloat(learningRateInput.value);
//     const batchSize = parseInt(batchSizeInput.value);
//     const epochs = parseInt(epochsInput.value);
//     denseUnits = parseInt(denseUnitsInput.value);




//     // 执行 id="codeEditor" 中的代码，确保 model 被正确初始化
//     executeCode('codeEditor');

//     // 检查 model 是否被正确初始化
//     if (!model) {
//         document.getElementById('warn').innerText = '模型初始化失败，请检查代码';
//         console.error('模型未初始化，请检查自定义代码');
//         return;
//     }
//     // console.log(``);

//     const myOptimizer = tf.train.adam(learningRate);
//     model.compile({
//         optimizer: myOptimizer,
//         loss: 'categoricalCrossentropy',
//         metrics: ['accuracy']
//     });

//     modelSummary.innerHTML = '<div>小车自动驾驶模型结构：</div>'

//     model.summary(null, null, x => { modelSummary.innerHTML += '<div>' + x + '</div' });



//     const xs = [];
//     const ys = [];

//     // document.getElementById('warn').innerText = '正在预处理图像数据集，请等待...';

//     const promises = [];

//     for (let category in imageData) {
//         let label = classNames.indexOf(category);

//         if (imageData[category].length === 0) {
//             continue;
//         }
//         // console.log(label);
//         document.getElementById('warn').innerText = '正在处理数据集，准备训练模型，训练时不要切换页面。';


//         for (let imgs of imageData[category]) {
//             promises.push(new Promise((resolve) => {
//                 const img = new Image();
//                 img.src = imgs;

//                 img.onload = () => {
//                     // 创建一个临时 canvas
//                     const tempCanvas = document.createElement('canvas');
//                     tempCanvas.width = img.width;
//                     tempCanvas.height = img.height;
//                     const tempContext = tempCanvas.getContext('2d');
//                     tempContext.drawImage(img, 0, 0);

//                     // 使用 tf.browser.fromPixels 处理临时 canvas
//                     let tensor = preprocessImage(img);
//                     let features = truncatedMobileNet.predict(tensor.expandDims(0));
//                     // console.log(features);
//                     xs.push(features);
//                     ys.push(tf.oneHot(tf.tensor1d([label]).toInt(), numClasses));
//                     // console.log('Tensor:', xs, ys);
//                     tensor.dispose();  // 释放不再需要的张量
//                     resolve();
//                 };
//             }));
//         }
//         await Promise.all(promises);//必须异步，否则 下面的  tf.concat(xs)会出错，因为数据还未下载。
//     }

//     (async () => {
//         const xsTensor = tf.concat(xs);
//         const ysTensor = tf.concat(ys);

//         // 清理中间数组中的张量
//         xs.forEach(tensor => tensor.dispose());
//         ys.forEach(tensor => tensor.dispose());

//         const xsArray = await xsTensor.array();
//         const ysArray = await ysTensor.array();

//         const xsDataset = tf.data.array(xsArray);
//         const ysDataset = tf.data.array(ysArray);

//         const dataset = tf.data.zip({ xs: xsDataset, ys: ysDataset }).shuffle(xsTensor.shape[0]);

//         const trainSize = Math.floor(xsTensor.shape[0] * 0.85);
//         const trainDataset = dataset.take(trainSize).batch(batchSize);
//         const valDataset = dataset.skip(trainSize).batch(batchSize);

//         // 清理不再需要的张量
//         xsTensor.dispose();
//         ysTensor.dispose();

//         // 继续进行模型训练等操作
//         // document.getElementById('warn').innerText = '正在训练模型，请不要切换页面。';
//         const memoryInfo = tf.memory();
//         document.getElementById('warn').innerText = `正在训练模型，请不要切换页面。内存使用: ${bytesToGB(memoryInfo.numBytes)} GB`;
//         const lossContainer = document.getElementById('loss-canvas');
//         const metrics = ['loss', 'val_loss', 'acc', 'val_acc'];
//         const history = [];
//         await model.fitDataset(trainDataset, {
//             epochs: epochs,
//             validationData: valDataset,
//             callbacks: {
//                 onEpochEnd: (epoch, logs) => {
//                     console.log(`Epoch ${epoch + 1}, Loss: ${logs.loss}, Val Loss: ${logs.val_loss}, Accuracy: ${logs.acc}, Val Accuracy: ${logs.val_acc}`);
//                     if (logs.loss === Infinity || logs.val_loss === Infinity) {
//                         console.error('损失函数值为 Infinity，请检查数据和模型设置');
//                     }
//                     document.getElementById('status').innerText = `第 ${epoch + 1} 轮学习, 训练集损失函数: ${logs.loss.toFixed(3)} , 验证集损失函数: ${logs.val_loss.toFixed(3)} , 训练集准确率 = ${logs.acc.toFixed(3)} , 验证集准确率 = ${logs.val_acc.toFixed(3)}`;
//                     history.push(logs);
//                     // tfvis.show.history(lossContainer, history, metrics);
//                     console.log(`内存使用情况: ${bytesToGB(memoryInfo.numBytes)} GB, 张量数量: ${memoryInfo.numTensors}`);
//                     const surface = {
//                         name: '训练过程', tab: '训练', styles: {
//                             width: 400,
//                             height: 600
//                         }
//                     };
//                     const opts = {
//                         xLabel: 'Epoch',
//                         yLabel: 'val',
//                         width: 400,
//                         height: 200
//                     };
//                     // console.log(`tfvis.show.history(${lossContainer}, ${history}, ${metrics}, ${opts});`);
//                     console.log('tfvis.show.history(', lossContainer, history, metrics, opts);
//                     console.log('tfvis');
//                     // tfvis.show.history(surface, history, metrics, opts);
//                     /*! tfvis.show.history(lossContainer, history, metrics, opts);*/
//                 },
//                 onTrainEnd: async () => {
//                     document.getElementById('warn').innerText = '模型训练已完成，模型可下载。';
//                     console.log('Training finished.');
//                     // 清理不再需要的张量
//                     await trainDataset.forEachAsync(item => {
//                         item.xs.dispose();
//                         item.ys.dispose();
//                     });
//                     await valDataset.forEachAsync(item => {
//                         item.xs.dispose();
//                         item.ys.dispose();
//                     });
//                     myOptimizer.dispose();
//                 }
//             }
//         });
//     })();



// }
// function bytesToGB(bytes) {
//     return (bytes / (1024 * 1024 * 1024)).toFixed(2);
// }
async function trainModel() {
    // try {
    // 确保 TensorFlow.js 库已加载
    if (typeof tf === 'undefined') {
        throw new Error('TensorFlow.js 未正确加载');
    }

    tf.engine().startScope();

    // 加载并初始化 truncatedMobileNet
    const truncatedMobileNet = await loadTruncatedMobileNet();
    if (!truncatedMobileNet) {
        throw new Error('truncatedMobileNet 加载失败');
    }

    numClasses = Object.keys(imageData).filter(category => imageData[category].length > 0).length;
    console.log(numClasses);

    if (numClasses < 3) {
        document.getElementById('warn').innerText = '请至少上传 go left right 三个类别的图像数据集。';
        return;
    }

    const learningRate = parseFloat(learningRateInput.value);
    const batchSize = parseInt(batchSizeInput.value);
    const epochs = parseInt(epochsInput.value);
    denseUnits = parseInt(denseUnitsInput.value);
    const validationSplit = parseFloat(validationSplitInput.value);

    // 执行用户自定义代码以初始化模型
    executeCode('codeEditor');
    if (!model) {
        document.getElementById('warn').innerText = '模型初始化失败，请检查代码';
        console.error('模型未初始化，请检查自定义代码');
        return;
    }

    const myOptimizer = tf.train.adam(learningRate);
    model.compile({
        optimizer: myOptimizer,
        loss: 'categoricalCrossentropy',
        metrics: ['accuracy']
    });

    modelSummary.innerHTML = '<div>小车自动驾驶模型结构：</div>';
    model.summary(null, null, x => { modelSummary.innerHTML += `<div>${x}</div>` });

    const xs = [];
    const ys = [];

    document.getElementById('warn').innerText = '正在处理数据集，准备训练模型，训练时不要切换页面。';

    const promises = [];

    for (let category in imageData) {
        let label = classNames.indexOf(category);

        if (imageData[category].length === 0) {
            continue;
        }
        // console.log(label);
        document.getElementById('warn').innerText = '正在处理数据集，准备训练模型，训练时不要切换页面。';


        for (let imgs of imageData[category]) {
            promises.push(new Promise((resolve) => {
                const img = new Image();
                img.src = imgs;

                img.onload = () => {
                    // 创建一个临时 canvas
                    const tempCanvas = document.createElement('canvas');
                    tempCanvas.width = img.width;
                    tempCanvas.height = img.height;
                    const tempContext = tempCanvas.getContext('2d');
                    tempContext.drawImage(img, 0, 0);

                    // 使用 tf.browser.fromPixels 处理临时 canvas
                    let tensor = preprocessImage(img);
                    let features = truncatedMobileNet.predict(tensor.expandDims(0));
                    // console.log(features);
                    xs.push(features);
                    ys.push(tf.oneHot(tf.tensor1d([label]).toInt(), numClasses));
                    // console.log('Tensor:', xs, ys);
                    tensor.dispose();  // 释放不再需要的张量
                    resolve();
                };
            }));
        }
        await Promise.all(promises);//必须异步，否则 下面的  tf.concat(xs)会出错，因为数据还未下载。
    }
    // console.log(xs);
    console.log('数据转换完毕');

    const xsTensor = tf.concat(xs);
    const ysTensor = tf.concat(ys);
    console.log('训练数据集已准备好');

    xs.forEach(tensor => tensor.dispose());
    ys.forEach(tensor => tensor.dispose());

    const xsArray = await xsTensor.array();
    const ysArray = await ysTensor.array();

    const xsDataset = tf.data.array(xsArray);
    const ysDataset = tf.data.array(ysArray);

    const dataset = tf.data.zip({ xs: xsDataset, ys: ysDataset }).shuffle(xsTensor.shape[0]);

    // 使用用户选择的验证集分割比例
    const trainSize = Math.floor(xsTensor.shape[0] * (1 - validationSplit));
    const trainDataset = dataset.take(trainSize).batch(batchSize);
    const valDataset = dataset.skip(trainSize).batch(batchSize);

    xsTensor.dispose();
    ysTensor.dispose();
    console.log(`训练数据集已准备好，训练集:${trainSize}个样本，验证集:${xsTensor.shape[0] - trainSize}个样本，验证集比例:${validationSplit * 100}%`);

    const memoryInfo = tf.memory();
    document.getElementById('warn').innerText = `正在训练模型，请不要切换页面。内存使用: ${bytesToGB(memoryInfo.numBytes)} GB`;
    const lossContainer = document.getElementById('loss-canvas');
    const metrics = ['loss', 'val_loss', 'acc', 'val_acc'];
    const history = [];
    const opts = {
        xLabel: 'Epoch',
        yLabel: 'val',
        width: 400,
        height: 200
    };

    // 显示训练配置信息
    document.getElementById('status').textContent =
        `开始训练模型 - 学习率:${learningRate}, 批量大小:${batchSize}, 训练轮数:${epochs}, 验证集比例:${validationSplit * 100}%`;

    await model.fitDataset(trainDataset, {
        epochs: epochs,
        validationData: valDataset,
        callbacks: {
            onEpochEnd: (epoch, logs) => {
                console.log(`Epoch ${epoch + 1}, Loss: ${logs.loss}, Accuracy: ${logs.acc}`);
                document.getElementById('status').textContent =
                    `第 ${epoch + 1} 轮学习, 训练集损失函数: ${logs.loss.toFixed(3)}, 验证集损失函数: ${logs.val_loss.toFixed(3)}, 训练集准确率: ${logs.acc.toFixed(3)}, 验证集准确率: ${logs.val_acc.toFixed(3)}`;
                history.push(logs);
                // console.log('tfvis.show.history(', lossContainer, history, metrics, opts);
                tfvis.show.history(lossContainer, history, metrics, opts);
            }
        }
    });

    document.getElementById('warn').innerText = '模型训练完成';
    // } catch (error) {
    //     console.error('训练模型时出错: ', error);
    //     document.getElementById('warn').innerText = '训练模型时出错: ' + error.message;
    // } finally {
    tf.engine().endScope();
    // }
}



function bytesToGB(bytes) {
    return (bytes / (1024 * 1024 * 1024)).toFixed(2);
}





async function saveModel() {
    if (model) {
        await model.save('downloads://my-model');
        document.getElementById('status').innerText = '模型已保存';
    } else {
        document.getElementById('warn').innerText = '模型尚未训练，请先训练模型。';
    }
}
async function init() {
    try {
        truncatedMobileNet = await loadTruncatedMobileNet();
    } catch (e) {
        console.log(e);
    }
}
// 保存模型到IndexedDB
const dbName = "tfjs_model_store";
const modelStoreName = "models";
async function openDb() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(dbName, 1);
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            db.createObjectStore(modelStoreName);
        };
        request.onsuccess = (event) => {
            resolve(event.target.result);
        };
        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}
document.getElementById('saveModelIndexedDB').addEventListener('click', async () => {

    try {

        if (model) {
            // await model.save('indexeddb://my-model');
            await model.save(tf.io.withSaveHandler(async (artifacts) => {
                const db = await openDb(dbName, 1);
                const transaction = db.transaction([modelStoreName], 'readwrite');
                const store = transaction.objectStore(modelStoreName);
                // Construct the expected model JSON format
                const modelJson = JSON.stringify({
                    modelTopology: artifacts.modelTopology,
                    format: 'layers-model',
                    generatedBy: `TensorFlow.js tfjs-layers v${tf.version.tfjs}`,
                    convertedBy: null,
                    weightsManifest: [{
                        paths: ['my-model.weights.bin'],
                        weights: artifacts.weightSpecs
                    }]
                });
                store.put(modelJson, 'my-model.json');
                store.put(artifacts.weightData, 'my-model.weights.bin');
                transaction.oncomplete = () => {
                    // document.getElementById('warn').innerText = '模型已保存在当前浏览器缓存。';
                    var warnElement = document.getElementById('warn');
                    // 创建链接元素
                    var linkElement = document.createElement('a');
                    linkElement.href = 'https://www.sjaiedu.site/aicar/predict/';
                    linkElement.textContent = '模型已保存在当前浏览器缓存,可点击此链接在当前浏览器中访问模型部署页面';
                    linkElement.target = '_blank'; // 在新窗口打开链接
                    // 将链接元素添加到页面中
                    warnElement.innerHTML = ''; // 清空原有内容
                    warnElement.appendChild(linkElement);


                };
                transaction.onerror = (event) => {
                    document.getElementById('warn').innerText = '模型缓存失败。';
                    console.error('Error saving model to IndexedDB:', event.target.error);
                };
            }));
            // document.getElementById('warn').innerText = '模型已保存在浏览器缓存。';
            // 获取元素
        } else {
            document.getElementById('warn').innerText = '模型尚未训练，请先训练模型。';
        }

    } catch (e) {
        document.getElementById('warn').innerText = '模型尚未训练，请先训练模型。';
        console.log(e);

    }
})
init();
//////////////////////////////////////////////////////////////////
const addGoImageButton = document.getElementById('addGoImage');
const clearGoImagesButton = document.getElementById('clearGoImages');
const addLeftImageButton = document.getElementById('addLeftImage');
const clearLeftImagesButton = document.getElementById('clearLeftImages');
const addRightImageButton = document.getElementById('addRightImage');
const clearRightImagesButton = document.getElementById('clearRightImages');
const addStopImageButton = document.getElementById('addStopImage');
const clearStopImagesButton = document.getElementById('clearStopImages');

// 为按钮添加点击事件监听器
addGoImageButton.addEventListener('click', () => {
    triggerFileInput('fileElemGo');
});
addLeftImageButton.addEventListener('click', () => {
    triggerFileInput('fileElemLeft');
});
addRightImageButton.addEventListener('click', () => {
    triggerFileInput('fileElemRight');
});
addStopImageButton.addEventListener('click', () => {
    triggerFileInput('fileElemStop');
});

function triggerFileInput(inputId) {
    document.getElementById(inputId).click();
}

document.getElementById('fileElemGo').addEventListener('change', function () {
    handleFiles(this.files, 'go');
});
document.getElementById('fileElemLeft').addEventListener('change', function () {
    handleFiles(this.files, 'left');
});
document.getElementById('fileElemRight').addEventListener('change', function () {
    handleFiles(this.files, 'right');
});
document.getElementById('fileElemStop').addEventListener('change', function () {
    handleFiles(this.files, 'stop');
});

function handleFiles(files, category) {
    let preview = document.getElementById(`${category}Images`);
    let count = document.getElementById(`count-${category}`);
    for (let file of files) {
        let reader = new FileReader();
        reader.onload = function (event) {
            let img = document.createElement('img');
            img.src = event.target.result; // 设置图片的src为读取到的文件内容，即Data URL
            img.onclick = function () {
                deleteImage(category, img);
            };

            // preview.appendChild(img);
            // imageData[category].push(img.src);
            // Insert the image at the beginning of the preview area
            if (preview.firstChild) {
                preview.insertBefore(img, preview.firstChild);
            } else {
                preview.appendChild(img);
            }
            imageData[category].unshift(img.src); // 将 Data URL base64格式图片 插入到数组的开头位置
            count.innerText = `样本数: ${imageData[category].length}`;
            updateCount();
        }
        // console.log('已上传一张图片');
        reader.readAsDataURL(file);
    }
}
function deleteImage(category, img) {
    let preview = document.getElementById(`${category}Images`);
    let count = document.getElementById(`count-${category}`);

    // 从 DOM 中移除图片
    preview.removeChild(img);

    let index = imageData[category].indexOf(img.src);//img.src 是base64格式图片
    if (index > -1) {
        imageData[category].splice(index, 1);
    }

    // 更新计数
    count.innerText = `样本数: ${imageData[category].length}`;
    updateCount();
}
//////////////////////////////////////////////////////////////



// 此脚本用于加载dataset目录中的图片到相应的预览容器中
// 由于浏览器安全限制，直接访问本地文件系统需要FileSystem API或Node.js服务器支持

// 定义全局函数用于加载默认数据集


// 定义全局函数用于加载数据集2


// 更新numClasses变量，反映当前实际拥有数据的类别数
function updateNumClasses() {
    if (typeof numClasses !== 'undefined') {
        const classesWithData = Object.keys(imageData).filter(category =>
            imageData[category] && imageData[category].length > 0
        ).length;

        if (classesWithData > 0) {
            numClasses = classesWithData;
            console.log(`更新 numClasses = ${classesWithData}`);
        }
    }
}

// 添加导入数据集压缩文件功能
function initializeImportDataset() {
    // 创建上传按钮
    const importButton = document.getElementById('importDataset');
    if (!importButton) {
        console.error('找不到importDataset按钮');
        return;
    }

    // 创建隐藏的文件输入
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.id = 'importDatasetInput';
    fileInput.accept = '.zip';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);

    // 为导入按钮添加点击事件
    importButton.addEventListener('click', () => {
        fileInput.click();
    });

    // 为文件输入添加change事件
    fileInput.addEventListener('change', async (event) => {
        if (event.target.files.length === 0) return;

        const file = event.target.files[0];
        if (file.type !== 'application/zip' && !file.name.endsWith('.zip')) {
            document.getElementById('warn').textContent = '请上传ZIP格式的数据集文件';
            return;
        }

        document.getElementById('status').textContent = '正在导入数据集...';

        try {
            await importDatasetFromZip(file);
            document.getElementById('status').textContent = '数据集导入成功！';
        } catch (error) {
            console.error('导入数据集时出错:', error);
            document.getElementById('warn').textContent = `导入数据集失败: ${error.message}`;
        }
    });
}

// 从ZIP文件导入数据集
async function importDatasetFromZip(file) {
    const zip = await JSZip.loadAsync(file);
    const categories = ['go', 'left', 'right', 'stop'];
    const importedCounts = {};
    let totalImported = 0;

    // 处理每个类别
    for (const category of categories) {
        importedCounts[category] = 0;
        const folderPath = category + '/';
        const folderFiles = Object.keys(zip.files).filter(filename =>
            filename.startsWith(folderPath) &&
            !filename.endsWith('/') &&
            (filename.endsWith('.png') || filename.endsWith('.jpg') || filename.endsWith('.jpeg'))
        );

        if (folderFiles.length === 0) {
            console.log(`ZIP文件中未找到${category}类别的图片`);
            continue;
        }

        // 确保imageData数组已初始化
        initializeDirection(category);

        // 获取预览容器
        const preview = document.getElementById(`${category}Images`);
        const count = document.getElementById(`count-${category}`);

        // 获取已经加载的图片的URL列表，避免重复加载
        const existingUrls = Array.from(preview.querySelectorAll('img')).map(img => img.src);

        // 设置加载状态
        document.getElementById('warn').textContent = `正在导入${category}类别的图片...`;

        // 处理每个文件
        for (let i = 0; i < folderFiles.length; i++) {
            const filename = folderFiles[i];
            try {
                // 读取文件内容为ArrayBuffer
                const imageDataBuffer = await zip.file(filename).async('arraybuffer');

                // 将ArrayBuffer转换为Blob
                const blob = new Blob([imageDataBuffer], { type: 'image/png' });

                // 创建URL
                const imageUrl = URL.createObjectURL(blob);

                // 创建图像元素
                const img = document.createElement('img');

                // 等待图像加载完成
                await new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = () => reject(new Error(`无法加载图片: ${filename}`));
                    img.src = imageUrl;
                    img.alt = `${category} image`;
                    img.title = '点击图片可以删除';
                });

                // 创建Canvas并转为合适的格式
                const canvas = document.createElement('canvas');
                canvas.width = img.width;
                canvas.height = img.height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0);
                const dataUrl = canvas.toDataURL('image/png');

                // 释放原始Blob URL
                URL.revokeObjectURL(imageUrl);

                // 检查是否已存在相同图片
                if (existingUrls.includes(dataUrl)) {
                    continue; // 跳过已存在的图片
                }

                // 创建新的图像元素用于显示
                const displayImg = document.createElement('img');
                displayImg.src = dataUrl;
                displayImg.alt = `${category} image`;
                displayImg.title = '点击图片可以删除';

                // 添加点击删除功能
                displayImg.addEventListener('click', function () {
                    const index = imageData[category].indexOf(this.src);
                    if (index > -1) {
                        imageData[category].splice(index, 1);
                    }
                    this.remove();
                    count.innerText = `样本数: ${imageData[category].length}`;
                    totalImagesCount--;
                    updateCount();
                });

                // 添加到预览容器
                if (preview.firstChild) {
                    preview.insertBefore(displayImg, preview.firstChild);
                } else {
                    preview.appendChild(displayImg);
                }

                // 添加到数据数组
                imageData[category].push(dataUrl);
                importedCounts[category]++;
                totalImported++;

                // 更新UI，每10张图片更新一次
                if (i % 10 === 0 || i === folderFiles.length - 1) {
                    count.innerText = `样本数: ${imageData[category].length}`;
                    document.getElementById('status').textContent =
                        `正在导入数据集...已导入 ${totalImported} 张图片`;
                }
            } catch (error) {
                console.error(`处理文件 ${filename} 时出错:`, error);
                // 继续处理下一个文件
            }
        }

        // 更新该类别的计数
        count.innerText = `样本数: ${imageData[category].length}`;
    }

    // 更新总计数
    totalImagesCount = Object.values(imageData).reduce((sum, arr) => sum + arr.length, 0);
    updateCount();

    // 显示导入结果
    const importSummary = categories
        .map(category => `${category}: ${importedCounts[category]}张`)
        .join(', ');

    document.getElementById('warn').textContent =
        `数据集导入成功！共导入 ${totalImported} 张图片 (${importSummary})`;
} 