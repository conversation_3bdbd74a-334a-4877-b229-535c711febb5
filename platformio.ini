[env:esp32-s3-devkitc-1]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino

; 配置分区表
board_build.partitions = partitions.csv

; 启用PSRAM支持 - 针对8MB PSRAM优化
board_build.arduino.memory_type = qio_opi
board_build.flash_mode = qio
board_build.psram_type = opi

; 串口配置
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; 构建标志 - 优化PSRAM配置
build_flags = 
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DCORE_DEBUG_LEVEL=1
    -DCAMERA_MODEL_XIAO_ESP32S3
    -DBOARD_HAS_PSRAM
    -DPSRAM_IC_APS6404
    -DPSRAM_SPEED=80
    -DPSRAM_MODE=1
    -DCONFIG_SPIRAM_SUPPORT=1
    -DCONFIG_SPIRAM_USE_CAPS_ALLOC=1
    -DCONFIG_SPIRAM_TRY_ALLOCATE_WIFI_LWIP=1

; 库依赖
lib_deps = 
    gilmaimon/ArduinoWebsockets@^0.5.3
    ; esp32 camera libraries are included in the platform
    ; WiFi, ESPmDNS, Preferences, Wire are included in ESP32 framework

; 文件系统配置
board_build.filesystem = littlefs

; 上传配置
upload_speed = 921600
upload_port = COM*

; 高级构建选项
board_build.f_cpu = 240000000L
board_build.f_flash = 80000000L

; ===== Seeed Studio XIAO ESP32S3 Sense 专用配置 =====
[env:seeed_xiao_esp32s3_sense]
platform = espressif32
board = seeed_xiao_esp32s3
framework = arduino

; 配置分区表 - 针对8MB Flash优化
board_build.partitions = partitions.csv

; 启用PSRAM支持 - 针对8MB PSRAM优化
board_build.arduino.memory_type = qio_opi
board_build.flash_mode = qio
board_build.psram_type = opi

; 串口配置
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; 构建标志 - 针对XIAO ESP32S3 Sense优化
build_flags = 
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DCORE_DEBUG_LEVEL=1
    -DCAMERA_MODEL_XIAO_ESP32S3
    -DBOARD_HAS_PSRAM
    -DPSRAM_IC_APS6404
    -DPSRAM_SPEED=80
    -DPSRAM_MODE=1
    -DCONFIG_SPIRAM_SUPPORT=1
    -DCONFIG_SPIRAM_USE_CAPS_ALLOC=1
    -DCONFIG_SPIRAM_TRY_ALLOCATE_WIFI_LWIP=1
    -DCONFIG_ESP32S3_DEFAULT_CPU_FREQ_240=1

; 库依赖
lib_deps = 
    gilmaimon/ArduinoWebsockets@^0.5.3
    ; esp32 camera libraries are included in the platform
    ; WiFi, ESPmDNS, Preferences, Wire are included in ESP32 framework

; 文件系统配置
board_build.filesystem = littlefs

; 上传配置
upload_speed = 921600
upload_port = COM*

; 高级构建选项 - 针对XIAO ESP32S3优化
board_build.f_cpu = 240000000L
board_build.f_flash = 80000000L

; ===== ESP32-S3-EYE 开发板专用配置 =====
[env:esp32s3_eye]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino

; 配置分区表 - 针对16MB Flash优化
board_build.partitions = partitions.csv

; 启用PSRAM支持 - 使用稳定配置
board_build.arduino.memory_type = qio_opi
board_build.flash_mode = qio
board_build.psram_type = opi

; 串口配置
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; 构建标志 - 针对ESP32-S3-EYE优化
build_flags =
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DCORE_DEBUG_LEVEL=1
    -DCAMERA_MODEL_ESP32S3_EYE
    -DBOARD_HAS_PSRAM
    -DPSRAM_IC_APS6404
    -DPSRAM_SPEED=80
    -DPSRAM_MODE=1
    -DCONFIG_SPIRAM_SUPPORT=1
    -DCONFIG_SPIRAM_USE_CAPS_ALLOC=1
    -DCONFIG_SPIRAM_TRY_ALLOCATE_WIFI_LWIP=1
    -DCONFIG_ESP32S3_DEFAULT_CPU_FREQ_240=1
    -DFORMAT_LITTLEFS_IF_FAILED=true
    -DCONFIG_LITTLEFS_FOR_IDF_3_2=1

; 库依赖
lib_deps =
    gilmaimon/ArduinoWebsockets@^0.5.3
    ; esp32 camera libraries are included in the platform
    ; WiFi, ESPmDNS, Preferences, Wire are included in ESP32 framework

; 文件系统配置
board_build.filesystem = littlefs

; 上传配置 - 针对稳定性优化（解决文件系统损坏问题）
upload_speed = 115200
upload_port = COM*
upload_resetmethod = hard_reset

; 高级构建选项 - 针对ESP32-S3-EYE优化
board_build.f_cpu = 240000000L
board_build.f_flash = 80000000L