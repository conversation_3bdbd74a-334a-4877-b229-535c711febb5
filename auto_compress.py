#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动文件压缩脚本
在PlatformIO构建时自动压缩文件
"""

import os
import gzip
import shutil
from pathlib import Path

def compress_file(src_path, dst_path=None):
    """压缩单个文件"""
    if dst_path is None:
        dst_path = src_path + '.gz'
    
    src_path = Path(src_path)
    dst_path = Path(dst_path)
    
    if not src_path.exists():
        return False
    
    try:
        with open(src_path, 'rb') as f_in:
            content = f_in.read()
        
        with gzip.open(dst_path, 'wb') as f_out:
            f_out.write(content)
        
        return True
    except:
        return False

def main():
    """主函数"""
    files_to_compress = [
        "data/js/tfjs-vis.js",
        "data/js/jquery-3.6.0.min.js",
        "data/js/jszip.min.js", 
        "data/css/bootstrap.min.css"
    ]
    
    for file_path in files_to_compress:
        if os.path.exists(file_path):
            compress_file(file_path)

if __name__ == "__main__":
    main()
