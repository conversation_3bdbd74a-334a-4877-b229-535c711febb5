# ESP32-S3 Camera LCD显示功能

本项目为DFrobot K10 ESP32-S3摄像头项目添加了2.8寸240x320彩色LCD显示屏支持，使用ILI9341驱动芯片。

## 硬件连接

LCD显示屏使用SPI接口连接，引脚配置如下：

| LCD引脚 | ESP32-S3引脚 | 功能 |
|---------|-------------|------|
| VCC     | 3.3V        | 电源 |
| GND     | GND         | 地   |
| SCLK    | GPIO 12     | SPI时钟 |
| MOSI    | GPIO 21     | SPI数据 |
| DC      | GPIO 13     | 数据/命令选择 |
| CS      | GPIO 14     | 片选 |
| RST     | 未连接      | 复位（内部复位） |

## 功能特性

### 1. 实时摄像头画面显示
- 自动缩放摄像头画面以适应LCD屏幕
- 支持多种摄像头格式：RGB565、YUV422、GRAYSCALE
- 保持画面宽高比，居中显示

### 2. 状态信息显示
- IP地址显示
- WiFi连接状态
- WebSocket连接状态
- 实时帧率显示
- 系统状态信息

### 3. 显示布局
```
┌─────────────────────────────────┐
│       摄像头画面区域              │
│        (240x240)                │
│                                 │
│                                 │
│                                 │
│                                 │
├─────────────────────────────────┤
│ FPS: 25                         │
│ Connected/Disconnected          │
│ IP: *************              │
│ Status: Ready                   │
└─────────────────────────────────┘
```

## 软件配置

### 1. 库依赖
项目已添加LovyanGFX库支持：
```ini
lib_deps = 
    lovyan03/LovyanGFX@^1.1.8
```

### 2. 初始化流程
1. LCD硬件初始化
2. 显示启动画面
3. 运行LCD功能测试
4. 开始实时显示

### 3. 摄像头格式优化
- 优先使用RGB565格式，与LCD格式匹配
- 自动降级到YUV422或GRAYSCALE格式
- 实时格式转换和缩放

## 使用方法

### 1. 编译和上传
```bash
# 使用PlatformIO构建
pio run

# 上传到ESP32-S3
pio run --target upload

# 查看串口输出
pio device monitor
```

### 2. 启动过程
1. 系统启动后会自动初始化LCD
2. 显示启动画面和测试图案
3. 连接WiFi后显示IP地址
4. 开始实时摄像头画面显示

### 3. 状态监控
- 串口监视器显示详细状态信息
- LCD屏幕显示关键状态信息
- 实时帧率和连接状态更新

## 故障排除

### LCD不显示
1. 检查硬件连接
2. 确认SPI引脚配置正确
3. 检查电源供应
4. 查看串口错误信息

### 画面显示异常
1. 检查摄像头格式设置
2. 确认缩放算法正确
3. 检查内存分配
4. 调整显示参数

### 性能问题
1. 降低摄像头分辨率
2. 减少帧率
3. 优化缩放算法
4. 检查内存使用情况

## 技术细节

### 1. 显示驱动
- 使用LovyanGFX库
- 支持硬件加速
- 优化的SPI通信

### 2. 图像处理
- 实时格式转换
- 双线性缩放
- 内存优化

### 3. 性能优化
- 使用PSRAM存储大图像
- 异步显示更新
- 智能内存管理

## 扩展功能

### 1. 可添加的功能
- 触摸屏支持
- 菜单界面
- 参数调节
- 录制功能

### 2. 自定义配置
- 修改显示布局
- 调整颜色主题
- 添加更多状态信息
- 自定义测试图案

## 注意事项

1. LCD和摄像头可能共享SPI总线，注意时序控制
2. 高分辨率摄像头可能影响显示性能
3. 确保足够的PSRAM用于图像处理
4. 定期检查内存使用情况

## 更新日志

### v1.0.0
- 初始LCD支持
- 实时摄像头显示
- 状态信息显示
- 基本测试功能 