// ESP32-S3 AI自动驾驶小车 - JavaScript应用
// 包含WebSocket摄像头流、蓝牙通信和AI分类功能

///////////////////////////////////////////////////////////////
// WebSocket摄像头流支持
let wsCamera = null;
let wsConnected = false;
let reconnectInterval = 3000; // 5秒重连间隔
let wsReconnectTimer = null;
let connectionAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 3;
let usingFallbackStream = false;

// UI元素
const statusElement = document.getElementById('status');
const consoleElement = document.getElementById('console');
const kValueInput = document.getElementById('k-value');
const kValueDisplay = document.getElementById('k-value-display');
const sendCommandToggle = document.getElementById('send-command-toggle');
const toggleStatus = document.getElementById('toggle-status');
const imgElement = document.getElementById('img');

// 全局变量
let sendCommandsEnabled = false; // 是否发送命令到小车的标志

// 安全地执行函数，捕获并记录错误但不抛出
function safeExecute(fn, ...args) {
    try {
        return fn(...args);
    } catch (error) {
        console.log(`执行函数 ${fn.name || '匿名函数'} 时出错:`, error);
        return null;
    }
}

// 安全地执行蓝牙命令的辅助函数（带重试和超时）
function safelyExecuteBleCommand(command) {
    // 如果未连接，直接返回
    if (!bleSerial.isConnected()) {
        console.log('设备未连接，无法发送命令');
        return Promise.resolve(false);
    }

    // 如果正在发送，先延迟100ms后再尝试
    if (bleSerial.sendInProgress) {
        console.log('命令正在发送中，稍后重试...');
        return new Promise((resolve) => {
            setTimeout(() => {
                // 重试一次
                if (!bleSerial.sendInProgress && bleSerial.isConnected()) {
                    const data = bleSerialBroadcastCmd(command);
                    bleSerial.write_data(data)
                        .then(() => resolve(true))
                        .catch(err => {
                            console.warn("重试发送命令失败:", err);
                            resolve(false);
                        });
                } else {
                    console.log('设备仍在发送或已断开，跳过命令');
                    resolve(false);
                }
            }, 100);
        });
    }

    // 正常情况，直接发送
    const data = bleSerialBroadcastCmd(command);
    return bleSerial.write_data(data)
        .then(() => {
            // console.log(`${command} 命令已发送`);
            return true;
        })
        .catch(err => {
            console.warn(`发送 ${command} 命令失败:`, err);
            return false;
        });
}

// 发送命令的通用方法
async function sendCommand(cmd) {
    // 首先检查是否允许发送命令
    if (!sendCommandsEnabled) {
        // console.log(`命令发送已禁用，跳过发送: ${cmd}`);
        return true; // 返回成功，但实际并未发送
    }

    // 如果允许发送，使用安全的命令发送函数
    return safelyExecuteBleCommand(cmd);
}

// 更新开关状态显示
function updateToggleState() {
    const statusElement = document.getElementById('toggle-status');

    if (sendCommandsEnabled) {
        statusElement.innerText = '已启用';
        statusElement.style.color = '#4CAF50'; // 绿色
        document.getElementById('status').innerHTML = '已启用发送指令到小车';
    } else {
        statusElement.innerText = '已禁用';
        statusElement.style.color = '#F44336'; // 红色
        document.getElementById('status').innerHTML = '已禁用发送指令，指令不会发送给小车';
    }

    console.log(`命令发送状态: ${sendCommandsEnabled ? '启用' : '禁用'}`);
}

// 连接到WebSocket摄像头服务器
function connectCameraWebSocket() {
    if (wsReconnectTimer) {
        clearTimeout(wsReconnectTimer);
        wsReconnectTimer = null;
    }
    if (connectionAttempts >= MAX_RECONNECT_ATTEMPTS) {
        console.log('WebSocket重连次数过多，切换到HTTP流');
        switchToHttpStream();
        return;
    }
    const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
    const wsUrl = `${protocol}${window.location.hostname}:81`;
    console.log(`正在连接WebSocket摄像头: ${wsUrl} (尝试 ${connectionAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`);
    statusElement.innerText = `状态: 正在连接摄像头WebSocket... (${connectionAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`;
    try {
        wsCamera = new WebSocket(wsUrl);
        const connectionTimeout = setTimeout(() => {
            if (wsCamera && wsCamera.readyState !== WebSocket.OPEN) {
                console.log('WebSocket连接超时');
                wsCamera.close();
                connectionAttempts++;
                retry();
            }
        }, 3000);
        wsCamera.onopen = function () {
            clearTimeout(connectionTimeout);
            console.log('摄像头WebSocket连接成功');
            statusElement.innerText = "状态: 摄像头WebSocket已连接";
            wsConnected = true;
            connectionAttempts = 0;
            wsCamera.send('requestStream');
        };
        wsCamera.onclose = function (event) {
            clearTimeout(connectionTimeout);
            console.log(`摄像头WebSocket连接关闭: 代码=${event.code}, 原因=${event.reason}`);
            wsConnected = false;
            if (window.lastImageUrl) {
                URL.revokeObjectURL(window.lastImageUrl);
                window.lastImageUrl = null;
            }
            if (event.code !== 1000 && event.code !== 1001) {
                connectionAttempts++;
            }
            retry();
        };
        wsCamera.onerror = function (error) {
            clearTimeout(connectionTimeout);
            console.error('摄像头WebSocket错误:', error);
        };
        wsCamera.onmessage = function (event) {
            if (event.data instanceof Blob) {
                const imageUrl = URL.createObjectURL(event.data);
                imgElement.onload = function () {
                    if (window.lastImageUrl) {
                        URL.revokeObjectURL(window.lastImageUrl);
                    }
                };
                imgElement.src = imageUrl;
                window.lastImageUrl = imageUrl;
            }
        };
    } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        statusElement.innerText = "状态: WebSocket连接失败";
        connectionAttempts++;
        retry();
    }
    function retry() {
        if (connectionAttempts >= MAX_RECONNECT_ATTEMPTS) {
            console.log('WebSocket重连次数过多，切换到HTTP流');
            switchToHttpStream();
            return;
        }
        const retryDelay = reconnectInterval * Math.min(connectionAttempts, 3);
        statusElement.innerText = `状态: 摄像头WebSocket断开，${retryDelay / 1000}秒后重连...`;
        wsReconnectTimer = setTimeout(function () {
            console.log(`尝试重新连接摄像头WebSocket... (${connectionAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`);
            connectCameraWebSocket();
        }, retryDelay);
    }
}

// 切换到传统HTTP流作为备用方案
function switchToHttpStream() {
    usingFallbackStream = true;
    statusElement.innerText = "状态: 使用HTTP流作为备用 (WebSocket连接失败)";
    imgElement.onerror = function () {
        console.log('HTTP流加载失败，3秒后重试');
        setTimeout(function () {
            imgElement.src = `/stream?t=${new Date().getTime()}`;
        }, 3000);
    };
    imgElement.onload = function () {
        statusElement.innerText = "状态: HTTP流连接成功 (备用模式)";
    };
    imgElement.src = `/stream?t=${new Date().getTime()}`;
    setInterval(function () {
        if (usingFallbackStream) {
            imgElement.src = `/stream?t=${new Date().getTime()}`;
        }
    }, 30000);
}

// 页面卸载时，关闭WebSocket连接
window.addEventListener('beforeunload', function () {
    if (wsCamera && wsConnected) {
        wsCamera.send('stopStream');
        wsCamera.close();
    }
});
window.addEventListener('online', function () {
    console.log('网络已恢复连接，尝试重新连接WebSocket');
    if (!wsConnected && !usingFallbackStream) {
        connectionAttempts = 0;
        connectCameraWebSocket();
    }
});
window.addEventListener('offline', function () {
    console.log('网络连接已断开');
    statusElement.innerText = "状态: 网络连接已断开";
});

///////////////////////////////////////////////////////////////
// BLE Support
const MICROBLOCKS_SERVICE_UUID = 'bb37a001-b922-4018-8e74-e14824b3a638';
const MICROBLOCKS_RX_CHAR_UUID = 'bb37a002-b922-4018-8e74-e14824b3a638';
const MICROBLOCKS_TX_CHAR_UUID = 'bb37a003-b922-4018-8e74-e14824b3a638';
const BLE_PACKET_LEN = 240;

// 兼容ESP32的其他可能的蓝牙UUID
const ESP32_SERVICE_UUIDS = [
    MICROBLOCKS_SERVICE_UUID
];

// 蓝牙通信参数
const BLE_RECONNECT_DELAY = 1000; // 重连等待时间
const BLE_CONNECTION_TIMEOUT = 10000; // 连接超时时间
const BLE_MAX_RETRIES = 3; // 最大重试次数
const DISCONNECT_DELAY = 500; // 断开连接前的等待时间

// 全局变量
const GP_serialInputBuffers = [];
let deviceCache = null; // 缓存已连接的设备
let isDisconnecting = false; // 正在断开连接的标志
let deviceIsConnected = false;

function bleSerialBroadcastCmd(str) {
    const encoder = new TextEncoder();
    const data = encoder.encode(str);
    let length = data.length + 1;
    return new Uint8Array([251, 27, 0, length % 256, Math.floor(length / 256), ...data, 254]);
}

// 清除缓存的设备信息
function clearDeviceCache() {
    deviceCache = null;
}

// 等待指定的毫秒数
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

class NimBLESerial {
    constructor() {
        this.device = null;
        this.server = null;
        this.service = null;
        this.rx_char = null;
        this.tx_char = null;
        this.connected = false;
        this.sendInProgress = false;
        this.deviceName = "未知设备"; // 设备名称
        this.disconnectRequested = false; // 标记是否为用户请求的断开连接
        this.reconnectAttempts = 0; // 重连尝试次数
        this.notificationsActive = false; // 通知状态标记
        this.isDisconnecting = false;     // 正在断开连接的标记

        // 绑定this上下文
        this.handle_disconnected = this.handle_disconnected.bind(this);
        this.handle_read = this.handle_read.bind(this);
        this.connect = this.connect.bind(this);
        this.reconnect = this.reconnect.bind(this);

    }

    handle_disconnected(event) {
        console.log("BLE断开连接事件触发");

        // 如果已经在处理断开连接，无需重复处理
        if (isDisconnecting) {
            console.log("断开连接过程已在进行中，跳过重复处理");
            return;
        }

        this.cleanupConnection();

        // 更新UI
        this.updateUI('蓝牙设备已断开连接');

        console.log("已处理断开连接事件，UI已更新");

        // 如果不是用户手动断开，尝试自动重连
        if (!this.disconnectRequested && this.device) {
            console.log("非用户请求断开，将尝试重连");
            // 设备存在但断开了，尝试重连
            setTimeout(() => this.reconnect(), BLE_RECONNECT_DELAY);
        }
    }

    // 清理连接资源的通用方法
    cleanupConnection(clearDevice = true) {
        // // 清理通知
        // if (this.tx_char && this.notificationsActive) {
        //     try {
        //         // 我们不等待这个操作完成，因为可能已经断开连接
        //         safeExecute(() => {
        //             this.tx_char.removeEventListener('characteristicvaluechanged', this.handle_read);
        //         });
        //     } catch (e) {
        //         console.log('清理通知监听器失败，但这是预期的:', e);
        //     }
        // }

        // // 清理状态
        // this.server = null;
        // this.service = null;
        // this.rx_char = null;
        // this.tx_char = null;
        // this.connected = false;
        // this.sendInProgress = false;
        // this.notificationsActive = false;
        // deviceIsConnected = false;

        // // 是否需要清除设备引用
        // if (clearDevice) {
        //     this.device = null;
        // }
        // 清理通知监听器
        if (this.tx_char && this.notificationsActive) {
            try {
                this.tx_char.removeEventListener('characteristicvaluechanged', this.handle_read);
            } catch (e) {
                console.log('清理通知监听器失败，这是预期内的:', e);
            }
        }

        // 重置状态
        this.server = null;
        this.service = null;
        this.rx_char = null;
        this.tx_char = null;
        this.connected = false;
        this.sendInProgress = false;
        this.notificationsActive = false;

    }

    handle_read(event) {
        let data = new Uint8Array(event.target.value.buffer);
        GP_serialInputBuffers.push(data);

        // 显示接收到的数据（用于调试）
        // console.log(event.target.value);
        const value = new TextDecoder().decode(event.target.value);
        const lettersOnly = value.replace(/[^A-Za-z]/g, '');
        // console.log(`收到数据: ${lettersOnly}`);
    }

    // 集中更新UI状态
    updateUI(message, enableConnect = true, enableDisconnect = false) {
        document.getElementById('status').innerHTML = message;
        document.getElementById('connBle').disabled = !enableConnect;
        document.getElementById('disconnBle').disabled = !enableDisconnect;
    }

    // 尝试重新连接到设备
    async connectToDevice(device) {
        console.log("尝试连接到GATT服务器...");

        // 使用超时Promise包装连接过程
        try {
            // 先执行一些清理工作
            this.cleanupConnection(false);

            // 如果最近刚断开连接，增加一个短暂的延迟
            if (window.lastBleDisconnectTime && (new Date().getTime() - window.lastBleDisconnectTime) < 1500) {
                console.log("设备最近刚断开，增加延迟以确保蓝牙堆栈准备好");
                await sleep(1500);
            }

            const connectWithTimeout = Promise.race([
                device.gatt.connect(),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('连接超时')), BLE_CONNECTION_TIMEOUT)
                )
            ]);

            this.server = await connectWithTimeout;
            console.log("GATT服务器连接成功");

            // 增加一个短暂的延迟，让设备有时间准备服务
            await sleep(500);

            // 尝试获取所有已知服务
            let serviceFound = false;
            let serviceError = null;

            for (const serviceUuid of ESP32_SERVICE_UUIDS) {
                try {
                    // 尝试获取服务
                    this.service = await this.server.getPrimaryService(serviceUuid);
                    if (this.service) {
                        console.log(`找到服务: ${serviceUuid}`);
                        serviceFound = true;
                        break;
                    }
                } catch (e) {
                    console.log(`服务 ${serviceUuid} 不可用: ${e.message}`);
                    serviceError = e;

                    // 如果失败是因为设备没有准备好，等待一会再试
                    if (e.message.includes('没有找到') || e.message.includes('not found')) {
                        await sleep(800);
                        try {
                            this.service = await this.server.getPrimaryService(serviceUuid);
                            if (this.service) {
                                console.log(`第二次尝试找到服务: ${serviceUuid}`);
                                serviceFound = true;
                                break;
                            }
                        } catch (e2) {
                            console.log(`第二次尝试获取服务 ${serviceUuid} 仍然失败: ${e2.message}`);
                        }
                    }
                }
            }

            if (!serviceFound) {
                const errorMsg = serviceError ?
                    `找不到任何所需的蓝牙服务: ${serviceError.message}` :
                    "找不到任何所需的蓝牙服务";
                throw new Error(errorMsg);
            }

            console.log("服务发现成功");

            this.tx_char = await this.service.getCharacteristic(MICROBLOCKS_TX_CHAR_UUID);
            this.rx_char = await this.service.getCharacteristic(MICROBLOCKS_RX_CHAR_UUID);
            console.log("特征值获取成功");

            // 确保之前的监听器被移除
            try {
                this.tx_char.removeEventListener('characteristicvaluechanged', this.handle_read);
            } catch (e) {
                // 忽略错误
            }

            await this.tx_char.startNotifications();
            this.tx_char.addEventListener("characteristicvaluechanged", this.handle_read);
            this.notificationsActive = true;

            this.connected = true;
            deviceIsConnected = true;
            this.sendInProgress = false;
            this.reconnectAttempts = 0; // 重置重连计数

            console.log("蓝牙连接成功");

            // 更新UI
            this.updateUI('已连接蓝牙设备: ' + device.name, false, true);

            // 更新设备缓存
            deviceCache = device;

            // 确保标记为非断开状态
            isDisconnecting = false;

            // 发送一个stop命令测试连接稳定性
            try {
                const stopData = bleSerialBroadcastCmd('stop');
                await this.write_data(stopData);
                console.log("连接测试成功");
            } catch (e) {
                console.warn("连接测试命令失败:", e);
            }

            return true;
        } catch (error) {
            console.error('连接到GATT服务器失败:', error);
            this.cleanupConnection();
            throw error;
        }
    }

    // 主连接方法
    async connect() {
        try {
            this.disconnectRequested = false;

            // 首先尝试使用当前实例的设备进行重连
            if (this.device) {
                console.log('尝试连接到之前的设备:', this.device.name || '未知设备');
                try {
                    return await this.connectToDevice(this.device);
                } catch (error) {
                    console.log('之前设备连接失败，尝试使用缓存或发现新设备:', error);
                    // 不清除设备引用，可能在某些浏览器中可以重用
                }
            }

            // 尝试使用缓存的设备进行重连
            if (deviceCache) {
                console.log('尝试连接到缓存的设备:', deviceCache.name || '未知设备');
                try {
                    // 更新当前实例的设备引用
                    this.device = deviceCache;
                    return await this.connectToDevice(deviceCache);
                } catch (error) {
                    console.log('缓存设备连接失败，尝试发现新设备:', error);
                    clearDeviceCache(); // 清除已失效的缓存
                }
            }

            // 请求新的蓝牙设备
            const requestOptions = {
                // 使用多个过滤器来提高设备发现率
                filters: [
                    // 主要通过服务UUID过滤
                    { services: [MICROBLOCKS_SERVICE_UUID] },
                    { namePrefix: 'MicroBlocks' },
                    { namePrefix: 'Micro' },
                    { namePrefix: 'ESP' }
                ],
                // 可选服务
                optionalServices: ESP32_SERVICE_UUIDS
            };

            console.log('请求新的蓝牙设备...');
            this.updateUI('请选择您的蓝牙设备...');

            this.device = await navigator.bluetooth.requestDevice(requestOptions);

            console.log("设备选择成功:", this.device.name || "未知设备");
            this.device.addEventListener('gattserverdisconnected', this.handle_disconnected);

            return await this.connectToDevice(this.device);
        } catch (error) {
            console.error('连接错误：', error);
            // 连接失败时重置状态
            this.cleanupConnection();

            // 更新UI显示具体错误
            this.updateUI('连接失败: ' + error.message);
            return false;
        }
    }

    // 安全地停止通知
    async stopNotificationsSafely() {
        if (this.tx_char && this.notificationsActive) {
            try {
                console.log('尝试停止通知...');
                // 首先移除事件监听器
                this.tx_char.removeEventListener('characteristicvaluechanged', this.handle_read);

                // 然后停止通知
                await this.tx_char.stopNotifications();
                console.log('成功停止通知');
                this.notificationsActive = false;
                return true;
            } catch (e) {
                console.log('停止通知失败，这可能是正常的:', e);
                return false;
            }
        }
        return false;
    }

    // 单独发送停止指令的方法，以便可以在其他地方调用
    async sendStopCommand() {
        if (!this.isConnected()) {
            console.log("设备未连接，无法发送停止指令");
            return false;
        }

        try {
            // 使用全局的sendCommand函数
            return await sendCommand('stop');
        } catch (error) {
            console.error("发送停止指令失败:", error);
            return false;
        }
    }

    async disconnect() {
        // 避免重复断开操作
        if (this.isDisconnecting) {
            console.log('断开连接操作已经在进行中');
            return;
        }

        this.isDisconnecting = true;

        // 标记这是用户请求的断开
        this.disconnectRequested = true;

        // 更新UI以反映断开中状态
        this.updateUI('正在停止小车并断开蓝牙连接...', true, false);

        

        // 无论如何都确保清理状态，但保留设备信息以便重连
        if (this.device && this.connected) {
            try {
                // 先发送停止命令，但先检查是否可以发送
                if (this.rx_char && !this.sendInProgress) {
                    try {
                        console.log("断开前发送停止命令...");
                        const stopCommand = "stop";
                        const cmdData = bleSerialBroadcastCmd(stopCommand);
                        
                        // 直接使用特性写入，而不是调用可能会被拒绝的write_data方法
                        await this.rx_char.writeValueWithoutResponse(cmdData);
                        console.log("停止命令发送成功");
                        
                        // 等待短暂时间，确保命令被发送
                        await new Promise(resolve => setTimeout(resolve, 300));
                    } catch (cmdError) {
                        console.error("发送停止命令失败:", cmdError);
                        // 即使发送失败，仍继续断开过程
                    }
                } else {
                    console.log("无法发送停止命令：特性不可用或发送正在进行中");
                }
                
                // 安全停止通知
                await this.stopNotificationsSafely();
                
                // 等待简短时间以确保通知已停止
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // 检查设备是否仍然连接
                if (this.device.gatt.connected) {
                    console.log('断开设备连接...');
                    this.device.gatt.disconnect();
                    console.log('断开命令已发送');
                } else {
                    console.log('设备已断开连接');
                }
            } catch (error) {
                console.error('断开过程中出错:', error);
            }
        } else {
            console.log('设备已断开或未连接，无需断开');
        }
        
        this.cleanupConnection(false); // 设置为false，不清除设备缓存

        // 更新UI
        this.updateUI('蓝牙设备已断开',true,false);
        console.log('断开蓝牙设备完成，状态已重置');

        // 重置断开状态标志
        isDisconnecting = false;
    }

    isConnected() {
        return this.connected && this.rx_char != null && !isDisconnecting;
    }

    getDeviceName() {
        return this.device ? this.device.name : '未知设备';
    }

    write_data(data) {
        if (!this.rx_char) {
            throw new TypeError("未连接");
        }
        if (this.sendInProgress || !this.connected || isDisconnecting) {
            return Promise.reject(new Error("正在发送中或未连接"));
        }
        let byteCount = (data.length > BLE_PACKET_LEN) ? BLE_PACKET_LEN : data.length;
        return this.write_loop(data.subarray(0, byteCount)).then(() => byteCount);
    }

    async write_loop(data) {
        this.sendInProgress = true;

        return new Promise(async (resolve, reject) => {
            try {
                let attempts = 0;
                const maxAttempts = 3; // 最多尝试3次

                while (attempts < maxAttempts) {
                    try {
                        await this.rx_char.writeValueWithoutResponse(data);
                        this.sendInProgress = false;
                        resolve(); // 成功时解决Promise
                        return;
                    } catch (error) {
                        attempts++;
                        console.log(`BLE写入失败 (尝试 ${attempts}/${maxAttempts}):\n`, error);

                        // 如果不再连接，则立即放弃
                        if (!this.isConnected()) {
                            this.sendInProgress = false;
                            reject(error); // 失败时拒绝Promise
                            return;
                        }

                        // 如果达到最大尝试次数
                        if (attempts >= maxAttempts) {
                            this.sendInProgress = false;
                            reject(new Error("达到最大写入尝试次数"));
                            return;
                        }

                        // 等待一小段时间再重试
                        await new Promise(r => setTimeout(r, 100));
                    }
                }
            } catch (error) {
                this.sendInProgress = false;
                reject(error);
            }
        });
    }

    // 尝试重新连接到设备
    async reconnect() {
        if (this.reconnectAttempts >= BLE_MAX_RETRIES || !this.device) {
            console.log(`达到最大重连次数(${BLE_MAX_RETRIES})或设备不存在，不再尝试重连`);
            this.updateUI('重连失败，请手动重新连接');
            this.reconnectAttempts = 0;
            return false;
        }

        this.reconnectAttempts++;
        console.log(`尝试重连 (${this.reconnectAttempts}/${BLE_MAX_RETRIES})...`);
        this.updateUI(`正在尝试重新连接...(${this.reconnectAttempts}/${BLE_MAX_RETRIES})`);

        try {
            return await this.connectToDevice(this.device);
        } catch (error) {
            console.log('重连失败:', error);

            if (this.reconnectAttempts < BLE_MAX_RETRIES) {
                console.log(`${BLE_RECONNECT_DELAY / 1000}秒后将再次尝试...`);
                setTimeout(() => this.reconnect(), BLE_RECONNECT_DELAY);
            } else {
                this.updateUI('重连失败，请手动重新连接');
                this.reconnectAttempts = 0;
            }
            return false;
        }
    }
}

const bleSerial = new NimBLESerial();

///////////////////////////////////////////////////////////////
// KNN Classifier and Data Persistence
const classifier = knnClassifier.create();
const classes = ['go', 'left', 'right', 'stop'];
const sampleCounts = [0, 0, 0, 0];
let isRunning = false;
let lastPredictedClass = null;

// FPS 和内存监控
let frameCount = 0;
let lastTime = performance.now();
const fpsSpan = document.getElementById('fps').querySelector('span');
const memorySpan = document.getElementById('memory-info').querySelector('span');

let db;
const DB_NAME = 'knnClassifierDB';
const STORE_NAME = 'knnDatasets';

function initDB() {
    return new Promise((resolve) => {
        const request = indexedDB.open(DB_NAME, 1);
        request.onerror = (event) => {
            console.error("IndexedDB error:", event);
            statusElement.innerText = '状态: 缓存数据库打开失败';
            resolve(false);
        };
        request.onsuccess = (event) => {
            db = event.target.result;
            console.log("IndexedDB opened successfully");
            resolve(true);
        };
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains(STORE_NAME)) {
                db.createObjectStore(STORE_NAME, { keyPath: 'name' });
            }
        };
    });
}

function saveToDB(name, data) {
    return new Promise((resolve, reject) => {
        if (!db) return reject('DB not initialized');
        const transaction = db.transaction([STORE_NAME], "readwrite");
        transaction.objectStore(STORE_NAME).put({ name, data, timestamp: new Date().getTime() });
        transaction.oncomplete = () => resolve();
        transaction.onerror = (event) => reject(event);
    });
}

function loadFromDB(name) {
    return new Promise((resolve, reject) => {
        if (!db) return reject('DB not initialized');
        const request = db.transaction([STORE_NAME]).objectStore(STORE_NAME).get(name);
        request.onsuccess = () => resolve(request.result ? request.result.data : null);
        request.onerror = (event) => reject(event);
    });
}

function serializeClassifier(classifierDataset) {
    const tensors = {};
    Object.entries(classifierDataset).forEach(([label, data]) => {
        tensors[label] = { shape: data.shape, values: Array.from(data.dataSync()) };
    });
    return JSON.stringify(tensors);
}

async function deserializeAndSetClassifier(jsonStr) {
    const tensorsData = JSON.parse(jsonStr);
    const dataset = {};
    Object.entries(tensorsData).forEach(([label, tensorData]) => {
        dataset[label] = tf.tensor(tensorData.values, tensorData.shape);
    });
    classifier.setClassifierDataset(dataset);
    updateSampleCountsFromClassifier();
    statusElement.innerText = `状态: KNN数据已加载，共 ${classifier.getNumClasses()} 个分类。`;
}

function updateSampleCountDisplay() {
    document.getElementById('count-go').innerText = sampleCounts[0];
    document.getElementById('count-left').innerText = sampleCounts[1];
    document.getElementById('count-right').innerText = sampleCounts[2];
    document.getElementById('count-stop').innerText = sampleCounts[3];
}

function updateSampleCountsFromClassifier() {
    const classCounts = classifier.getClassExampleCount();
    classes.forEach((_, i) => sampleCounts[i] = classCounts[i] || 0);
    updateSampleCountDisplay();
}

///////////////////////////////////////////////////////////////
// Application Logic
async function app() {
    await loadMobileNetModel();
    startClassification();
}

let mobilenet;

// 创建限制并发连接的fetch函数
function createLimitedFetch(maxConcurrent = 3) {
    let activeRequests = 0;
    const queue = [];

    return async function limitedFetch(url, options = {}) {
        return new Promise((resolve, reject) => {
            const executeRequest = async () => {
                activeRequests++;
                try {
                    let response = await fetch(url, options);
                    if (!response.ok) throw new Error(`HTTP error ${response.status}`);
                    resolve(response);
                } catch (error) {
                    reject(error);
                } finally {
                    activeRequests--;
                    if (queue.length > 0) queue.shift()();
                }
            };

            if (activeRequests < maxConcurrent) {
                executeRequest();
            } else {
                queue.push(executeRequest);
            }
        });
    };
}


async function loadMobileNetModel() {
    statusElement.innerText = '状态: 正在加载MobileNet模型...';
    try {
        let loadedModel;
        const modelPath = '/models/mobilenet/model.json';

        try {
            console.log("尝试从本地文件系统加载模型...");
            statusElement.innerText = '状态: 正在从本地加载模型...';
            // 使用限制并发的fetch函数从本地加载
            loadedModel = await tf.loadLayersModel(modelPath, { fetchFunc: createLimitedFetch(3) });
            console.log("本地模型加载成功。");
        } catch (e) {
            console.warn("本地模型加载失败，尝试从网络加载:", e);
            statusElement.innerText = '状态: 本地加载失败，请刷新页面重试...';
            // const modelUrl = 'https://storage.googleapis.com/tfjs-models/tfjs/mobilenet_v1_0.25_224/model.json';
            // loadedModel = await tf.loadLayersModel(modelUrl);
            console.log("状态: 本地加载失败");
        }

        const layer = loadedModel.getLayer('conv_pw_13_relu');
        mobilenet = tf.model({ inputs: loadedModel.inputs, outputs: layer.output });
        statusElement.innerText = '状态: 模型已加载，准备就绪。';
    } catch (error) {
        console.error("模型加载失败:", error);
        statusElement.innerText = '状态: 模型加载失败。';
    }
}

// 清除浏览器HTTP缓存的函数
async function clearBrowserCache() {
    try {
        // 清除浏览器缓存（需要用户手动操作）
        console.log("🗑️ 提示用户清除浏览器缓存");
        statusElement.innerText = '状态: 请手动清除浏览器缓存或使用Ctrl+F5强制刷新';

        // 可选：尝试清除一些可清除的缓存
        if ('caches' in window) {
            const cacheNames = await caches.keys();
            await Promise.all(
                cacheNames.map(cacheName => caches.delete(cacheName))
            );
            console.log("🗑️ 已清除Service Worker缓存");
        }

        return true;
    } catch (error) {
        console.error("清除缓存失败:", error);
        statusElement.innerText = '状态: 清除缓存失败 - ' + error.message;
        return false;
    }
}

function extractFeatures(img, isTraining = false) {
    return tf.tidy(() => {
        const tensor = tf.browser.fromPixels(img).toFloat();
        const resized = tf.image.resizeBilinear(tensor, [224, 224]);
        const offset = tf.scalar(127.5);
        const normalized = resized.sub(offset).div(offset);
        const batched = normalized.reshape([1, 224, 224, 3]);
        return mobilenet.predict(batched);
    });
}

async function addExample(classId) {
    if (!mobilenet) {
        statusElement.innerText = "模型尚未加载，请稍候。";
        return;
    }
    const activation = extractFeatures(imgElement, true);
    classifier.addExample(activation, classId);
    activation.dispose();
    sampleCounts[classId]++;
    updateSampleCountDisplay();
    statusElement.innerText = `状态: 已添加样本到 ${classes[classId]}`;
    if (!isRunning) startClassification();
}

function resetClassifier() {
    classifier.clearAllClasses();
    classes.forEach((_, i) => sampleCounts[i] = 0);
    updateSampleCountDisplay();
    statusElement.innerText = "状态: 分类器已重置。";
    consoleElement.innerText = "请添加新的样本进行分类。";
}

async function startClassification() {
    isRunning = true;

    while (isRunning) {
        try {
            // --- FPS and Memory Monitoring ---
            const now = performance.now();
            frameCount++;
            if (now - lastTime > 1000) {
                fpsSpan.innerText = `帧率: ${frameCount} FPS`;
                lastTime = now;
                frameCount = 0;
                if (tf.memory().numBytes) {
                    memorySpan.innerText = `内存使用: ${formatBytes(tf.memory().numBytes)}`;
                }
            }

            // --- KNN Classification ---
            if (classifier.getNumClasses() > 0) {
                let activation;
                try {
                    activation = extractFeatures(imgElement, false);
                    const kValue = parseInt(kValueInput.value);
                    const result = await classifier.predictClass(activation, kValue);

                    if (result.label !== undefined) {
                        const predictedClass = parseInt(result.label);
                        const confidence = result.confidences[result.label];

                        // Update status with inference result
                        statusElement.innerText = `推理结果: ${classes[predictedClass]}:${confidence.toFixed(2)} (K=${kValue})`;

                        // Update console with BLE status
                        if (bleSerial.isConnected()) {
                            consoleElement.innerText = `蓝牙: "${bleSerial.getDeviceName()}" 已连接 | 指令发送: ${sendCommandsEnabled ? '已启用' : '已禁用'}`;
                        } else {
                            consoleElement.innerText = '蓝牙: 未连接';
                        }

                        // Send command via BLE
                        if (bleSerial.isConnected() && sendCommandsEnabled) {
                            // console.log(`持续发送指令: ${classes[predictedClass]}`);
                            await sendCommand(classes[predictedClass]);
                        }
                    }
                } finally {
                    // Ensure the activation tensor is always disposed
                    if (activation) {
                        activation.dispose();
                    }
                }
            }
        } catch (err) {
            console.error("Error in classification loop:", err);
        }

        await tf.nextFrame();
    }
}

///////////////////////////////////////////////////////////////
// Event Listeners
document.getElementById('class-go').addEventListener('click', () => addExample(0));
document.getElementById('class-left').addEventListener('click', () => addExample(1));
document.getElementById('class-right').addEventListener('click', () => addExample(2));
document.getElementById('class-stop').addEventListener('click', () => addExample(3));
document.getElementById('reset').addEventListener('click', resetClassifier);

// 清除浏览器缓存按钮事件
document.getElementById('clearMobileNetCache').addEventListener('click', async () => {
    if (confirm('确定要清除浏览器缓存吗？这将清除所有静态文件的缓存，下次访问将重新从ESP32获取。')) {
        const success = await clearBrowserCache();
        if (success) {
            // 提示用户手动刷新
            alert('缓存清除完成！请使用 Ctrl+F5 强制刷新页面以重新加载所有文件。');
        }
    }
});

kValueInput.addEventListener('input', (e) => kValueDisplay.textContent = e.target.value);

sendCommandToggle.addEventListener('change', (e) => {
    sendCommandsEnabled = e.target.checked;
    updateToggleState();

    // 如果关闭发送且蓝牙已连接，发送停止命令
    // if (!e.target.checked && bleSerial.isConnected()) {
    //     try {
    //         console.log("禁用蓝牙发送，发送停止命令");

    //         // 安全地发送停止命令
    //         safelyExecuteBleCommand('stop');
    //     } catch (error) {
    //         console.warn("尝试发送停止命令时出错:", error);
    //     }
    // }

    // 如果关闭发送且蓝牙已连接，发送停止命令
        if (!this.checked && bleSerial.isConnected()) {
            try {
                console.log("禁用蓝牙发送，发送停止命令");
                const stopCmd = bleSerialBroadcastCmd('stop');
                bleSerial.write_data(stopCmd).catch(err => 
                    console.warn("发送停止命令失败:", err)
                );
            } catch (e) {
                console.warn("尝试发送停止命令时出错:", e);
            }
        }
});

// 为整个开关容器添加点击事件，提高用户交互体验
document.querySelector('.switch-container').addEventListener('click', function (event) {
    // 排除点击标签文本和状态文本的情况
    if (event.target.tagName.toLowerCase() !== 'label' &&
        event.target.id !== 'toggle-status') {
        const toggleInput = document.getElementById('send-command-toggle');
        toggleInput.checked = !toggleInput.checked;

        // 触发change事件，确保事件处理器被调用
        const changeEvent = new Event('change');
        toggleInput.dispatchEvent(changeEvent);
    }
});



document.getElementById('connBle').addEventListener('click', async () => {
    try {
        document.getElementById('connBle').disabled = true;
        document.getElementById('status').innerHTML = '正在搜索蓝牙设备...';

        // 检查是否最近断开过连接，如果是则多等待一会儿
        const currentTime = new Date().getTime();
        const lastDisconnectTime = window.lastBleDisconnectTime || 0;
        const timeSinceDisconnect = currentTime - lastDisconnectTime;

        // 如果断开连接后不到2秒就尝试重连，先等待一会儿
        if (timeSinceDisconnect < 2000 && timeSinceDisconnect > 0) {
            const waitTime = 2000 - timeSinceDisconnect;
            document.getElementById('status').innerHTML = `蓝牙设备准备中，请稍候(${Math.ceil(waitTime / 1000)}秒)...`;
            await sleep(waitTime);
        }

        await bleSerial.connect();
    } catch (error) {
        console.error('连接过程中出现异常:', error);
        document.getElementById('status').innerHTML = '连接失败: ' + error.message;
        document.getElementById('connBle').disabled = false;
    }
});

document.getElementById('disconnBle').addEventListener('click', async () => {
    document.getElementById('disconnBle').disabled = true;
    await bleSerial.disconnect();

    // 记录断开连接的时间
    window.lastBleDisconnectTime = new Date().getTime();
});

document.getElementById('save-data').addEventListener('click', async () => {
    const fileName = document.getElementById('knn-file-name').value.trim();
    if (!fileName || classifier.getNumClasses() === 0) return;
    const dataset = classifier.getClassifierDataset();
    const jsonStr = serializeClassifier(dataset);
    await saveToDB(fileName, jsonStr);
    const a = document.createElement('a');
    a.href = URL.createObjectURL(new Blob([jsonStr], { type: 'application/json' }));
    a.download = `${fileName}.json`;
    a.click();
    statusElement.innerText = `状态: 数据 "${fileName}" 已保存。`;
});

document.getElementById('load-data').addEventListener('click', async () => {
    const fileName = document.getElementById('knn-file-name').value.trim();
    if (!fileName) return;
    const jsonStr = await loadFromDB(fileName);
    if (jsonStr) {
        await deserializeAndSetClassifier(jsonStr);
    } else {
        statusElement.innerText = `状态: 未找到数据 "${fileName}"。`;
    }
});

document.getElementById('file-selector').addEventListener('change', (event) => {
    const file = event.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = async (e) => {
        await deserializeAndSetClassifier(e.target.result);
        document.getElementById('file-selection-status').innerText = file.name;
    };
    reader.readAsText(file);
});


// Initial page load setup
window.addEventListener('load', async () => {
    await initDB();
    await app();
    setTimeout(connectCameraWebSocket, 1000);

    // 初始化命令发送开关状态
    const toggleElement = document.getElementById('send-command-toggle');
    toggleElement.checked = sendCommandsEnabled;
    updateToggleState();
});

function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
} 