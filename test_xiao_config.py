#!/usr/bin/env python3
"""
XIAO ESP32S3 Sense 配置验证脚本
用于验证项目配置是否正确适配Seeed Studio XIAO ESP32S3 Sense开发板
"""

import os
import re
import sys

def check_platformio_config():
    """检查platformio.ini配置"""
    print("🔍 检查 platformio.ini 配置...")
    
    if not os.path.exists('platformio.ini'):
        print("❌ platformio.ini 文件不存在")
        return False
    
    with open('platformio.ini', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查XIAO环境配置
    if '[env:seeed_xiao_esp32s3_sense]' in content:
        print("✅ 找到XIAO ESP32S3 Sense专用环境配置")
    else:
        print("❌ 未找到XIAO ESP32S3 Sense专用环境配置")
        return False
    
    # 检查摄像头模型定义
    if 'CAMERA_MODEL_XIAO_ESP32S3' in content:
        print("✅ 摄像头模型配置正确")
    else:
        print("❌ 摄像头模型配置错误")
        return False
    
    # 检查开发板配置
    if 'board = seeed_xiao_esp32s3' in content:
        print("✅ 开发板配置正确")
    else:
        print("❌ 开发板配置错误")
        return False
    
    return True

def check_partition_table():
    """检查分区表配置"""
    print("\n🔍 检查分区表配置...")
    
    if not os.path.exists('partitions.csv'):
        print("❌ partitions.csv 文件不存在")
        return False
    
    with open('partitions.csv', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查应用分区大小 (应该适合8MB Flash)
    app0_match = re.search(r'app0,\s+app,\s+ota_0,\s+0x10000,\s+0x([0-9a-fA-F]+)', content)
    if app0_match:
        app0_size = int(app0_match.group(1), 16)
        app0_size_mb = app0_size / (1024 * 1024)
        print(f"✅ 应用分区大小: {app0_size_mb:.2f}MB")
        
        if app0_size_mb <= 1.5:  # 应该小于1.5MB以适应8MB Flash
            print("✅ 应用分区大小适合8MB Flash")
        else:
            print("❌ 应用分区过大，可能不适合8MB Flash")
            return False
    else:
        print("❌ 无法解析应用分区大小")
        return False
    
    return True

def check_camera_pins():
    """检查摄像头引脚定义"""
    print("\n🔍 检查摄像头引脚定义...")
    
    if not os.path.exists('include/camera_pins.h'):
        print("❌ camera_pins.h 文件不存在")
        return False
    
    with open('include/camera_pins.h', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查XIAO ESP32S3引脚定义
    if '#elif defined(CAMERA_MODEL_XIAO_ESP32S3)' in content:
        print("✅ 找到XIAO ESP32S3摄像头引脚定义")
        
        # 检查关键引脚定义
        expected_pins = [
            'XCLK_GPIO_NUM     10',
            'SIOD_GPIO_NUM     40', 
            'SIOC_GPIO_NUM     39',
            'Y9_GPIO_NUM       48',
            'Y8_GPIO_NUM       11',
            'Y7_GPIO_NUM       12',
            'Y6_GPIO_NUM       14',
            'Y5_GPIO_NUM       16',
            'Y4_GPIO_NUM       18',
            'Y3_GPIO_NUM       17',
            'Y2_GPIO_NUM       15',
            'VSYNC_GPIO_NUM    38',
            'HREF_GPIO_NUM     47',
            'PCLK_GPIO_NUM     13'
        ]
        
        for pin in expected_pins:
            if pin in content:
                print(f"✅ 引脚定义正确: {pin}")
            else:
                print(f"❌ 引脚定义缺失: {pin}")
                return False
    else:
        print("❌ 未找到XIAO ESP32S3摄像头引脚定义")
        return False
    
    return True

def check_camera_init():
    """检查摄像头初始化代码"""
    print("\n🔍 检查摄像头初始化代码...")
    
    if not os.path.exists('src/camera_init.cpp'):
        print("❌ camera_init.cpp 文件不存在")
        return False
    
    with open('src/camera_init.cpp', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查OV2640相关注释
    if 'OV2640' in content:
        print("✅ 摄像头初始化代码已更新为OV2640")
    else:
        print("⚠️  摄像头初始化代码可能需要更新为OV2640")
    
    # 检查I2C配置
    if 'Wire.setClock(100000)' in content:
        print("✅ I2C时钟频率配置正确 (100kHz)")
    else:
        print("❌ I2C时钟频率配置可能不正确")
        return False
    
    return True

def generate_build_commands():
    """生成构建命令"""
    print("\n🚀 构建命令:")
    print("=" * 50)
    print("# 清理构建缓存")
    print("pio run -e seeed_xiao_esp32s3_sense --target clean")
    print()
    print("# 构建项目")
    print("pio run -e seeed_xiao_esp32s3_sense")
    print()
    print("# 上传固件")
    print("pio run -e seeed_xiao_esp32s3_sense --target upload")
    print()
    print("# 监控串口输出")
    print("pio device monitor")
    print("=" * 50)

def main():
    """主函数"""
    print("🔧 XIAO ESP32S3 Sense 配置验证")
    print("=" * 50)
    
    all_checks_passed = True
    
    # 执行各项检查
    if not check_platformio_config():
        all_checks_passed = False
    
    if not check_partition_table():
        all_checks_passed = False
    
    if not check_camera_pins():
        all_checks_passed = False
    
    if not check_camera_init():
        all_checks_passed = False
    
    # 生成构建命令
    generate_build_commands()
    
    # 总结
    print("\n📋 检查总结:")
    print("=" * 50)
    if all_checks_passed:
        print("✅ 所有配置检查通过！")
        print("🎉 项目已成功适配Seeed Studio XIAO ESP32S3 Sense开发板")
        print("\n💡 下一步:")
        print("1. 连接XIAO ESP32S3 Sense开发板")
        print("2. 运行构建命令")
        print("3. 上传固件并测试")
    else:
        print("❌ 部分配置检查失败")
        print("🔧 请根据上述错误信息修复配置")
        sys.exit(1)

if __name__ == "__main__":
    main() 