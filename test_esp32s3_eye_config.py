#!/usr/bin/env python3
"""
ESP32-S3-EYE 配置验证脚本
验证新增的 ESP32-S3-EYE 开发板配置是否正确
"""

import os
import re

def test_platformio_config():
    """测试 platformio.ini 配置"""
    print("🔍 检查 platformio.ini 配置...")
    
    with open('platformio.ini', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否包含 esp32s3_eye 环境
    if '[env:esp32s3_eye]' in content:
        print("✅ 找到 esp32s3_eye 环境配置")
    else:
        print("❌ 未找到 esp32s3_eye 环境配置")
        return False
    
    # 检查关键配置项
    required_configs = [
        'platform = espressif32',
        'board = esp32-s3-devkitc-1',
        'framework = arduino',
        '-DCAMERA_MODEL_ESP32S3_EYE',
        'board_build.partitions = partitions.csv',
        'board_build.psram_type = opi'
    ]
    
    for config in required_configs:
        if config in content:
            print(f"✅ 找到配置: {config}")
        else:
            print(f"❌ 缺少配置: {config}")
            return False
    
    return True

def test_camera_pins():
    """测试摄像头引脚配置"""
    print("\n🔍 检查摄像头引脚配置...")
    
    with open('include/camera_pins.h', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否包含 ESP32S3_EYE 配置
    if '#elif defined(CAMERA_MODEL_ESP32S3_EYE)' in content:
        print("✅ 找到 CAMERA_MODEL_ESP32S3_EYE 配置")
    else:
        print("❌ 未找到 CAMERA_MODEL_ESP32S3_EYE 配置")
        return False
    
    # 检查关键引脚定义
    required_pins = [
        '#define XCLK_GPIO_NUM     15',
        '#define SIOD_GPIO_NUM     4',
        '#define SIOC_GPIO_NUM     5',
        '#define Y2_GPIO_NUM       11',
        '#define Y9_GPIO_NUM       16',
        '#define VSYNC_GPIO_NUM    6',
        '#define HREF_GPIO_NUM     7',
        '#define PCLK_GPIO_NUM     13'
    ]
    
    for pin in required_pins:
        if pin in content:
            print(f"✅ 找到引脚定义: {pin}")
        else:
            print(f"❌ 缺少引脚定义: {pin}")
            return False
    
    return True

def test_file_structure():
    """测试文件结构"""
    print("\n🔍 检查文件结构...")
    
    required_files = [
        'platformio.ini',
        'include/camera_pins.h',
        'include/app_config.h',
        'src/main.cpp',
        'src/camera_init.cpp',
        'src/camera_server.cpp',
        'QUICK_START_ESP32S3_EYE.md'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ 文件存在: {file_path}")
        else:
            print(f"❌ 文件缺失: {file_path}")
            return False
    
    return True

def test_documentation():
    """测试文档更新"""
    print("\n🔍 检查文档更新...")
    
    # 检查 README.md
    with open('README.md', 'r', encoding='utf-8') as f:
        readme_content = f.read()
    
    if 'ESP32-S3-EYE' in readme_content:
        print("✅ README.md 包含 ESP32-S3-EYE 信息")
    else:
        print("❌ README.md 缺少 ESP32-S3-EYE 信息")
        return False
    
    if 'QUICK_START_ESP32S3_EYE.md' in readme_content:
        print("✅ README.md 包含快速开始指南链接")
    else:
        print("❌ README.md 缺少快速开始指南链接")
        return False
    
    # 检查快速开始指南
    if os.path.exists('QUICK_START_ESP32S3_EYE.md'):
        with open('QUICK_START_ESP32S3_EYE.md', 'r', encoding='utf-8') as f:
            guide_content = f.read()
        
        if 'esp32s3_eye' in guide_content:
            print("✅ 快速开始指南包含环境配置信息")
        else:
            print("❌ 快速开始指南缺少环境配置信息")
            return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 ESP32-S3-EYE 配置验证开始...\n")
    
    tests = [
        ("PlatformIO 配置", test_platformio_config),
        ("摄像头引脚配置", test_camera_pins),
        ("文件结构", test_file_structure),
        ("文档更新", test_documentation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            print(f"✅ {test_name} 测试通过")
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！ESP32-S3-EYE 配置正确。")
        print("\n📝 下一步操作:")
        print("1. 连接 ESP32-S3-EYE 开发板")
        print("2. 运行: pio run -e esp32s3_eye")
        print("3. 上传: pio run -e esp32s3_eye -t upload")
        print("4. 参考: QUICK_START_ESP32S3_EYE.md")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    main()
