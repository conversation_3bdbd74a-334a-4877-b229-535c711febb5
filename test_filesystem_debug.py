#!/usr/bin/env python3
"""
ESP32-S3-EYE 文件系统调试功能测试脚本
验证新增的调试功能是否正确实现
"""

import os
import re

def test_debug_functions():
    """测试调试函数是否正确添加"""
    print("🔍 检查调试函数实现...")
    
    with open('src/main.cpp', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键调试函数
    required_functions = [
        'printPartitionInfo',
        'testFileReadability',
        'printFileSystemInfo'
    ]
    
    for func in required_functions:
        if f'void {func}(' in content:
            print(f"✅ 找到函数: {func}")
        else:
            print(f"❌ 缺少函数: {func}")
            return False
    
    return True

def test_enhanced_logging():
    """测试增强的日志输出"""
    print("\n🔍 检查增强的日志输出...")
    
    with open('src/main.cpp', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键日志信息
    required_logs = [
        '=== 文件系统初始化开始 ===',
        '📊 分区信息:',
        '💾 文件系统状态:',
        '📁 扫描文件系统内容...',
        '🔍 测试关键文件可读性...',
        '⚠️  文件系统为空',
        '❌ 文件不存在'
    ]
    
    for log in required_logs:
        if log in content:
            print(f"✅ 找到日志: {log}")
        else:
            print(f"❌ 缺少日志: {log}")
            return False
    
    return True

def test_camera_server_debug():
    """测试camera_server.cpp中的调试功能"""
    print("\n🔍 检查camera_server.cpp调试功能...")
    
    with open('src/camera_server.cpp', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查index_handler的调试信息
    required_logs = [
        '=== 主页请求处理 ===',
        '🏠 收到根路径请求',
        '📖 尝试从文件系统读取',
        '✅ 成功读取文件系统中的',
        '❌ 无法读取文件系统中的',
        '🔄 回退到备用HTML页面'
    ]
    
    for log in required_logs:
        if log in content:
            print(f"✅ 找到日志: {log}")
        else:
            print(f"❌ 缺少日志: {log}")
            return False
    
    return True

def test_header_includes():
    """测试必要的头文件包含"""
    print("\n🔍 检查头文件包含...")
    
    with open('include/app_config.h', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查分区相关头文件
    required_includes = [
        '#include "esp_partition.h"',
        '#include "esp_ota_ops.h"'
    ]
    
    for include in required_includes:
        if include in content:
            print(f"✅ 找到头文件: {include}")
        else:
            print(f"❌ 缺少头文件: {include}")
            return False
    
    return True

def test_documentation():
    """测试文档更新"""
    print("\n🔍 检查文档更新...")
    
    # 检查调试指南文档
    if os.path.exists('filesystem_debug_guide.md'):
        print("✅ 找到文件系统调试指南")
        
        with open('filesystem_debug_guide.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'ESP32-S3-EYE 文件系统调试指南' in content:
            print("✅ 调试指南标题正确")
        else:
            print("❌ 调试指南标题错误")
            return False
    else:
        print("❌ 缺少文件系统调试指南")
        return False
    
    # 检查快速开始指南更新
    if os.path.exists('QUICK_START_ESP32S3_EYE.md'):
        with open('QUICK_START_ESP32S3_EYE.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'filesystem_debug_guide.md' in content:
            print("✅ 快速开始指南包含调试指南链接")
        else:
            print("❌ 快速开始指南缺少调试指南链接")
            return False
    
    return True

def test_static_file_handler():
    """测试静态文件处理器的调试功能"""
    print("\n🔍 检查静态文件处理器调试功能...")
    
    with open('src/main.cpp', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查static_file_handler的调试信息
    required_logs = [
        '=== 静态文件请求处理 ===',
        '🏠 根路径请求，映射到:',
        '📄 直接文件请求:',
        '🔍 检查压缩文件:',
        '✅ 找到gzip压缩文件:',
        '📝 压缩文件不存在，检查原始文件:',
        '📋 当前文件系统内容:',
        '📂 尝试打开文件:'
    ]
    
    for log in required_logs:
        if log in content:
            print(f"✅ 找到日志: {log}")
        else:
            print(f"❌ 缺少日志: {log}")
            return False
    
    return True

def test_readfile_debug():
    """测试readFile函数的调试功能"""
    print("\n🔍 检查readFile函数调试功能...")
    
    with open('src/main.cpp', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查readFile的调试信息
    required_logs = [
        '📖 读取文件:',
        '文件大小:',
        '⚠️  文件为空:',
        '✅ 文件读取成功:',
        '❌ 文件读取失败:'
    ]
    
    for log in required_logs:
        if log in content:
            print(f"✅ 找到日志: {log}")
        else:
            print(f"❌ 缺少日志: {log}")
            return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 ESP32-S3-EYE 文件系统调试功能测试开始...\n")
    
    tests = [
        ("调试函数实现", test_debug_functions),
        ("增强日志输出", test_enhanced_logging),
        ("Camera Server调试", test_camera_server_debug),
        ("头文件包含", test_header_includes),
        ("文档更新", test_documentation),
        ("静态文件处理器调试", test_static_file_handler),
        ("ReadFile函数调试", test_readfile_debug)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            print(f"✅ {test_name} 测试通过")
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有调试功能测试通过！")
        print("\n📝 现在您可以:")
        print("1. 编译并上传固件到ESP32-S3-EYE")
        print("2. 观察详细的调试输出")
        print("3. 根据日志信息诊断文件系统问题")
        print("4. 参考调试指南解决问题")
        return True
    else:
        print("⚠️  部分调试功能测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    main()
