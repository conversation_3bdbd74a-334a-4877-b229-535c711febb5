{"weightsManifest": [{"paths": ["group1-shard1of1.bin"], "weights": [{"dtype": "float32", "shape": [], "name": "module_apply_default/hub_input/Mul/y"}, {"dtype": "float32", "shape": [], "name": "module_apply_default/hub_input/Sub/y"}, {"dtype": "int32", "shape": [2], "name": "module_apply_default/MobilenetV1/Logits/global_pool/reduction_indices"}, {"dtype": "float32", "shape": [3, 3, 3, 8], "name": "module/MobilenetV1/Conv2d_0/weights"}, {"dtype": "float32", "shape": [8], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_0/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 8, 1], "name": "module/MobilenetV1/Conv2d_1_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [8], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 8, 16], "name": "module/MobilenetV1/Conv2d_1_pointwise/weights"}, {"dtype": "float32", "shape": [16], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 16, 1], "name": "module/MobilenetV1/Conv2d_2_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [16], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 16, 32], "name": "module/MobilenetV1/Conv2d_2_pointwise/weights"}, {"dtype": "float32", "shape": [32], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 32, 1], "name": "module/MobilenetV1/Conv2d_3_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [32], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 32, 32], "name": "module/MobilenetV1/Conv2d_3_pointwise/weights"}, {"dtype": "float32", "shape": [32], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 32, 1], "name": "module/MobilenetV1/Conv2d_4_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [32], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 32, 64], "name": "module/MobilenetV1/Conv2d_4_pointwise/weights"}, {"dtype": "float32", "shape": [64], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 64, 1], "name": "module/MobilenetV1/Conv2d_5_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [64], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 64, 64], "name": "module/MobilenetV1/Conv2d_5_pointwise/weights"}, {"dtype": "float32", "shape": [64], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 64, 1], "name": "module/MobilenetV1/Conv2d_6_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [64], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 64, 128], "name": "module/MobilenetV1/Conv2d_6_pointwise/weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 128, 1], "name": "module/MobilenetV1/Conv2d_7_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 128, 128], "name": "module/MobilenetV1/Conv2d_7_pointwise/weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 128, 1], "name": "module/MobilenetV1/Conv2d_8_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 128, 128], "name": "module/MobilenetV1/Conv2d_8_pointwise/weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 128, 1], "name": "module/MobilenetV1/Conv2d_9_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 128, 128], "name": "module/MobilenetV1/Conv2d_9_pointwise/weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 128, 1], "name": "module/MobilenetV1/Conv2d_10_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 128, 128], "name": "module/MobilenetV1/Conv2d_10_pointwise/weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 128, 1], "name": "module/MobilenetV1/Conv2d_11_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 128, 128], "name": "module/MobilenetV1/Conv2d_11_pointwise/weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 128, 1], "name": "module/MobilenetV1/Conv2d_12_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [128], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 128, 256], "name": "module/MobilenetV1/Conv2d_12_pointwise/weights"}, {"dtype": "float32", "shape": [256], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_pointwise/Conv2D_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 256, 1], "name": "module/MobilenetV1/Conv2d_13_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [256], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_depthwise/depthwise_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 256, 256], "name": "module/MobilenetV1/Conv2d_13_pointwise/weights"}, {"dtype": "float32", "shape": [256], "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Conv2D_bn_offset"}]}], "modelTopology": {"node": [{"attr": {"dtype": {"type": "DT_FLOAT"}, "shape": {"shape": {"dim": [{"size": "-1"}, {"size": "-1"}, {"size": "-1"}, {"size": "3"}]}}}, "name": "images", "op": "Placeholder"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}, "name": "module_apply_default/hub_input/Mul/y", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}, "name": "module_apply_default/hub_input/Sub/y", "op": "Const"}, {"attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}, "name": "module_apply_default/MobilenetV1/Logits/global_pool/reduction_indices", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "8"}]}}}}, "name": "module/MobilenetV1/Conv2d_0/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "8"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_0/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "8"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_1_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "8"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "8"}, {"size": "16"}]}}}}, "name": "module/MobilenetV1/Conv2d_1_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_2_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "32"}]}}}}, "name": "module/MobilenetV1/Conv2d_2_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_3_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "32"}]}}}}, "name": "module/MobilenetV1/Conv2d_3_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_4_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "64"}]}}}}, "name": "module/MobilenetV1/Conv2d_4_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_5_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "64"}]}}}}, "name": "module/MobilenetV1/Conv2d_5_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_6_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}, "name": "module/MobilenetV1/Conv2d_6_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_7_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "128"}]}}}}, "name": "module/MobilenetV1/Conv2d_7_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_8_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "128"}]}}}}, "name": "module/MobilenetV1/Conv2d_8_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_9_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "128"}]}}}}, "name": "module/MobilenetV1/Conv2d_9_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_10_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "128"}]}}}}, "name": "module/MobilenetV1/Conv2d_10_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_11_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "128"}]}}}}, "name": "module/MobilenetV1/Conv2d_11_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_12_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "256"}]}}}}, "name": "module/MobilenetV1/Conv2d_12_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_pointwise/Conv2D_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}}, "name": "module/MobilenetV1/Conv2d_13_depthwise/depthwise_weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_depthwise/depthwise_bn_offset", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}}, "name": "module/MobilenetV1/Conv2d_13_pointwise/weights", "op": "Const"}, {"attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Conv2D_bn_offset", "op": "Const"}, {"input": ["images", "module_apply_default/hub_input/Mul/y"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/hub_input/Mul", "op": "<PERSON><PERSON>"}, {"input": ["module_apply_default/hub_input/Mul", "module_apply_default/hub_input/Sub/y"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/hub_input/Sub", "op": "Sub"}, {"device": "/device:CPU:0", "input": ["module_apply_default/hub_input/Sub", "module/MobilenetV1/Conv2d_0/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_0/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_0/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_0/Relu6", "module/MobilenetV1/Conv2d_1_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_depthwise/Relu6", "module/MobilenetV1/Conv2d_1_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_1_pointwise/Relu6", "module/MobilenetV1/Conv2d_2_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_depthwise/Relu6", "module/MobilenetV1/Conv2d_2_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_2_pointwise/Relu6", "module/MobilenetV1/Conv2d_3_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_depthwise/Relu6", "module/MobilenetV1/Conv2d_3_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_3_pointwise/Relu6", "module/MobilenetV1/Conv2d_4_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_depthwise/depthwise_bn_offset"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_depthwise/Relu6", "module/MobilenetV1/Conv2d_4_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_4_pointwise/Relu6", "module/MobilenetV1/Conv2d_5_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_depthwise/Relu6", "module/MobilenetV1/Conv2d_5_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_5_pointwise/Relu6", "module/MobilenetV1/Conv2d_6_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_depthwise/Relu6", "module/MobilenetV1/Conv2d_6_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_6_pointwise/Relu6", "module/MobilenetV1/Conv2d_7_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_depthwise/Relu6", "module/MobilenetV1/Conv2d_7_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_7_pointwise/Relu6", "module/MobilenetV1/Conv2d_8_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_depthwise/Relu6", "module/MobilenetV1/Conv2d_8_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_8_pointwise/Relu6", "module/MobilenetV1/Conv2d_9_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_depthwise/Relu6", "module/MobilenetV1/Conv2d_9_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_9_pointwise/Relu6", "module/MobilenetV1/Conv2d_10_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_depthwise/Relu6", "module/MobilenetV1/Conv2d_10_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_10_pointwise/Relu6", "module/MobilenetV1/Conv2d_11_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_depthwise/Relu6", "module/MobilenetV1/Conv2d_11_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_11_pointwise/Relu6", "module/MobilenetV1/Conv2d_12_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_depthwise/Relu6", "module/MobilenetV1/Conv2d_12_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_12_pointwise/Relu6", "module/MobilenetV1/Conv2d_13_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_depthwise/depthwise", "op": "DepthwiseConv2dNative"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_depthwise/depthwise", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_depthwise/depthwise_bn_offset"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_depthwise/BatchNorm/FusedBatchNorm", "op": "BiasAdd"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_depthwise/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_depthwise/Relu6", "op": "Relu6"}, {"device": "/device:CPU:0", "input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_depthwise/Relu6", "module/MobilenetV1/Conv2d_13_pointwise/weights", "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Conv2D_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}, "name": "module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Relu6", "op": "_FusedConv2D"}, {"input": ["module_apply_default/MobilenetV1/MobilenetV1/Conv2d_13_pointwise/Relu6", "module_apply_default/MobilenetV1/Logits/global_pool/reduction_indices"], "attr": {"T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}}, "name": "module_apply_default/MobilenetV1/Logits/global_pool", "op": "Mean"}, {"input": ["module_apply_default/MobilenetV1/Logits/global_pool"], "attr": {"squeeze_dims": {"list": {"i": ["1", "2"]}}, "T": {"type": "DT_FLOAT"}}, "name": "module_apply_default/hub_output/feature_vector/SpatialSqueeze", "op": "Squeeze"}], "library": {}, "versions": {}}, "generatedBy": "1.14.0", "convertedBy": "TensorFlow.js Converter v1.2.10.1", "format": "graph-model"}