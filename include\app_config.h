#ifndef APP_CONFIG_H
#define APP_CONFIG_H

#include <Arduino.h>
#include "esp_camera.h"
#include <WiFi.h>
#include "esp_timer.h"
#include "img_converters.h"
#include "fb_gfx.h"
#include "esp_http_server.h"
#include "nvs_flash.h"
#include <ESPmDNS.h>
#include <Preferences.h>
#include <ArduinoWebsockets.h>
#include <Wire.h>
#include <LittleFS.h>
#include "esp_partition.h"
#include "esp_ota_ops.h"
#include "camera_pins.h"

// 引脚定义现在由camera_pins.h通过CAMERA_MODEL宏定义控制

// 增加一组可能适用于OV2154的引脚定义
#define OV2154_PWDN_GPIO_NUM    -1
#define OV2154_RESET_GPIO_NUM   -1
#define OV2154_XCLK_GPIO_NUM    7
#define OV2154_SIOD_GPIO_NUM    47
#define OV2154_SIOC_GPIO_NUM    48
#define OV2154_Y9_GPIO_NUM      6
#define OV2154_Y8_GPIO_NUM      15
#define OV2154_Y7_GPIO_NUM      16
#define OV2154_Y6_GPIO_NUM      18
#define OV2154_Y5_GPIO_NUM      9
#define OV2154_Y4_GPIO_NUM      11
#define OV2154_Y3_GPIO_NUM      10
#define OV2154_Y2_GPIO_NUM      8
#define OV2154_VSYNC_GPIO_NUM   4
#define OV2154_HREF_GPIO_NUM    5
#define OV2154_PCLK_GPIO_NUM    17

// 增加另一组可能的OV2154引脚定义 (通用OV2系列兼容配置)
#define OV2_ALT_PWDN_GPIO_NUM    -1
#define OV2_ALT_RESET_GPIO_NUM   -1
#define OV2_ALT_XCLK_GPIO_NUM    15
#define OV2_ALT_SIOD_GPIO_NUM    4
#define OV2_ALT_SIOC_GPIO_NUM    5
#define OV2_ALT_Y9_GPIO_NUM      16
#define OV2_ALT_Y8_GPIO_NUM      17
#define OV2_ALT_Y7_GPIO_NUM      18
#define OV2_ALT_Y6_GPIO_NUM      12
#define OV2_ALT_Y5_GPIO_NUM      10
#define OV2_ALT_Y4_GPIO_NUM      8
#define OV2_ALT_Y3_GPIO_NUM      9
#define OV2_ALT_Y2_GPIO_NUM      11
#define OV2_ALT_VSYNC_GPIO_NUM   6
#define OV2_ALT_HREF_GPIO_NUM    7
#define OV2_ALT_PCLK_GPIO_NUM    13

// 定义全局变量
#define PART_BOUNDARY "123456789000000000000987654321"

// 全局变量声明
extern httpd_handle_t stream_httpd;
extern httpd_handle_t camera_httpd;
extern websockets::WebsocketsServer wsServer;
extern websockets::WebsocketsClient activeClient;
extern bool clientConnected;
extern int connectedClients;
extern unsigned long lastPingTime;

// 函数声明
bool initCamera();
void startCameraServer();
bool initFileSystem();
String readFile(const char* path);
const char* getMimeType(const char* path);
esp_err_t static_file_handler(httpd_req_t *req);
esp_err_t error_404_handler(httpd_req_t *req, httpd_err_code_t err);

// 摄像头参数调整函数声明
void adjustCameraSettings(int brightness, int contrast, int saturation, int exposure_level, int gain);
void printCameraSettings();

// HTML页面内容声明
extern const char FALLBACK_INDEX_HTML[];

#endif // APP_CONFIG_H 