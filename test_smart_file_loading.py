#!/usr/bin/env python3
"""
测试智能文件加载机制
验证服务器能够正确选择gzip压缩版本或未压缩版本
"""

import os
import re

def check_data_files():
    """检查data目录中的文件"""
    print("🔍 检查data目录中的文件...")
    
    js_files = {
        'jquery-3.6.0.min.js': {'compressed': False, 'uncompressed': False},
        'jszip.min.js': {'compressed': False, 'uncompressed': False},
        'file-saver.js': {'compressed': False, 'uncompressed': False},
        'tfjs.js': {'compressed': False, 'uncompressed': False},
        'tfjs-vis.js': {'compressed': False, 'uncompressed': False},
        'collecttrain.js': {'compressed': False, 'uncompressed': False}
    }
    
    # 检查文件存在情况
    for filename in js_files.keys():
        compressed_path = f"data/js/{filename}.gz"
        uncompressed_path = f"data/js/{filename}"
        
        js_files[filename]['compressed'] = os.path.exists(compressed_path)
        js_files[filename]['uncompressed'] = os.path.exists(uncompressed_path)
    
    print("📋 JS文件存在情况:")
    for filename, status in js_files.items():
        compressed_status = "✅" if status['compressed'] else "❌"
        uncompressed_status = "✅" if status['uncompressed'] else "❌"
        
        print(f"  📄 {filename}")
        print(f"    压缩版本 (.gz): {compressed_status}")
        print(f"    未压缩版本:     {uncompressed_status}")
        
        # 分析加载策略
        if status['compressed'] and status['uncompressed']:
            print(f"    🎯 服务器将选择: 压缩版本 (优先)")
        elif status['compressed']:
            print(f"    🎯 服务器将选择: 压缩版本 (仅有)")
        elif status['uncompressed']:
            print(f"    🎯 服务器将选择: 未压缩版本 (仅有)")
        else:
            print(f"    ❌ 错误: 两个版本都不存在!")
    
    return js_files

def check_server_routes():
    """检查服务器路由配置"""
    print("\n🔍 检查服务器路由配置...")
    
    if not os.path.exists("src/camera_server.cpp"):
        print("❌ 未找到 src/camera_server.cpp")
        return False
    
    with open("src/camera_server.cpp", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查路由定义（应该是未压缩版本的路径）
    expected_routes = [
        '"/js/jquery-3.6.0.min.js"',
        '"/js/jszip.min.js"',
        '"/js/file-saver.js"',
        '"/js/tfjs-vis.js"',
        '"/js/collecttrain.js"'
    ]
    
    print("📋 服务器路由检查:")
    all_good = True
    for route in expected_routes:
        if route in content:
            print(f"  ✅ 找到路由: {route}")
        else:
            print(f"  ❌ 缺少路由: {route}")
            all_good = False
    
    # 检查是否错误地使用了.gz路径
    wrong_routes = [
        '"/js/jquery-3.6.0.min.js.gz"',
        '"/js/jszip.min.js.gz"',
        '"/js/file-saver.js.gz"',
        '"/js/tfjs-vis.js.gz"'
    ]
    
    print("\n📋 检查错误的.gz路由:")
    for route in wrong_routes:
        if route in content:
            print(f"  ⚠️  发现.gz路由: {route} (应该移除)")
            all_good = False
        else:
            print(f"  ✅ 正确: 未使用.gz路由 {route}")
    
    return all_good

def check_html_references():
    """检查HTML文件中的引用"""
    print("\n🔍 检查HTML文件中的JS引用...")
    
    html_files = ["data/index.html", "data/collecttrain.html"]
    
    for html_file in html_files:
        if not os.path.exists(html_file):
            print(f"❌ 未找到 {html_file}")
            continue
        
        print(f"\n📄 检查 {html_file}:")
        
        with open(html_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找script标签
        script_pattern = r'<script\s+src="([^"]+)"'
        scripts = re.findall(script_pattern, content)
        
        for script_src in scripts:
            if script_src.startswith('/js/'):
                if script_src.endswith('.gz'):
                    print(f"  ⚠️  使用.gz引用: {script_src} (应该使用未压缩路径)")
                else:
                    print(f"  ✅ 正确引用: {script_src}")

def check_static_file_handler():
    """检查static_file_handler的gzip逻辑"""
    print("\n🔍 检查static_file_handler的gzip处理逻辑...")
    
    if not os.path.exists("src/main.cpp"):
        print("❌ 未找到 src/main.cpp")
        return False
    
    with open("src/main.cpp", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查关键的gzip处理逻辑
    gzip_checks = {
        "gzip文件路径构建": 'snprintf(gzip_filepath, sizeof(gzip_filepath), "%s.gz", filepath)' in content,
        "gzip文件存在检查": 'LittleFS.exists(gzip_filepath)' in content,
        "优先使用gzip": 'is_gzip ? gzip_filepath : filepath' in content,
        "gzip标志设置": 'is_gzip = true' in content
    }
    
    print("📋 static_file_handler gzip逻辑检查:")
    all_good = True
    for check_name, result in gzip_checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def generate_recommendations(js_files):
    """生成建议"""
    print("\n💡 建议和说明:")
    
    print("\n🎯 智能文件加载机制工作原理:")
    print("1. HTML中引用未压缩文件路径 (如 /js/tfjs.js)")
    print("2. 服务器收到请求后检查是否存在对应的.gz文件")
    print("3. 如果存在.gz文件，自动返回压缩版本")
    print("4. 如果不存在.gz文件，返回未压缩版本")
    print("5. 浏览器自动解压gzip内容")
    
    print("\n📦 当前文件状态分析:")
    for filename, status in js_files.items():
        if status['compressed'] and not status['uncompressed']:
            print(f"  📄 {filename}: 仅有压缩版本 - 正常")
        elif not status['compressed'] and status['uncompressed']:
            print(f"  📄 {filename}: 仅有未压缩版本 - 正常")
        elif status['compressed'] and status['uncompressed']:
            print(f"  📄 {filename}: 两个版本都存在 - 将使用压缩版本")
        else:
            print(f"  ❌ {filename}: 两个版本都不存在 - 需要添加文件!")
    
    print("\n🚀 优化建议:")
    print("1. 优先使用gzip压缩版本以节省带宽")
    print("2. 保留未压缩版本作为备用")
    print("3. 在HTML中始终引用未压缩路径")
    print("4. 让服务器自动选择最佳版本")

def main():
    """主测试函数"""
    print("🚀 智能文件加载机制测试")
    print("=" * 50)
    
    # 检查文件存在情况
    js_files = check_data_files()
    
    # 检查服务器配置
    server_ok = check_server_routes()
    
    # 检查HTML引用
    check_html_references()
    
    # 检查处理逻辑
    handler_ok = check_static_file_handler()
    
    # 生成建议
    generate_recommendations(js_files)
    
    print(f"\n{'='*50}")
    print("测试总结:")
    print(f"  服务器路由配置: {'✅ 正确' if server_ok else '❌ 需要修复'}")
    print(f"  文件处理逻辑: {'✅ 正确' if handler_ok else '❌ 需要修复'}")
    
    # 检查是否有缺失的文件
    missing_files = []
    for filename, status in js_files.items():
        if not status['compressed'] and not status['uncompressed']:
            missing_files.append(filename)
    
    if missing_files:
        print(f"  ❌ 缺失文件: {', '.join(missing_files)}")
    else:
        print("  ✅ 所有必需文件都存在")
    
    if server_ok and handler_ok and not missing_files:
        print("\n🎉 智能文件加载机制配置正确!")
        print("服务器将自动选择最佳的文件版本提供给客户端。")
    else:
        print("\n⚠️  发现一些问题，请根据上述建议进行修复。")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"💥 测试过程出错: {e}")
        exit(1)
