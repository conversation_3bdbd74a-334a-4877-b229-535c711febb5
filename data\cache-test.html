<!DOCTYPE html>
<html>
<head>
    <title>缓存测试工具</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .clear-btn {
            background-color: #dc3545;
        }
        .clear-btn:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 HTTP缓存测试工具</h1>
        
        <div class="test-section">
            <h3>📊 缓存状态检测</h3>
            <p>测试各种文件的缓存响应头和加载时间</p>
            <button class="test-button" onclick="testModelJsonCache()">测试模型JSON缓存</button>
            <button class="test-button" onclick="testModelWeightCache()">测试权重文件缓存</button>
            <button class="test-button" onclick="testJSCache()">测试JS文件缓存</button>
            <button class="test-button" onclick="testCSSCache()">测试CSS文件缓存</button>
            <button class="test-button" onclick="testHTMLCache()">测试HTML文件缓存</button>
            <button class="test-button" onclick="testAllFiles()">测试所有文件</button>
            <div id="cache-results"></div>
        </div>

        <div class="test-section">
            <h3>⏱️ 加载时间对比</h3>
            <p>对比首次加载和缓存加载的时间差异</p>
            <button class="test-button" onclick="performanceTest()">性能测试</button>
            <button class="test-button clear-btn" onclick="clearBrowserCache()">清除缓存</button>
            <div id="performance-results"></div>
        </div>

        <div class="test-section">
            <h3>🌐 浏览器缓存信息</h3>
            <p>显示当前浏览器的缓存策略和支持情况</p>
            <button class="test-button" onclick="showBrowserInfo()">显示浏览器信息</button>
            <div id="browser-info"></div>
        </div>

        <div class="test-section">
            <h3>📋 测试说明</h3>
            <ul>
                <li><strong>首次测试</strong>：关闭开发者工具，清除缓存后进行测试</li>
                <li><strong>缓存验证</strong>：查看Network面板中的"Size"列，显示"(from disk cache)"表示缓存生效</li>
                <li><strong>响应头检查</strong>：查看Response Headers中的Cache-Control、ETag等字段</li>
                <li><strong>强制刷新</strong>：使用Ctrl+F5可以绕过缓存重新加载</li>
            </ul>
        </div>
    </div>

    <script>
        // 测试文件列表
        const testFiles = {
            modelJson: '/models/mobilenet/model.json',
            modelWeight1: '/models/mobilenet/group1-shard1of1',
            modelWeight10: '/models/mobilenet/group10-shard1of1',
            modelWeight55: '/models/mobilenet/group55-shard1of1',
            js: '/js/app.js',
            css: '/css/style.css',
            html: '/index.html'
        };

        // 显示结果的辅助函数
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }

        // 清除结果
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 测试单个文件的缓存
        async function testFileCache(url, fileName) {
            try {
                const startTime = performance.now();
                const response = await fetch(url, { cache: 'default' });
                const endTime = performance.now();
                const loadTime = (endTime - startTime).toFixed(2);

                const cacheControl = response.headers.get('Cache-Control');
                const etag = response.headers.get('ETag');
                const expires = response.headers.get('Expires');
                const lastModified = response.headers.get('Last-Modified');

                let message = `<strong>${fileName}</strong><br>`;
                message += `⏱️ 加载时间: ${loadTime}ms<br>`;
                message += `📦 Cache-Control: ${cacheControl || '未设置'}<br>`;
                message += `🏷️ ETag: ${etag || '未设置'}<br>`;
                message += `⏰ Expires: ${expires || '未设置'}<br>`;
                message += `📅 Last-Modified: ${lastModified || '未设置'}<br>`;
                message += `📊 状态: ${response.status} ${response.statusText}`;

                const type = cacheControl && cacheControl.includes('max-age') ? 'success' : 'warning';
                return { message, type, loadTime: parseFloat(loadTime) };

            } catch (error) {
                return {
                    message: `<strong>${fileName}</strong><br>❌ 错误: ${error.message}`,
                    type: 'error',
                    loadTime: 0
                };
            }
        }

        // 测试模型JSON文件缓存
        async function testModelJsonCache() {
            clearResults('cache-results');
            showResult('cache-results', '🔍 正在测试模型JSON文件缓存...', 'info');
            const result = await testFileCache(testFiles.modelJson, '模型JSON文件 (model.json)');
            showResult('cache-results', result.message, result.type);
        }

        // 测试权重文件缓存
        async function testModelWeightCache() {
            clearResults('cache-results');
            showResult('cache-results', '🔍 正在测试权重文件缓存...', 'info');

            // 测试多个权重文件
            const weightFiles = [
                { url: testFiles.modelWeight1, name: '权重文件 group1-shard1of1' },
                { url: testFiles.modelWeight10, name: '权重文件 group10-shard1of1' },
                { url: testFiles.modelWeight55, name: '权重文件 group55-shard1of1' }
            ];

            for (const file of weightFiles) {
                const result = await testFileCache(file.url, file.name);
                showResult('cache-results', result.message, result.type);
            }
        }

        // 测试所有文件
        async function testAllFiles() {
            clearResults('cache-results');
            showResult('cache-results', '🔍 正在测试所有文件的缓存...', 'info');

            const allFiles = [
                { url: testFiles.modelJson, name: '模型JSON (model.json)' },
                { url: testFiles.modelWeight1, name: '权重文件 (group1-shard1of1)' },
                { url: testFiles.js, name: 'JavaScript (app.js)' },
                { url: testFiles.css, name: 'CSS (style.css)' },
                { url: testFiles.html, name: 'HTML (index.html)' }
            ];

            for (const file of allFiles) {
                const result = await testFileCache(file.url, file.name);
                showResult('cache-results', result.message, result.type);
            }
        }

        // 测试JS文件缓存
        async function testJSCache() {
            clearResults('cache-results');
            showResult('cache-results', '🔍 正在测试JS文件缓存...', 'info');
            const result = await testFileCache(testFiles.js, 'JavaScript文件 (app.js)');
            showResult('cache-results', result.message, result.type);
        }

        // 测试CSS文件缓存
        async function testCSSCache() {
            clearResults('cache-results');
            showResult('cache-results', '🔍 正在测试CSS文件缓存...', 'info');
            const result = await testFileCache(testFiles.css, 'CSS文件 (style.css)');
            showResult('cache-results', result.message, result.type);
        }

        // 测试HTML文件缓存
        async function testHTMLCache() {
            clearResults('cache-results');
            showResult('cache-results', '🔍 正在测试HTML文件缓存...', 'info');
            const result = await testFileCache(testFiles.html, 'HTML文件 (index.html)');
            showResult('cache-results', result.message, result.type);
        }

        // 性能测试
        async function performanceTest() {
            clearResults('performance-results');
            showResult('performance-results', '⏱️ 开始性能测试...', 'info');

            const results = [];
            for (const [key, url] of Object.entries(testFiles)) {
                const result = await testFileCache(url, key);
                results.push({ key, loadTime: result.loadTime });
            }

            let message = '<strong>📊 加载时间汇总:</strong><br>';
            results.forEach(result => {
                message += `${result.key}: ${result.loadTime}ms<br>`;
            });

            const totalTime = results.reduce((sum, r) => sum + r.loadTime, 0);
            message += `<strong>总计: ${totalTime.toFixed(2)}ms</strong>`;

            showResult('performance-results', message, 'success');
        }

        // 清除浏览器缓存
        async function clearBrowserCache() {
            try {
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                    showResult('performance-results', '✅ Service Worker缓存已清除', 'success');
                } else {
                    showResult('performance-results', '⚠️ 请手动清除浏览器缓存 (Ctrl+Shift+Delete)', 'warning');
                }
            } catch (error) {
                showResult('performance-results', `❌ 清除缓存失败: ${error.message}`, 'error');
            }
        }

        // 显示浏览器信息
        function showBrowserInfo() {
            clearResults('browser-info');
            
            let message = '<strong>🌐 浏览器缓存信息:</strong><br>';
            message += `User Agent: ${navigator.userAgent}<br>`;
            message += `支持Service Worker: ${'serviceWorker' in navigator ? '✅' : '❌'}<br>`;
            message += `支持Cache API: ${'caches' in window ? '✅' : '❌'}<br>`;
            message += `支持IndexedDB: ${'indexedDB' in window ? '✅' : '❌'}<br>`;
            message += `当前协议: ${location.protocol}<br>`;
            message += `当前域名: ${location.hostname}<br>`;
            
            showResult('browser-info', message, 'info');
        }

        // 页面加载时显示基本信息
        window.addEventListener('load', () => {
            showResult('browser-info', '✅ 缓存测试工具已加载，请开始测试', 'success');
        });
    </script>
</body>
</html>
