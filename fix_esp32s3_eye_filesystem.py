#!/usr/bin/env python3
"""
ESP32-S3-EYE 文件系统一键修复脚本
自动执行完整的修复流程
"""

import subprocess
import time
import sys
import os

def run_command(command, description, timeout=120):
    """执行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🔧 {description}")
    print(f"📝 命令: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            if result.stdout:
                print("📤 输出:")
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} - 失败")
            if result.stderr:
                print("🚨 错误:")
                print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 超时")
        return False
    except Exception as e:
        print(f"💥 {description} - 异常: {e}")
        return False

def check_prerequisites():
    """检查必要的工具和文件"""
    print("🔍 检查修复前提条件...")
    
    # 检查 PlatformIO
    if not run_command("pio --version", "检查 PlatformIO", 10):
        print("❌ PlatformIO 未安装或不在 PATH 中")
        return False
    
    # 检查项目文件
    required_files = [
        "platformio.ini",
        "src/main.cpp",
        "include/camera_pins.h"
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"❌ 缺少必要文件: {file_path}")
            return False
        else:
            print(f"✅ 找到文件: {file_path}")
    
    # 检查 data 目录
    if os.path.exists("data"):
        print("✅ 找到 data 目录")
        data_files = []
        for root, dirs, files in os.walk("data"):
            for file in files:
                data_files.append(os.path.join(root, file))
        
        if data_files:
            print(f"📁 data 目录包含 {len(data_files)} 个文件:")
            for file in data_files[:5]:  # 只显示前5个
                print(f"  📄 {file}")
            if len(data_files) > 5:
                print(f"  ... 还有 {len(data_files) - 5} 个文件")
        else:
            print("⚠️  data 目录为空")
    else:
        print("⚠️  未找到 data 目录")
    
    return True

def main():
    """主修复流程"""
    print("🚀 ESP32-S3-EYE 文件系统一键修复工具")
    print("=" * 60)
    
    # 检查前提条件
    if not check_prerequisites():
        print("\n❌ 前提条件检查失败，请解决上述问题后重试")
        return False
    
    print("\n🎯 开始修复流程...")
    
    # 修复步骤
    steps = [
        ("pio run -e esp32s3_eye -t erase", "步骤 1: 完全擦除 Flash", 60),
        ("pio run -e esp32s3_eye -t clean", "步骤 2: 清理构建缓存", 30),
        ("pio run -e esp32s3_eye", "步骤 3: 重新编译固件", 120),
        ("pio run -e esp32s3_eye -t upload", "步骤 4: 上传固件", 120),
    ]
    
    # 执行修复步骤
    for command, description, timeout in steps:
        if not run_command(command, description, timeout):
            print(f"\n💥 修复失败于: {description}")
            print("🛠️  建议:")
            print("  1. 检查 ESP32-S3-EYE 连接")
            print("  2. 确认 USB 端口正确")
            print("  3. 尝试手动执行失败的命令")
            print("  4. 参考 FILESYSTEM_REPAIR_GUIDE.md")
            return False
        
        # 在关键步骤之间等待
        if "上传固件" in description:
            print("⏳ 等待设备重启...")
            time.sleep(5)
    
    # 上传文件系统
    print("\n🔄 准备上传文件系统...")
    time.sleep(2)
    
    if run_command("pio run -e esp32s3_eye -t uploadfs", "步骤 5: 上传文件系统", 120):
        print("\n🎉 文件系统修复完成!")
        
        # 启动监控
        print("\n📺 启动串口监控 (按 Ctrl+C 退出)...")
        time.sleep(2)
        
        try:
            subprocess.run("pio device monitor -e esp32s3_eye", shell=True)
        except KeyboardInterrupt:
            print("\n👋 监控已停止")
        
        print("\n✅ 修复流程完成!")
        print("🔍 请检查串口输出确认文件系统正常工作")
        return True
    else:
        print("\n⚠️  文件系统上传失败")
        print("💡 可能的原因:")
        print("  1. data 目录为空或不存在")
        print("  2. 设备未正确重启")
        print("  3. Flash 仍有问题")
        print("\n🔄 尝试手动上传:")
        print("  pio run -e esp32s3_eye -t uploadfs")
        return False

def show_help():
    """显示帮助信息"""
    print("""
ESP32-S3-EYE 文件系统修复工具

用法:
  python fix_esp32s3_eye_filesystem.py

修复流程:
  1. 检查必要文件和工具
  2. 完全擦除 Flash
  3. 清理构建缓存
  4. 重新编译固件
  5. 上传固件
  6. 上传文件系统
  7. 启动串口监控

注意事项:
  - 确保 ESP32-S3-EYE 已连接
  - 确保没有其他程序占用串口
  - 修复过程可能需要 5-10 分钟
  - 修复会清除所有现有数据

如需帮助，请参考:
  - FILESYSTEM_REPAIR_GUIDE.md
  - QUICK_START_ESP32S3_EYE.md
""")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        show_help()
    else:
        try:
            success = main()
            sys.exit(0 if success else 1)
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            sys.exit(1)
        except Exception as e:
            print(f"\n💥 意外错误: {e}")
            sys.exit(1)
