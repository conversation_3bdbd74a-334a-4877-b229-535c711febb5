    async disconnect() {
    // 防止重复断开操作
    if (this.isDisconnecting) {
        console.log('断开操作已在进行中');
        return;
    }

    this.isDisconnecting = true;

    // 标记为用户请求的断开
    this.disconnectRequested = true;

    // 更新UI显示断开状态
    const statusElement = document.getElementById('status');
    if (statusElement) {
        statusElement.innerText = '状态: 正在断开蓝牙设备...';
    }

    if (this.device && this.connected) {
        try {
            // 先发送停止命令，但先检查是否可以发送
            if (this.rx_char && !this.sendInProgress) {
                try {
                    console.log("断开前发送停止命令...");
                    const stopCommand = "stop";
                    const cmdData = bleSerialBroadcastCmd(stopCommand);

                    // 直接使用特性写入，而不是调用可能会被拒绝的write_data方法
                    await this.rx_char.writeValueWithoutResponse(cmdData);
                    console.log("停止命令发送成功");

                    // 等待短暂时间，确保命令被发送
                    await new Promise(resolve => setTimeout(resolve, 300));
                } catch (cmdError) {
                    console.error("发送停止命令失败:", cmdError);
                    // 即使发送失败，仍继续断开过程
                }
            } else {
                console.log("无法发送停止命令：特性不可用或发送正在进行中");
            }

            // 安全停止通知
            await this.stopNotificationsSafely();

            // 等待简短时间以确保通知已停止
            await new Promise(resolve => setTimeout(resolve, 100));

            // 检查设备是否仍然连接
            if (this.device.gatt.connected) {
                console.log('断开设备连接...');
                this.device.gatt.disconnect();
                console.log('断开命令已发送');
            } else {
                console.log('设备已断开连接');
            }
        } catch (error) {
            console.error('断开过程中出错:', error);
        }
    } else {
        console.log('设备已断开或未连接，无需断开');
    }

    // 始终清理状态
    this.cleanupConnection();

    // 更新UI
    const connButton = document.getElementById('connBle');
    const disconnButton = document.getElementById('disconnBle');

    if (statusElement) {
        statusElement.innerText = '状态: 蓝牙设备已断开连接';
    }
    if (connButton) connButton.disabled = false;
    if (disconnButton) disconnButton.disabled = true;

    console.log('蓝牙设备断开连接完成，状态已重置');

    // 重置断开连接标记
    this.isDisconnecting = false;
}

isConnected() {
    // 确保在断开连接过程中不报告为已连接
    return this.connected && this.rx_char != null && !this.isDisconnecting;
}
getDeviceName() {
    return this.deviceName;
}