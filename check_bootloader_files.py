#!/usr/bin/env python3
"""
检查可用的 ESP32-S3 bootloader 文件
帮助确定正确的 Flash 频率配置
"""

import os
import glob

def find_bootloader_files():
    """查找可用的 bootloader 文件"""
    print("🔍 查找 ESP32-S3 bootloader 文件...")
    
    # 常见的 PlatformIO ESP32 路径
    base_paths = [
        os.path.expanduser("~/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/bin/"),
        "C:/Users/<USER>/AppData/Local/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/bin/",
        "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/bin/"
    ]
    
    bootloader_files = []
    
    for base_path in base_paths:
        # 展开通配符路径
        expanded_paths = glob.glob(base_path)
        
        for path in expanded_paths:
            if os.path.exists(path):
                print(f"📁 检查目录: {path}")
                
                # 查找 bootloader 文件
                bootloader_pattern = os.path.join(path, "bootloader_*.elf")
                files = glob.glob(bootloader_pattern)
                
                for file in files:
                    filename = os.path.basename(file)
                    bootloader_files.append((filename, file))
                    print(f"  ✅ 找到: {filename}")
    
    return bootloader_files

def analyze_bootloader_files(bootloader_files):
    """分析 bootloader 文件并推荐配置"""
    print(f"\n📊 分析结果 (找到 {len(bootloader_files)} 个文件):")
    
    if not bootloader_files:
        print("❌ 未找到任何 bootloader 文件")
        print("💡 可能的解决方案:")
        print("  1. 重新安装 ESP32 平台: pio platform install espressif32")
        print("  2. 更新 PlatformIO: pio upgrade")
        return None
    
    # 分析文件名模式
    flash_modes = set()
    flash_freqs = set()
    
    for filename, filepath in bootloader_files:
        print(f"\n📄 {filename}")
        
        # 解析文件名
        parts = filename.replace('.elf', '').split('_')
        if len(parts) >= 3:
            mode = parts[1] if len(parts) > 1 else "unknown"
            freq = parts[2] if len(parts) > 2 else "unknown"
            
            flash_modes.add(mode)
            flash_freqs.add(freq)
            
            print(f"  Flash 模式: {mode}")
            print(f"  Flash 频率: {freq}")
    
    print(f"\n🔧 可用配置:")
    print(f"  Flash 模式: {', '.join(sorted(flash_modes))}")
    print(f"  Flash 频率: {', '.join(sorted(flash_freqs))}")
    
    # 推荐配置
    print(f"\n💡 推荐配置:")
    
    if "qio" in flash_modes and "80m" in flash_freqs:
        print("  ✅ 推荐: QIO + 80MHz")
        return {"mode": "qio", "freq": "80m"}
    elif "qio" in flash_modes and "40m" in flash_freqs:
        print("  ✅ 推荐: QIO + 40MHz")
        return {"mode": "qio", "freq": "40m"}
    elif flash_modes and flash_freqs:
        mode = sorted(flash_modes)[0]
        freq = sorted(flash_freqs)[0]
        print(f"  ⚠️  备选: {mode} + {freq}")
        return {"mode": mode, "freq": freq}
    else:
        print("  ❌ 无法确定推荐配置")
        return None

def generate_platformio_config(config):
    """生成 platformio.ini 配置建议"""
    if not config:
        return
    
    print(f"\n📝 建议的 platformio.ini 配置:")
    print("```ini")
    print("[env:esp32s3_eye]")
    print("platform = espressif32")
    print("board = esp32-s3-devkitc-1")
    print("framework = arduino")
    print()
    print("board_build.partitions = partitions.csv")
    print("board_build.arduino.memory_type = qio_opi")
    print(f"board_build.flash_mode = {config['mode']}")
    print(f"board_build.flash_freq = {config['freq']}")
    print("board_build.psram_type = opi")
    print()
    print("upload_speed = 115200")
    print("upload_port = COM*")
    print()
    print("build_flags = -DCAMERA_MODEL_ESP32S3_EYE")
    print("```")

def check_current_config():
    """检查当前 platformio.ini 配置"""
    print("\n🔍 检查当前 platformio.ini 配置...")
    
    if not os.path.exists("platformio.ini"):
        print("❌ 未找到 platformio.ini 文件")
        return
    
    with open("platformio.ini", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 提取当前配置
    import re
    
    flash_mode = re.search(r'board_build\.flash_mode\s*=\s*(\w+)', content)
    flash_freq = re.search(r'board_build\.flash_freq\s*=\s*(\w+)', content)
    upload_speed = re.search(r'upload_speed\s*=\s*(\d+)', content)
    
    print("📋 当前配置:")
    print(f"  Flash 模式: {flash_mode.group(1) if flash_mode else '未设置'}")
    print(f"  Flash 频率: {flash_freq.group(1) if flash_freq else '未设置'}")
    print(f"  上传速度: {upload_speed.group(1) if upload_speed else '未设置'}")

def main():
    """主函数"""
    print("🚀 ESP32-S3 Bootloader 文件检查工具")
    print("=" * 50)
    
    # 检查当前配置
    check_current_config()
    
    # 查找 bootloader 文件
    bootloader_files = find_bootloader_files()
    
    # 分析并推荐配置
    config = analyze_bootloader_files(bootloader_files)
    
    # 生成配置建议
    generate_platformio_config(config)
    
    print(f"\n🎯 下一步建议:")
    if config:
        print("1. 根据上述建议更新 platformio.ini")
        print("2. 运行: pio run -e esp32s3_eye -t clean")
        print("3. 运行: pio run -e esp32s3_eye")
    else:
        print("1. 重新安装 ESP32 平台")
        print("2. 检查 PlatformIO 安装")
        print("3. 尝试使用默认配置")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"💥 检查过程出错: {e}")
        print("请手动检查 PlatformIO ESP32 平台安装")
