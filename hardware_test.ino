
/*
 * ESP32-S3-EYE 硬件信息检测
 * 编译并上传此代码到设备，通过串口查看详细硬件信息
 */

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("\n" + String("=").substring(0, 50));
  Serial.println("🔍 ESP32-S3-EYE 硬件信息检测");
  Serial.println(String("=").substring(0, 50));
  
  // 基本芯片信息
  Serial.println("\n📱 芯片信息:");
  Serial.printf("  芯片型号: %s\n", ESP.getChipModel());
  Serial.printf("  芯片版本: %d\n", ESP.getChipRevision());
  Serial.printf("  CPU 频率: %d MHz\n", ESP.getCpuFreqMHz());
  Serial.printf("  CPU 核心数: %d\n", ESP.getChipCores());
  
  // Flash 信息
  Serial.println("\n💾 Flash 信息:");
  Serial.printf("  Flash 大小: %d KB (%.1f MB)\n", 
                ESP.getFlashChipSize() / 1024, 
                ESP.getFlashChipSize() / 1024.0 / 1024.0);
  Serial.printf("  Flash 速度: %d MHz\n", ESP.getFlashChipSpeed() / 1000000);
  Serial.printf("  Flash 模式: %d\n", ESP.getFlashChipMode());
  
  // PSRAM 信息
  Serial.println("\n🧠 PSRAM 信息:");
  if (psramFound()) {
    Serial.println("  PSRAM: ✅ 检测到");
    Serial.printf("  PSRAM 大小: %d KB (%.1f MB)\n", 
                  ESP.getPsramSize() / 1024,
                  ESP.getPsramSize() / 1024.0 / 1024.0);
    Serial.printf("  PSRAM 可用: %d KB\n", ESP.getFreePsram() / 1024);
  } else {
    Serial.println("  PSRAM: ❌ 未检测到");
  }
  
  // 内存信息
  Serial.println("\n🔋 内存信息:");
  Serial.printf("  总内存: %d KB\n", ESP.getHeapSize() / 1024);
  Serial.printf("  可用内存: %d KB\n", ESP.getFreeHeap() / 1024);
  Serial.printf("  最大分配: %d KB\n", ESP.getMaxAllocHeap() / 1024);
  
  // MAC 地址
  Serial.println("\n🌐 网络信息:");
  Serial.printf("  WiFi MAC: %s\n", WiFi.macAddress().c_str());
  
  // 分区信息
  Serial.println("\n📊 分区信息:");
  const esp_partition_t* running = esp_ota_get_running_partition();
  if (running) {
    Serial.printf("  运行分区: %s\n", running->label);
    Serial.printf("  分区大小: %d KB\n", running->size / 1024);
  }
  
  Serial.println("\n✅ 硬件检测完成");
  Serial.println(String("=").substring(0, 50));
}

void loop() {
  delay(10000);
}
