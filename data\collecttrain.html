<!DOCTYPE html>
<html>

<head>
    <title>自动驾驶小车数据采集模型训练二合一（MobileNet+MLP）</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .img-container {
            white-space: nowrap;
            overflow-x: auto;
            background-color: #d6e2f1;
            padding: 5px;
            margin: 0
        }

        .img-container img {
            height: 60px;
            width: 60px;
            padding: 5px
        }

        #canvasOutput {
            display: none
        }

        .btn {
            width: 100%;
            max-width: 180px;
        }

        .blebtn,
        .btn {
            height: 50px;
            margin: 10px;
            font-size: large
        }

        .blebtn {
            width: 120px;
        }

        #topic {
            width: 100px
        }

        .category {
            margin-bottom: 10px;
            background-color: #d6e2f1;
            border-radius: 10px;
            width: 100%;
            max-width: 760px;
        }

        .category img {
            height: 60px;
            width: 60px;
            padding: 5px;
            cursor: pointer
        }

        .preview {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            width: 100%;
            max-width: 760px;
            margin: 0 auto
        }

        .clear-btn {
            width: 100%;
            max-width: 760px;
            padding: 0;
            text-align: center;
            margin: 0 auto
        }

        .collect-btn {
            width: 100%;
            max-width: 130px;
            margin: 10px;
        }

        .clear-down {
            margin: 5px;
        }

        .drop-area {
            width: 100%;
            max-width: 650px;
            margin: 0px auto;
            padding: 0px;
            text-align: center;
        }

        #status,
        #warn {
            color: #f50404
        }

        textarea.code {
            width: 100%;
            max-width: 100%;
            font-family: monospace;
            white-space: pre;
            overflow-x: auto;
            resize: vertical;
        }

        .button-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }

        /* 媒体查询 - 小屏幕设备 */
        @media (max-width: 768px) {
            .btn, .blebtn {
                margin: 5px;
                font-size: medium;
                height: 40px;
            }
            
            .collect-btn {
                max-width: 100px;
            }
            
            .category img {
                height: 50px;
                width: 50px;
            }
            
            textarea.code {
                font-size: 0.9rem;
            }
            
            h3, h4 {
                font-size: 1.2rem;
            }
        }

        /* 媒体查询 - 超小屏幕设备 */
        @media (max-width: 576px) {
            .btn, .blebtn {
                margin: 3px;
                font-size: small;
                height: 35px;
            }
            
            .collect-btn {
                max-width: 80px;
            }
            
            .category img {
                height: 40px;
                width: 40px;
            }
            
            textarea.code {
                font-size: 0.8rem;
            }
            
            h3, h4 {
                font-size: 1rem;
            }
        }

        #model-summary {
            width: 100%;
            max-width: 800px;
            overflow-x: auto;
            font-family: monospace;
            white-space: nowrap;
        }

        #model-summary div {
            white-space: pre;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            #model-summary div {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 576px) {
            #model-summary div {
                font-size: 0.7rem;
            }
        }

        #loss-canvas {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            overflow-x: auto;
        }
        
        #loss-canvas canvas {
            margin: 0 auto;
            display: block;
        }

        @media (max-width: 768px) {
            #loss-canvas canvas {
                max-width: 100%;
                height: auto;
            }
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light mb-3">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">ESP32-S3 AI 摄像头</a>
            <div class="navbar-nav">
                <a class="nav-link" href="/">主页</a>
                <a class="nav-link active" href="/collecttrain.html">数据采集训练</a>
            </div>
        </div>
    </nav>

    <div class="d-flex justify-content-center">
        <div class="p-2">
            <h3 class="mt-4 text-center">人工智能自动驾驶小车MobileNet+MLP</h3>
            <h4 class="mt-4 text-center">数据采集模型训练二合一</h4>
            <p class="text-center text-muted">使用 ESP32-S3 WebSocket 摄像头流</p>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="p-2">
            <img id="webcam" width="224" height="224" style="border: 1px solid #ccc; background-color: #f0f0f0;" alt="ESP32 摄像头流">
            <div id="camera-status" style="text-align: center; margin-top: 5px; font-size: 12px; color: #666;">
                正在连接摄像头...
            </div>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="p-2"><canvas id="canvasOutput"></canvas></div>
    </div>
    <div class="d-flex justify-content-center flex-wrap"> 
        <button id="connect" class="blebtn btn-primary">连接蓝牙</button> 
        <button id="disconnect" class="blebtn btn-primary" disabled>断开蓝牙</button> 
    </div>
    <div class="d-flex justify-content-center flex-wrap"> 
        <label for="maxImagesCount">允许采集的图片数目: </label> 
        <input type="number" id="maxImagesCount" value="1000" step="100" title="在这里输入允许最多采集图片数量，不建议超过1500"> 
    </div>
    <div class="d-flex justify-content-center">
        <div id="imageCount"></div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="custom-control custom-switch"> 
            <input type="checkbox" class="custom-control-input" id="trueFalseToggleSwitch"> 
            <label class="custom-control-label" for="trueFalseToggleSwitch">连续采集（收到一次指令会连续采集多张图片）</label> 
        </div>
    </div>
    <div class="d-flex justify-content-center flex-wrap button-container">
        <button id="goCollectBtn" class="btn btn-primary collect-btn" title="" data-category="go">采集go</button>
        <button id="leftCollectBtn" class="btn btn-primary collect-btn" title="" data-category="left">采集left</button>
        <button id="rightCollectBtn" class="btn btn-primary collect-btn" title="" data-category="right">采集right</button>
        <button id="stopCollectBtn" class="btn btn-primary collect-btn" title="" data-category="stop">采集stop</button>
    </div>
    <div class="d-flex justify-content-center flex-wrap button-container">
        <button id="download" class="btn btn-primary clear-down" title="打包压缩下载图片数据集">下载数据集</button>
        <button id="clear" class="btn btn-danger clear-down" title="清空所有图片分类数据">清空所有数据</button>
        <button id="importDataset" class="btn btn-info clear-down" title="上传之前下载的zip压缩数据集">上传数据集</button>
    </div>
    <div class="d-flex justify-content-center">
        <div class="category p-2">
            <div class="drop-area" id="drop-area-go" title="不需要的图片，点击后可以删除。">
                <input type="file" id="fileElemGo" multiple accept="image/*" style="display:none">
                <div class="d-flex justify-content-center flex-wrap">
                    <button class="btn btn-primary" id="addGoImage">添加go图像文件</button>
                    <button class="btn btn-danger clear-category" id="clearGoImages" data-category="go"
                        title="清空当前分类所有图片">清空go分类图片</button>
                </div>
                <p id="count-go">样本数: 0</p>
            </div>
            <div class="preview" id="goImages"> </div>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="category p-2">
            <div class="drop-area" id="drop-area-left" title="不需要的图片，点击后可以删除。">
                <input type="file" id="fileElemLeft" multiple accept="image/*" style="display:none">
                <div class="d-flex justify-content-center flex-wrap">
                    <button class="btn btn-primary" id="addLeftImage">添加left图像文件</button>
                    <button class="btn btn-danger clear-category" id="clearLeftImages" data-category="left"
                        title="清空当前分类所有图片">清空left分类图片</button>
                </div>
                <p id="count-left">样本数: 0</p>
            </div>
            <div class="preview" id="leftImages"> </div>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="category p-2">
            <div class="drop-area" id="drop-area-right" title="不需要的图片，点击后可以删除。">
                <input type="file" id="fileElemRight" multiple accept="image/*" style="display:none">
                <div class="d-flex justify-content-center flex-wrap">
                    <button class="btn btn-primary" id="addRightImage">添加right图像文件</button>
                    <button class="btn btn-danger clear-category" id="clearRightImages" data-category="right"
                        title="清空当前分类所有图片">清空right分类图片</button>
                </div>
                <p id="count-right">样本数: 0</p>
            </div>
            <div class="preview" id="rightImages"> </div>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="category p-2">
            <div class="drop-area" id="drop-area-stop" title="不需要的图片，点击后可以删除。">
                <input type="file" id="fileElemStop" multiple accept="image/*" style="display:none">
                <div class="d-flex justify-content-center flex-wrap">
                    <button class="btn btn-primary" id="addStopImage">添加stop图像文件</button>
                    <button class="btn btn-danger clear-category" id="clearRightImages" data-category="stop"
                        title="清空当前分类所有图片">清空stop分类图片</button>
                </div>
                <p id="count-stop">样本数: 0</p>
            </div>

            <div class="preview" id="stopImages"> </div>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="p-2">
            <div id="bleDiv"></div>
        </div>
    </div>
    <div class="d-flex justify-content-center flex-wrap">
        <div class="p-2"><label for="validationSplit">验证集分割比例 Validation Split:</label>
            <select id="validationSplit">
                <option value="0.05">5%</option>
                <option value="0.10">10%</option>
                <option value="0.15" selected>15%</option>
                <option value="0.20">20%</option>
                <option value="0.25">25%</option>
            </select>
        </div>
    </div>
    <div class="d-flex justify-content-center flex-wrap">
        <div class="p-2"><label for="learningRate">学习率Learning Rate:</label>
            <input type="number" id="learningRate" value="0.0001" step="0.0001">
        </div>
    </div>
    <div class="d-flex justify-content-center flex-wrap">
        <div class="p-2"><label for="batchSize">批量大小Batch Size:</label>
            <input type="number" id="batchSize" value="64" step="16">
        </div>
    </div>
    <div class="d-flex justify-content-center flex-wrap">
        <div class="p-2"><label for="epochs">训练轮数Epoch:</label>
            <input type="number" id="epochs" value="20">
        </div>
    </div>
    <div class="d-flex justify-content-center flex-wrap">
        <div class="p-2"><label for="dense-units">隐藏层神经元数量dense units:</label>
            <select id="dense-units">
                <option value="10">10</option>
                <option selected="" value="50">50</option>
                <option value="100">100</option>
                <option value="200">200</option>
            </select>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="p-2">
            <div>神经网络代码：</div>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="p-2" style="width: 100%; max-width: 800px;">
            <textarea class="code" rows="10" id="codeEditor" spellcheck="false"></textarea>
            <script id="codeSnippet" type="text/code-snippet">
model = tf.sequential();
model.add(tf.layers.flatten({
        inputShape: truncatedMobileNet.outputs[0].shape.slice(1)// [7, 7, 256]
    })); // 输入层，扁平化层
                
model.add(tf.layers.dense({
        units: denseUnits,
        activation: 'relu',
        kernelInitializer: 'varianceScaling',
        useBias: true
    })); // 隐藏层，全连接层1
                
model.add(tf.layers.dense({
        units: numClasses,
        kernelInitializer: 'varianceScaling',
        useBias: false,
        activation: 'softmax'
    })); // 输出层，全连接层2

initializeModel(model, tf, truncatedMobileNet, numClasses, denseUnits); // 初始化模型，不要修改这一行
            </script>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="p-2" style="width: 100%; max-width: 800px;">
            <div id="model-summary"></div>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="p-2">
            <div id="warn" class="mt-3"></div>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div class="p-2">
            <div id="status" class="mt-3">页面载入中，请稍后...</div>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <div id="loss-canvas"></div>
    </div>
    <div class="d-flex justify-content-center flex-wrap button-container">
        <button class="btn btn-success mt-3" id="trainModel" title="开始训练模型，注意训练中不要切换页面。">训练模型</button>
        <button class="btn btn-info mt-3" id="downloadModel" title="下载保存训练生成的模型文件">下载模型</button>
        <button id="downloadDataset" class="btn btn-primary mt-3" title="保存自己的数据集">保存数据集</button>
        <button id="downloadDatasetSample" class="btn btn-primary mt-3" title="下载小车自动驾驶示例数据集">示例数据集</button>
        <button class="btn btn-info mt-3" id="saveModelIndexedDB" title="保存模型到本地缓存">保存模型到缓存</button>
    </div>
    <div class="d-flex justify-content-center">
        <div class="p-2">
            <div class="mt-3 text-center">开发：上海市松江区青少年综合实践教育中心 汤铭</div>
        </div>
    </div>
    <!-- JS文件加载 - 服务器会自动选择gzip版本（如果存在）或未压缩版本 -->
    <script src="/js/jquery-3.6.0.min.js"></script>
    <script src="/js/jszip.min.js"></script>
    <script src="/js/file-saver.js"></script>
    <script src="/js/tfjs.js"></script>
    <script src="/js/tfjs-vis.js"></script>
    <script src="/js/collecttrain.js"></script>
</body>

</html> 