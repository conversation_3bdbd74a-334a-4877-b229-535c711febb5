<!DOCTYPE html>
<html>

<head>
    <title>AI自动驾驶小车-数据采集训练</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/css/style.css">
    <style>
        /* 数据采集训练页面特定样式 */
        .img-container {
            white-space: nowrap;
            overflow-x: auto;
            background-color: #f8f9fa;
            padding: 10px;
            margin: 10px auto;
            border-radius: 8px;
            border: 1px solid #ddd;
            max-width: 600px;
        }

        .img-container img {
            height: 60px;
            width: 60px;
            padding: 5px;
            border-radius: 4px;
            border: 1px solid #ccc;
            cursor: pointer;
        }

        #canvasOutput {
            display: none;
        }

        .category {
            margin: 15px auto;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #ddd;
            max-width: 600px;
        }

        .category h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.1em;
        }

        .training-section {
            margin: 20px auto;
            padding: 20px;
            background-color: #f0f8ff;
            border-radius: 8px;
            border: 1px solid #b3d9ff;
            max-width: 600px;
        }

        .training-section h3 {
            color: #0066cc;
            margin-bottom: 15px;
        }

        .model-info {
            background-color: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 0.9em;
            color: #2e7d32;
        }

        .progress-container {
            margin: 15px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }

        .metrics-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .metric-item {
            background-color: #fff;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            text-align: center;
        }

        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #0066cc;
        }

        .metric-label {
            font-size: 0.9em;
            color: #666;
        }

        .collect-btn {
            width: 100%;
            max-width: 130px;
            margin: 5px;
        }

        .data-management-section {
            margin: 20px auto;
            padding: 15px;
            background-color: #fff8e1;
            border-radius: 8px;
            border: 1px solid #ffcc02;
            max-width: 600px;
        }

        .data-management-section h4 {
            color: #f57c00;
            margin-bottom: 15px;
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        .input-group label {
            font-weight: bold;
            min-width: 120px;
        }

        .input-group input[type="number"] {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100px;
        }

        #warn {
            color: #f50404;
            margin: 10px auto;
            max-width: 600px;
            text-align: center;
        }

        #model-summary {
            width: 100%;
            max-width: 600px;
            margin: 15px auto;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 8px;
            border: 1px solid #ddd;
            font-family: monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }

        #loss-canvas {
            width: 100%;
            max-width: 600px;
            margin: 15px auto;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .collect-btn {
                max-width: 100px;
                font-size: 0.9em;
            }
            
            .category img {
                height: 50px;
                width: 50px;
            }
            
            .input-group {
                flex-direction: column;
                align-items: center;
            }
            
            .input-group label {
                min-width: auto;
                text-align: center;
            }
        }

        @media (max-width: 480px) {
            .collect-btn {
                max-width: 80px;
                font-size: 0.8em;
                height: 40px;
            }
            
            .category img {
                height: 40px;
                width: 40px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h3>AI自动驾驶小车-数据采集训练</h3>

        <!-- 导航链接 -->
        <div class="nav-links" style="text-align: center; margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
            <a href="/" style="margin: 0 10px; text-decoration: none; color: #007bff;">🏠 主页</a>
            <a href="/collecttrain.html" style="margin: 0 10px; text-decoration: none; color: #28a745; font-weight: bold;">📊 数据采集训练</a>
        </div>

        <!-- 摄像头显示区域 -->
        <div class="image-container">
            <img id="webcam" crossorigin alt="Camera Stream" width="224" height="224" />
        </div>
        
        <div id="status">状态: 等待摄像头连接...</div>
        <div id="warn"></div>
        <canvas id="canvasOutput" width="224" height="224"></canvas>

        <!-- 数据采集计数显示 -->
        <div class="sample-counts">
            <span class="sample-counter counter-go">go(<span id="go-count">0</span>)</span>
            <span class="sample-counter counter-left">left(<span id="left-count">0</span>)</span>
            <span class="sample-counter counter-right">right(<span id="right-count">0</span>)</span>
            <span class="sample-counter counter-stop">stop(<span id="stop-count">0</span>)</span>
        </div>

        <!-- 蓝牙连接控制 -->
        <div class="control-section">
            <div class="buttons-row">
                <button id="connect" class="btn">连接蓝牙</button>
                <button id="disconnect" class="btn" disabled>断开蓝牙</button>
            </div>
        </div>

        <!-- 数据采集设置 -->
        <div class="data-management-section">
            <h4>📊 数据采集设置</h4>
            
            <div class="input-group">
                <label for="maxImagesCount">最大图片数量:</label>
                <input type="number" id="maxImagesCount" value="1000" step="100" title="允许最多采集图片数量，不建议超过1500">
            </div>
            
            <div class="switch-container">
                <label for="trueFalseToggleSwitch">连续采集模式:</label>
                <label class="switch">
                    <input type="checkbox" id="trueFalseToggleSwitch">
                    <span class="slider round"></span>
                </label>
                <span id="continuous-status">已禁用</span>
            </div>
            
            <div id="imageCount" style="text-align: center; margin: 10px 0; font-weight: bold;"></div>
        </div>

        <!-- 数据采集按钮 -->
        <div class="button-group">
            <button id="goCollectBtn" class="btn collect-btn" data-category="go" style="background-color: #2196F3;">采集go</button>
            <button id="leftCollectBtn" class="btn collect-btn" data-category="left" style="background-color: #FF9800;">采集left</button>
            <button id="rightCollectBtn" class="btn collect-btn" data-category="right" style="background-color: #9C27B0;">采集right</button>
            <button id="stopCollectBtn" class="btn collect-btn" data-category="stop" style="background-color: #E91E63;">采集stop</button>
        </div>

        <!-- 数据管理按钮 -->
        <div class="button-group">
            <button id="download" class="btn" style="background-color: #007bff;" title="打包压缩下载图片数据集">下载数据集</button>
            <button id="clear" class="btn" style="background-color: #f44336;" title="清空所有图片分类数据">清空所有数据</button>
            <button id="importDataset" class="btn" style="background-color: #17a2b8;" title="上传之前下载的zip压缩数据集">上传数据集</button>
        </div>

        <!-- 数据采集区域 -->
        <div class="category" id="go">
            <h4>go 类别数据 (<span id="go-count-display">0</span>)</h4>
            <div class="img-container" id="go-container"></div>
        </div>
        <div class="category" id="left">
            <h4>left 类别数据 (<span id="left-count-display">0</span>)</h4>
            <div class="img-container" id="left-container"></div>
        </div>
        <div class="category" id="right">
            <h4>right 类别数据 (<span id="right-count-display">0</span>)</h4>
            <div class="img-container" id="right-container"></div>
        </div>
        <div class="category" id="stop">
            <h4>stop 类别数据 (<span id="stop-count-display">0</span>)</h4>
            <div class="img-container" id="stop-container"></div>
        </div>

        <!-- 模型训练部分 -->
        <div class="training-section">
            <h3>🤖 模型训练</h3>
            
            <div class="model-info">
                <strong>模型架构:</strong> MobileNet + MLP<br>
                <strong>输入尺寸:</strong> 224x224x3<br>
                <strong>输出类别:</strong> go, left, right, stop
            </div>
            
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="training-progress"></div>
                </div>
                <div style="text-align: center; margin-top: 5px; font-size: 0.9em;" id="progress-text">准备训练...</div>
            </div>
            
            <div class="metrics-display">
                <div class="metric-item">
                    <div class="metric-value" id="current-epoch">0</div>
                    <div class="metric-label">当前轮次</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="current-loss">0.000</div>
                    <div class="metric-label">损失值</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="current-accuracy">0.0%</div>
                    <div class="metric-label">准确率</div>
                </div>
            </div>
            
            <div class="button-group">
                <button class="btn" id="trainModel" style="background-color: #28a745;" title="开始训练模型">训练模型</button>
                <button class="btn" id="downloadModel" style="background-color: #17a2b8;" title="下载训练生成的模型文件">下载模型</button>
            </div>
            
            <div class="button-group">
                <button id="downloadDataset" class="btn" style="background-color: #007bff;" title="保存自己的数据集">保存数据集</button>
                <button id="downloadDatasetSample" class="btn" style="background-color: #6f42c1;" title="下载示例数据集">示例数据集</button>
                <button class="btn" id="saveModelIndexedDB" style="background-color: #fd7e14;" title="保存模型到本地缓存">保存到缓存</button>
            </div>
        </div>

        <!-- 模型摘要显示 -->
        <div id="model-summary"></div>
        
        <!-- 训练损失图表 -->
        <div id="loss-canvas"></div>
    </div>

    <div class="container">
        <div>开发：上海市松江区青少年综合实践教育中心 汤铭</div> 
    </div>

    <!-- JS文件加载 - 服务器会自动选择gzip版本（如果存在）或未压缩版本 -->
    <script src="/js/jquery-3.6.0.min.js"></script>
    <script src="/js/jszip.min.js"></script>
    <script src="/js/file-saver.js"></script>
    <script src="/js/tfjs.js"></script>
    <script src="/js/tfjs-vis.js"></script>
    <script src="/js/collecttrain.js"></script>
</body>

</html>
