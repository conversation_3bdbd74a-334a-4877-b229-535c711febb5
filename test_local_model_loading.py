#!/usr/bin/env python3
"""
测试本地模型加载功能
验证 collecttrain.js 是否正确修改为从本地文件系统加载 MobileNet 模型
"""

import os
import re

def test_model_files():
    """测试模型文件是否存在"""
    print("🔍 检查 MobileNet 模型文件...")
    
    # 检查主要模型文件
    model_json = "data/models/mobilenet/model.json"
    
    print("📋 主要模型文件检查:")
    if os.path.exists(model_json):
        print(f"  ✅ {model_json}")
        
        # 检查文件大小
        file_size = os.path.getsize(model_json)
        print(f"    文件大小: {file_size} bytes")
    else:
        print(f"  ❌ {model_json} - 缺失")
        return False
    
    # 检查分片文件
    print("\n📋 模型分片文件检查:")
    missing_shards = []
    total_shards = 55
    
    for i in range(1, total_shards + 1):
        shard_file = f"data/models/mobilenet/group{i}-shard1of1"
        if os.path.exists(shard_file):
            if i <= 5 or i > total_shards - 5:  # 只显示前5个和后5个
                print(f"  ✅ group{i}-shard1of1")
        else:
            missing_shards.append(f"group{i}-shard1of1")
    
    if len(missing_shards) == 0:
        print(f"  ✅ 所有 {total_shards} 个分片文件都存在")
    else:
        print(f"  ❌ 缺失 {len(missing_shards)} 个分片文件:")
        for shard in missing_shards[:5]:  # 只显示前5个缺失的
            print(f"    - {shard}")
        if len(missing_shards) > 5:
            print(f"    ... 还有 {len(missing_shards) - 5} 个")
    
    return len(missing_shards) == 0

def test_server_routes():
    """测试服务器模型路由"""
    print("\n🔍 检查服务器模型路由...")
    
    if not os.path.exists("src/camera_server.cpp"):
        print("❌ 未找到 src/camera_server.cpp")
        return False
    
    with open("src/camera_server.cpp", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查模型路由
    checks = {
        "model.json 路由": '"/models/mobilenet/model.json"' in content,
        "分片路由循环": 'for (int i = 1; i <= 55; i++)' in content,
        "分片路径模板": '"/models/mobilenet/group%d-shard1of1"' in content,
        "模型路由注册": 'httpd_register_uri_handler(camera_httpd, &model_json_uri)' in content
    }
    
    print("📋 服务器路由检查:")
    all_good = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def test_collecttrain_js_modifications():
    """测试 collecttrain.js 的修改"""
    print("\n🔍 检查 collecttrain.js 的本地模型加载修改...")
    
    if not os.path.exists("data/js/collecttrain.js"):
        print("❌ 未找到 data/js/collecttrain.js")
        return False
    
    with open("data/js/collecttrain.js", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查关键修改
    checks = {
        "createLimitedFetch 函数": "function createLimitedFetch" in content,
        "本地模型路径": "'/models/mobilenet/model.json'" in content,
        "本地加载尝试": "尝试从本地文件系统加载MobileNet模型" in content,
        "网络备用方案": "尝试从网络加载" in content,
        "限制并发fetch": "fetchFunc: createLimitedFetch(3)" in content,
        "错误处理": "本地模型加载失败" in content
    }
    
    print("📋 collecttrain.js 修改检查:")
    all_good = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    # 检查是否移除了直接的网络URL
    old_network_url = "https://storage.googleapis.com/tfjs-models/tfjs/mobilenet_v1_0.25_224/model.json"
    if old_network_url in content:
        # 检查是否作为备用方案
        if "备用方案" in content or "networkUrl" in content:
            print("  ✅ 网络URL作为备用方案保留")
        else:
            print("  ⚠️  网络URL仍作为主要加载方式")
            all_good = False
    else:
        print("  ❌ 网络URL被完全移除 (应该保留作为备用)")
        all_good = False
    
    return all_good

def test_app_js_consistency():
    """测试与 app.js 的一致性"""
    print("\n🔍 检查与 app.js 的一致性...")
    
    files_to_check = ["data/js/app.js", "data/js/collecttrain.js"]
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ 未找到 {file_path}")
            return False
    
    # 读取两个文件
    with open("data/js/app.js", "r", encoding="utf-8") as f:
        app_content = f.read()
    
    with open("data/js/collecttrain.js", "r", encoding="utf-8") as f:
        collecttrain_content = f.read()
    
    # 检查一致性
    consistency_checks = {
        "模型路径一致": "'/models/mobilenet/model.json'" in app_content and "'/models/mobilenet/model.json'" in collecttrain_content,
        "createLimitedFetch 函数": "createLimitedFetch" in app_content and "createLimitedFetch" in collecttrain_content,
        "conv_pw_13_relu 层": "conv_pw_13_relu" in app_content and "conv_pw_13_relu" in collecttrain_content,
        "fetchFunc 参数": "fetchFunc: createLimitedFetch" in app_content and "fetchFunc: createLimitedFetch" in collecttrain_content
    }
    
    print("📋 与 app.js 一致性检查:")
    all_good = True
    for check_name, result in consistency_checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def generate_recommendations():
    """生成建议"""
    print("\n💡 本地模型加载机制说明:")
    print("1. 优先尝试从本地文件系统加载: /models/mobilenet/model.json")
    print("2. 使用限制并发的 fetch 函数避免过载")
    print("3. 本地加载失败时自动切换到网络加载")
    print("4. 提供详细的状态反馈和错误信息")
    
    print("\n🎯 优势:")
    print("- ✅ 离线可用 (本地模型)")
    print("- ✅ 加载速度快 (无网络延迟)")
    print("- ✅ 节省带宽 (不依赖外部网络)")
    print("- ✅ 可靠性高 (网络备用方案)")
    print("- ✅ 并发控制 (避免服务器过载)")

def main():
    """主测试函数"""
    print("🚀 本地模型加载功能测试")
    print("=" * 50)
    
    tests = [
        ("MobileNet 模型文件", test_model_files),
        ("服务器模型路由", test_server_routes),
        ("collecttrain.js 修改", test_collecttrain_js_modifications),
        ("与 app.js 一致性", test_app_js_consistency)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            print(f"✅ {test_name} 测试通过")
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    # 生成建议
    generate_recommendations()
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 本地模型加载功能配置正确!")
        print("\n📝 下一步操作:")
        print("1. 编译并上传固件:")
        print("   pio run -e seeed_xiao_esp32s3_sense -t upload")
        print("2. 上传文件系统 (包含模型文件):")
        print("   pio run -e seeed_xiao_esp32s3_sense -t uploadfs")
        print("3. 访问数据采集训练页面:")
        print("   http://[设备IP]/collecttrain.html")
        print("4. 验证模型从本地加载成功")
        return True
    else:
        print("⚠️  发现问题，请根据测试结果进行修复")
        return False

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except Exception as e:
        print(f"💥 测试过程出错: {e}")
        exit(1)
