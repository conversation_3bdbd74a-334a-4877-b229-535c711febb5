#include "app_config.h"

// 摄像头初始化函数，适配OV2640 (XIAO ESP32S3 Sense)
bool initCamera() {
  // 初始化I2C总线，这可能有助于解决某些摄像头的通信问题
  Serial.println("初始化I2C总线...");
  
  // 尝试使用硬件I2C初始化，用于摄像头SCCB通信
  Wire.begin(SIOD_GPIO_NUM, SIOC_GPIO_NUM);
  Wire.setClock(100000); // 设置为100kHz，OV2640需要较低频率
  
  // 扫描I2C设备，查找摄像头地址
  Serial.println("扫描I2C总线上的设备...");
  int deviceCount = 0;
  for (byte address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    byte error = Wire.endTransmission();
    if (error == 0) {
      Serial.printf("I2C设备发现在地址 0x%02X\n", address);
      deviceCount++;
      
      // 常见的OV摄像头地址是0x60或0x3C
      if (address == 0x60 || address == 0x3C || address == 0x42) {
        Serial.println("可能找到摄像头传感器!");
        // OV2640的I2C地址通常是0x60
        if (address == 0x60) {
          Serial.println("检测到OV2640摄像头传感器!");
        }
      }
    }
  }
  
  if (deviceCount == 0) {
    Serial.println("未找到I2C设备，可能是连接问题或I2C地址不兼容");
  }

  // 配置摄像头
  Serial.println("配置摄像头...");
  camera_config_t config;
  config.ledc_channel = LEDC_CHANNEL_0;
  config.ledc_timer = LEDC_TIMER_0;
  config.pin_d0 = Y2_GPIO_NUM;
  config.pin_d1 = Y3_GPIO_NUM;
  config.pin_d2 = Y4_GPIO_NUM;
  config.pin_d3 = Y5_GPIO_NUM;
  config.pin_d4 = Y6_GPIO_NUM;
  config.pin_d5 = Y7_GPIO_NUM;
  config.pin_d6 = Y8_GPIO_NUM;
  config.pin_d7 = Y9_GPIO_NUM;
  config.pin_xclk = XCLK_GPIO_NUM;
  config.pin_pclk = PCLK_GPIO_NUM;
  config.pin_vsync = VSYNC_GPIO_NUM;
  config.pin_href = HREF_GPIO_NUM;
  config.pin_sccb_sda = SIOD_GPIO_NUM;
  config.pin_sccb_scl = SIOC_GPIO_NUM;
  config.pin_pwdn = PWDN_GPIO_NUM;
  config.pin_reset = RESET_GPIO_NUM;
  config.xclk_freq_hz = 20000000;  // 20MHz时钟频率 - 提高帧率
  config.pixel_format = PIXFORMAT_RGB565;  // 使用RGB565格式，OV2640支持这种格式

  // 针对AI分类优化的配置 - 使用240x240正方形格式
  if (psramFound()) {
    Serial.printf("检测到PSRAM: %d KB\n", ESP.getPsramSize() / 1024);
    Serial.println("配置AI分类模式 (PSRAM可用) - 240x240分辨率");
    config.frame_size = FRAMESIZE_240X240;  // 240x240 - 正方形格式，适合AI分类
    config.jpeg_quality = 30;               // 中等质量，平衡速度和清晰度
    config.fb_count = 2;                    // 双缓冲，平衡性能和内存
    config.fb_location = CAMERA_FB_IN_PSRAM; // 使用PSRAM
  } else {
    Serial.println("未检测到PSRAM，使用AI分类优化配置");
    config.frame_size = FRAMESIZE_240X240;  // 240x240 - 正方形格式，适合AI分类
    config.jpeg_quality = 50;               // 稍低质量以节省内存
    config.fb_count = 1;                    // 单缓冲节省内存
    config.fb_location = CAMERA_FB_IN_DRAM; // 使用内部RAM
  }

  // 设置抓取模式
  config.grab_mode = CAMERA_GRAB_WHEN_EMPTY;
  
  // 初始化摄像头
  Serial.println("初始化摄像头(使用RGB565格式)...");
  esp_err_t err = esp_camera_init(&config);
  
  // 如果初始化失败，尝试不同的配置
  if (err != ESP_OK) {
    Serial.printf("RGB565格式初始化失败，错误代码: 0x%x\n", err);
    Serial.println("尝试使用YUV422格式...");
    
    // 尝试YUV422格式
    config.pixel_format = PIXFORMAT_YUV422;
    config.xclk_freq_hz = 8000000;       // 8MHz
    err = esp_camera_init(&config);
    
    if (err != ESP_OK) {
      Serial.printf("YUV422格式初始化失败，错误代码: 0x%x\n", err);
      Serial.println("尝试使用GRAYSCALE格式...");
      
      // 尝试灰度格式
      config.pixel_format = PIXFORMAT_GRAYSCALE;
      config.frame_size = FRAMESIZE_240X240; // 240x240，保持正方形格式
      config.xclk_freq_hz = 5000000;         // 5MHz
      err = esp_camera_init(&config);
      
      if (err != ESP_OK) {
        Serial.printf("GRAYSCALE格式初始化失败，错误代码: 0x%x\n", err);
        Serial.println("尝试使用最低配置...");
        
        // 最低配置
        config.frame_size = FRAMESIZE_96X96; // 96x96，最小分辨率
        config.xclk_freq_hz = 2000000;        // 2MHz，非常低的时钟频率
        config.fb_count = 1;
        err = esp_camera_init(&config);
        
        if (err != ESP_OK) {
          Serial.printf("最低配置初始化失败，错误代码: 0x%x\n", err);
          return false;
        }
      }
    }
  }

  Serial.println("摄像头初始化成功!");

  // 获取摄像头传感器信息并进行优化配置
  sensor_t *s = esp_camera_sensor_get();
  if (s != NULL) {
    Serial.printf("摄像头传感器ID: 0x%x\n", s->id.PID);

    // 针对OV2640优化设置 - 改善画面偏暗问题
    Serial.println("配置OV2640摄像头参数...");
    
    // 亮度设置 - 提高亮度
    s->set_brightness(s, 1);     // 0 -> 1 (提高亮度)
    Serial.println("亮度设置为: 1");
    
    // 对比度设置 - 增强对比度
    s->set_contrast(s, 1);       // 0 -> 1 (增强对比度)
    Serial.println("对比度设置为: 1");
    
    // 饱和度设置 - 保持适中
    s->set_saturation(s, 0);     // 保持默认
    Serial.println("饱和度设置为: 0");
    
    // 特效设置
    s->set_special_effect(s, 0); // 无特效
    Serial.println("特效设置为: 无");
    
    // 白平衡设置 - 启用自动白平衡
    s->set_whitebal(s, 1);       // 启用自动白平衡
    s->set_awb_gain(s, 1);       // 启用自动白平衡增益
    s->set_wb_mode(s, 0);        // 自动白平衡模式
    Serial.println("白平衡设置为: 自动");
    
    // 曝光控制 - 优化曝光设置
    s->set_exposure_ctrl(s, 1);  // 启用自动曝光控制
    s->set_aec2(s, 1);           // 启用AEC2 (0 -> 1)
    s->set_ae_level(s, 1);       // 提高曝光等级 (0 -> 1)
    s->set_aec_value(s, 400);    // 增加曝光值 (300 -> 400)
    Serial.println("曝光控制设置为: 自动, AEC2启用, 等级1, 值400");
    
    // 增益控制 - 优化增益设置
    s->set_gain_ctrl(s, 1);      // 启用自动增益控制
    s->set_agc_gain(s, 5);       // 增加增益 (0 -> 5)
    s->set_gainceiling(s, (gainceiling_t)2);  // 提高增益上限 (0 -> 2)
    Serial.println("增益控制设置为: 自动, 增益5, 上限2");
    
    // 图像质量设置
    s->set_bpc(s, 1);            // 启用坏点校正 (0 -> 1)
    s->set_wpc(s, 1);            // 启用白点校正
    s->set_raw_gma(s, 1);        // 启用伽马校正
    s->set_lenc(s, 1);           // 启用镜头阴影校正
    Serial.println("图像质量设置: 启用所有校正");
    
    // 图像方向设置
    s->set_hmirror(s, 0);        // 不水平镜像
    s->set_vflip(s, 0);          // 不垂直翻转
    Serial.println("图像方向: 正常");
    
    // 其他设置
    s->set_dcw(s, 1);            // 启用下采样
    s->set_colorbar(s, 0);       // 禁用彩条测试
    Serial.println("其他设置: 下采样启用, 彩条禁用");

    Serial.println("摄像头设置优化完成");
  } else {
    Serial.println("无法获取摄像头传感器信息");
  }

  return true;
}

// 摄像头参数调整函数 - 可以通过串口命令调用
void adjustCameraSettings(int brightness, int contrast, int saturation, int exposure_level, int gain) {
  sensor_t *s = esp_camera_sensor_get();
  if (s != NULL) {
    Serial.println("调整摄像头参数...");
    
    // 设置亮度 (-2 到 2)
    if (brightness >= -2 && brightness <= 2) {
      s->set_brightness(s, brightness);
      Serial.printf("亮度设置为: %d\n", brightness);
    }
    
    // 设置对比度 (-2 到 2)
    if (contrast >= -2 && contrast <= 2) {
      s->set_contrast(s, contrast);
      Serial.printf("对比度设置为: %d\n", contrast);
    }
    
    // 设置饱和度 (-2 到 2)
    if (saturation >= -2 && saturation <= 2) {
      s->set_saturation(s, saturation);
      Serial.printf("饱和度设置为: %d\n", saturation);
    }
    
    // 设置曝光等级 (-2 到 2)
    if (exposure_level >= -2 && exposure_level <= 2) {
      s->set_ae_level(s, exposure_level);
      Serial.printf("曝光等级设置为: %d\n", exposure_level);
    }
    
    // 设置增益 (0 到 30)
    if (gain >= 0 && gain <= 30) {
      s->set_agc_gain(s, gain);
      Serial.printf("增益设置为: %d\n", gain);
    }
    
    Serial.println("摄像头参数调整完成");
  } else {
    Serial.println("无法获取摄像头传感器，参数调整失败");
  }
}

// 获取当前摄像头参数
void printCameraSettings() {
  sensor_t *s = esp_camera_sensor_get();
  if (s != NULL) {
    Serial.println("当前摄像头参数:");
    Serial.printf("亮度: %d\n", s->status.brightness);
    Serial.printf("对比度: %d\n", s->status.contrast);
    Serial.printf("饱和度: %d\n", s->status.saturation);
    Serial.printf("曝光等级: %d\n", s->status.ae_level);
    Serial.printf("增益: %d\n", s->status.agc_gain);
    Serial.printf("白平衡: %s\n", s->status.awb ? "启用" : "禁用");
    Serial.printf("自动曝光: %s\n", s->status.aec ? "启用" : "禁用");
  } else {
    Serial.println("无法获取摄像头传感器信息");
  }
} 