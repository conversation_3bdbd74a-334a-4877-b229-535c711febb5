# 🚀 XIAO ESP32S3 Sense 快速开始指南

## 📋 硬件要求

- ✅ Seeed Studio XIAO ESP32S3 Sense 开发板
- ✅ OV2640 摄像头模块 (已集成)
- ✅ USB Type-C 数据线
- ✅ 电脑 (Windows/Mac/Linux)

## ⚡ 快速开始

### 1. 连接硬件

1. 使用USB Type-C线连接XIAO ESP32S3 Sense到电脑
2. 确保摄像头模块正确连接 (开发板上已集成)

### 2. 构建和上传

```bash
# 使用XIAO ESP32S3 Sense专用环境
pio run -e seeed_xiao_esp32s3_sense

# 上传固件
pio run -e seeed_xiao_esp32s3_sense --target upload

# 监控串口输出
pio device monitor
```

### 3. 配置WiFi

通过串口监视器配置WiFi：

```
AT+CWJAP="你的WiFi名称","你的WiFi密码"
AT+RST
```

### 4. 访问Web界面

- 浏览器访问: `http://设备IP地址`
- 或者: `http://esp32.local` (如果支持mDNS)

## 🔧 配置说明

### 开发板配置
- **芯片**: ESP32-S3
- **Flash**: 8MB
- **PSRAM**: 8MB
- **摄像头**: OV2640

### 引脚定义
```cpp
XCLK: GPIO 10
SIOD: GPIO 40
SIOC: GPIO 39
Y9: GPIO 48
Y8: GPIO 11
Y7: GPIO 12
Y6: GPIO 14
Y5: GPIO 16
Y4: GPIO 18
Y3: GPIO 17
Y2: GPIO 15
VSYNC: GPIO 38
HREF: GPIO 47
PCLK: GPIO 13
```

## 🎯 功能特性

- ✅ 实时摄像头视频流
- ✅ Web界面控制
- ✅ AI分类功能 (TensorFlow.js)
- ✅ WebSocket实时通信
- ✅ 蓝牙连接支持
- ✅ 响应式Web界面

## 🔍 故障排除

### 常见问题

1. **上传失败**
   - 检查USB连接
   - 确认开发板型号选择正确
   - 尝试降低上传速度

2. **摄像头不工作**
   - 检查摄像头连接
   - 确认引脚定义正确
   - 查看串口错误信息

3. **WiFi连接失败**
   - 检查WiFi凭据
   - 确认信号强度
   - 尝试AP模式

### 调试命令

```bash
# 清理构建缓存
pio run -e seeed_xiao_esp32s3_sense --target clean

# 详细构建信息
pio run -e seeed_xiao_esp32s3_sense -v

# 查看可用端口
pio device list
```

## 📊 性能指标

- **启动时间**: ~3秒
- **视频帧率**: 60fps (目标)
- **分辨率**: 240x240 (AI优化)
- **内存使用**: 优化配置

## 🎮 使用示例

### AI分类训练

1. 打开Web界面
2. 等待TensorFlow.js模型加载
3. 点击方向按钮添加训练样本:
   - `go` - 前进
   - `left` - 左转
   - `right` - 右转
   - `stop` - 停止
4. 添加足够样本后自动开始分类

### 蓝牙控制

1. 启用"发送指令到小车"开关
2. 连接蓝牙设备
3. 实时控制小车移动

## 📚 更多信息

- 📖 详细文档: [XIAO_ESP32S3_SENSE_MIGRATION.md](XIAO_ESP32S3_SENSE_MIGRATION.md)
- 🔧 项目配置: [platformio.ini](platformio.ini)
- 📁 源代码: [src/](src/) 目录

## 🤝 技术支持

如遇到问题：
1. 查看串口输出
2. 检查硬件连接
3. 参考故障排除部分
4. 查看详细迁移文档

---

**🎉 恭喜！你的XIAO ESP32S3 Sense项目已准备就绪！** 