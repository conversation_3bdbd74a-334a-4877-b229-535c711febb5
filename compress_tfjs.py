#!/usr/bin/env python3
import gzip
import shutil
import os

def compress_file(input_path, output_path):
    """压缩文件为gzip格式"""
    try:
        with open(input_path, 'rb') as f_in:
            with gzip.open(output_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        # 获取文件大小信息
        original_size = os.path.getsize(input_path)
        compressed_size = os.path.getsize(output_path)
        compression_ratio = (1 - compressed_size / original_size) * 100
        
        print(f"压缩完成!")
        print(f"原始文件: {input_path} ({original_size:,} bytes)")
        print(f"压缩文件: {output_path} ({compressed_size:,} bytes)")
        print(f"压缩率: {compression_ratio:.1f}%")
        print(f"节省空间: {original_size - compressed_size:,} bytes")
        
    except Exception as e:
        print(f"压缩失败: {e}")

if __name__ == "__main__":
    input_file = "data/js/bundle.js"
    output_file = "data/js/bundle.js.gz"
    
    if os.path.exists(input_file):
        compress_file(input_file, output_file)
    else:
        print(f"文件不存在: {input_file}") 