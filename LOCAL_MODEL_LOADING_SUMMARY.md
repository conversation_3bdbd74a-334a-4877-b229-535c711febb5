# 本地模型加载功能实现总结

## 🎯 目标

将 `data/js/collecttrain.js` 中的 MobileNet 模型加载方式从网络加载改为本地文件系统加载，参考 `data/js/app.js` 的实现方式。

## ✅ 已完成的修改

### 1. 添加限制并发连接的 fetch 函数

参考 `app.js` 的实现，添加了 `createLimitedFetch` 函数：

```javascript
// 创建限制并发连接的fetch函数 (参考 app.js)
function createLimitedFetch(maxConcurrent = 3) {
    let activeRequests = 0;
    const queue = [];

    return function limitedFetch(url, options) {
        return new Promise((resolve, reject) => {
            const executeRequest = () => {
                activeRequests++;
                fetch(url, options)
                    .then(response => {
                        activeRequests--;
                        processQueue();
                        resolve(response);
                    })
                    .catch(error => {
                        activeRequests--;
                        processQueue();
                        reject(error);
                    });
            };

            const processQueue = () => {
                if (queue.length > 0 && activeRequests < maxConcurrent) {
                    const nextRequest = queue.shift();
                    nextRequest();
                }
            };

            if (activeRequests < maxConcurrent) {
                executeRequest();
            } else {
                queue.push(executeRequest);
            }
        });
    };
}
```

### 2. 修改 `loadTruncatedMobileNet` 函数

#### 原来的实现（网络加载）：
```javascript
async function loadTruncatedMobileNet() {
    document.getElementById('status').innerText = 'MobileNet预训练模型加载中...';
    try {
        const mobilenet = await tf.loadLayersModel('https://storage.googleapis.com/tfjs-models/tfjs/mobilenet_v1_0.25_224/model.json');
        const layer = mobilenet.getLayer('conv_pw_13_relu');
        document.getElementById('status').innerText = 'MobileNet 预训练模型加载完成';
        return tf.model({ inputs: mobilenet.inputs, outputs: layer.output });
    } catch (error) {
        console.error('Error loading model:', error);
    }
}
```

#### 现在的实现（本地优先，网络备用）：
```javascript
async function loadTruncatedMobileNet() {
    document.getElementById('status').innerText = 'MobileNet预训练模型加载中...';
    try {
        let loadedModel;
        const modelPath = '/models/mobilenet/model.json';

        try {
            console.log("尝试从本地文件系统加载MobileNet模型...");
            document.getElementById('status').innerText = '正在从本地文件系统加载模型...';
            
            // 使用限制并发的fetch函数从本地加载 (参考 app.js)
            loadedModel = await tf.loadLayersModel(modelPath, { fetchFunc: createLimitedFetch(3) });
            console.log("本地MobileNet模型加载成功");
            document.getElementById('status').innerText = '本地模型加载成功';
        } catch (e) {
            console.warn("本地模型加载失败，尝试从网络加载:", e);
            document.getElementById('status').innerText = '本地加载失败，尝试网络加载...';
            
            try {
                // 备用方案：从网络加载
                const networkUrl = 'https://storage.googleapis.com/tfjs-models/tfjs/mobilenet_v1_0.25_224/model.json';
                loadedModel = await tf.loadLayersModel(networkUrl);
                console.log("网络MobileNet模型加载成功");
                document.getElementById('status').innerText = '网络模型加载成功';
            } catch (networkError) {
                console.error("网络模型加载也失败:", networkError);
                document.getElementById('status').innerText = '模型加载失败，请检查网络连接';
                throw networkError;
            }
        }

        // Return a model that outputs an internal activation.
        const layer = loadedModel.getLayer('conv_pw_13_relu');
        document.getElementById('status').innerText = 'MobileNet 预训练模型加载完成';

        return tf.model({ inputs: loadedModel.inputs, outputs: layer.output });

    } catch (error) {
        console.error('MobileNet模型加载失败:', error);
        document.getElementById('status').innerText = 'MobileNet模型加载失败';
        throw error;
    }
}
```

## 🔧 技术实现要点

### 1. 本地优先策略
- **第一步**：尝试从本地文件系统加载 `/models/mobilenet/model.json`
- **第二步**：如果本地加载失败，自动切换到网络加载
- **状态反馈**：实时更新加载状态信息

### 2. 并发控制
- 使用 `createLimitedFetch(3)` 限制最多3个并发请求
- 避免同时加载过多模型文件导致服务器过载
- 实现请求队列机制，按顺序处理请求

### 3. 错误处理和备用方案
- **本地加载失败**：自动切换到网络加载
- **网络加载失败**：显示明确的错误信息
- **状态显示**：每个阶段都有对应的状态提示

### 4. 与 app.js 的一致性
- 使用相同的模型路径：`/models/mobilenet/model.json`
- 使用相同的并发控制机制：`createLimitedFetch(3)`
- 使用相同的模型层：`conv_pw_13_relu`
- 保持相同的错误处理模式

## 📁 文件系统结构

### 模型文件位置
```
data/models/mobilenet/
├── model.json                 # 模型结构定义
├── group1-shard1of1           # 模型权重分片 1
├── group2-shard1of1           # 模型权重分片 2
├── ...
└── group55-shard1of1          # 模型权重分片 55
```

### 服务器路由配置
```cpp
// 模型主文件路由
httpd_uri_t model_json_uri = {
  .uri = "/models/mobilenet/model.json",
  .method = HTTP_GET,
  .handler = static_file_handler,
  .user_ctx = NULL
};

// 模型分片文件路由 (1-55)
for (int i = 1; i <= 55; i++) {
  snprintf(uri_paths[i-1], sizeof(uri_paths[i-1]), "/models/mobilenet/group%d-shard1of1", i);
  // ... 路由配置
}
```

## 🚀 加载流程

### 成功的本地加载流程
```
1. 用户访问 collecttrain.html
2. 页面加载完成，开始初始化模型
3. 显示状态: "MobileNet预训练模型加载中..."
4. 尝试本地加载: "/models/mobilenet/model.json"
5. 显示状态: "正在从本地文件系统加载模型..."
6. 使用 createLimitedFetch(3) 控制并发
7. 成功加载模型和所有分片文件
8. 显示状态: "本地模型加载成功"
9. 提取 conv_pw_13_relu 层
10. 显示状态: "MobileNet 预训练模型加载完成"
```

### 备用网络加载流程
```
1-4. (同上)
5. 本地加载失败
6. 显示状态: "本地加载失败，尝试网络加载..."
7. 从网络URL加载模型
8. 显示状态: "网络模型加载成功"
9-10. (同上)
```

## 💡 优势和特点

### 1. 性能优势
- **加载速度快**：本地文件无网络延迟
- **离线可用**：不依赖外部网络连接
- **节省带宽**：避免重复下载大型模型文件

### 2. 可靠性
- **双重保障**：本地+网络双重备用方案
- **错误恢复**：自动切换到备用加载方式
- **状态透明**：清晰的加载状态反馈

### 3. 服务器友好
- **并发控制**：避免同时请求过多文件
- **资源管理**：合理分配网络资源
- **负载均衡**：请求队列机制

## 📝 使用说明

### 部署要求
1. **模型文件**：确保 `data/models/mobilenet/` 目录包含所有必需文件
2. **文件系统上传**：运行 `pio run -e seeed_xiao_esp32s3_sense -t uploadfs`
3. **服务器路由**：确保模型文件路由已正确配置

### 验证方法
1. 访问 `http://[设备IP]/collecttrain.html`
2. 观察浏览器控制台日志
3. 查看状态显示信息
4. 确认模型加载成功

### 故障排除
- **本地加载失败**：检查文件系统是否正确上传
- **网络加载失败**：检查网络连接和防火墙设置
- **模型初始化失败**：检查 TensorFlow.js 库是否正确加载

---

现在 `collecttrain.html` 页面的 MobileNet 模型加载已经完全本地化，与 `index.html` 保持一致的加载策略，提供了更好的性能和可靠性！
