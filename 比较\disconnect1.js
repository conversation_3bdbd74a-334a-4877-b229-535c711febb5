async disconnect() {
    // 避免重复断开操作
    if (isDisconnecting) {
        console.log('断开连接操作已经在进行中');
        return;
    }

    isDisconnecting = true;

    // 标记这是用户请求的断开
    this.disconnectRequested = true;

    // 更新UI以反映断开中状态
    this.updateUI('正在停止小车并断开蓝牙连接...', false, true);

    if (this.device && this.server && this.connected) {
        try {
            // 先发送stop指令停止小车
            try {
                document.getElementById('status').innerHTML = '<span style="color:#ff6600">正在发送停止指令...</span>';
                await sendCommand('stop');
                document.getElementById('status').innerHTML = '<span style="color:#33cc33">停止指令已发送，正在断开连接...</span>';

                // 等待较长时间确保stop指令处理完成
                await sleep(DISCONNECT_DELAY);
            } catch (error) {
                console.log("发送停止指令错误：" + error);
                document.getElementById('status').innerHTML = '<span style="color:#ff0000">停止指令发送失败，正在断开连接...</span>';
            }

            // 安全地尝试停止通知
            await this.stopNotificationsSafely();

            // 短暂等待以确保通知已停止
            await sleep(100);

            // 发送系统重置指令 (OpCode: 0x0F)
            try {
                const resetCommand = new Uint8Array([250, 15, 0]); // 0xFA(250), 0x0F(15), 0
                await this.rx_char.writeValue(resetCommand);
                console.log("系统重置指令已发送");

                // 等待短暂时间确保指令处理
                await sleep(DISCONNECT_DELAY);
            } catch (error) {
                console.log("发送重置指令错误：" + error);
            }

            console.log('正在断开与设备的连接...');
            this.device.gatt.disconnect();
            console.log('断开连接成功');
        } catch (error) {
            console.error('断开连接时出错:', error);
        }
    } else {
        console.log('无需断开连接，因为设备已经断开或未连接');
    }

    // 无论如何都确保清理状态，但保留设备信息以便重连
    this.cleanupConnection(false); // 设置为false，不清除设备缓存

    // 更新UI
    this.updateUI('蓝牙设备已断开');
    console.log('断开蓝牙设备完成，状态已重置');

    // 重置断开状态标志
    isDisconnecting = false;
}

isConnected() {
    return this.connected && this.rx_char != null && !isDisconnecting;
}

getDeviceName() {
    return this.device ? this.device.name : '未知设备';
}