#!/usr/bin/env python3
"""
分析 model.json 文件，确定实际需要的权重文件
"""

import json
import os

def analyze_model_json():
    """分析 model.json 文件"""
    model_json_path = "data/models/mobilenet/model.json"
    
    if not os.path.exists(model_json_path):
        print(f"❌ 文件不存在: {model_json_path}")
        return
    
    print("🔍 分析 model.json 文件...")
    
    try:
        with open(model_json_path, 'r') as f:
            model_data = json.load(f)
        
        # 获取权重清单
        weights_manifest = model_data.get('weightsManifest', [])
        
        print(f"📊 权重清单条目数量: {len(weights_manifest)}")
        print("\n📋 权重文件列表:")
        
        all_files = []
        for i, manifest in enumerate(weights_manifest):
            paths = manifest.get('paths', [])
            weights = manifest.get('weights', [])
            
            print(f"\n组 {i+1}:")
            print(f"  文件: {paths}")
            print(f"  权重数量: {len(weights)}")
            
            if weights:
                weight_names = [w.get('name', 'unknown') for w in weights[:3]]  # 只显示前3个
                print(f"  权重名称: {weight_names}{'...' if len(weights) > 3 else ''}")
            
            all_files.extend(paths)
        
        print(f"\n📈 统计:")
        print(f"  总权重组数: {len(weights_manifest)}")
        print(f"  总文件数: {len(all_files)}")
        print(f"  预期文件范围: group1-shard1of1 到 group{len(all_files)}-shard1of1")
        
        # 检查实际文件存在情况
        print(f"\n🔍 检查实际文件存在情况:")
        model_dir = "data/models/mobilenet"
        
        missing_files = []
        existing_files = []
        
        for file_name in all_files:
            file_path = os.path.join(model_dir, file_name)
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                existing_files.append((file_name, file_size))
            else:
                missing_files.append(file_name)
        
        print(f"  ✅ 存在的文件: {len(existing_files)}")
        print(f"  ❌ 缺失的文件: {len(missing_files)}")
        
        if missing_files:
            print(f"\n❌ 缺失的文件列表:")
            for file_name in missing_files:
                print(f"    - {file_name}")
        
        # 检查多余的文件
        print(f"\n🔍 检查多余的文件:")
        actual_files = []
        for file_name in os.listdir(model_dir):
            if file_name.startswith('group') and file_name.endswith('-shard1of1'):
                actual_files.append(file_name)
        
        expected_files_set = set(all_files)
        actual_files_set = set(actual_files)
        
        extra_files = actual_files_set - expected_files_set
        
        if extra_files:
            print(f"  ⚠️  多余的文件: {len(extra_files)}")
            for file_name in sorted(extra_files):
                print(f"    - {file_name}")
        else:
            print(f"  ✅ 没有多余的文件")
        
        # 分析问题文件
        print(f"\n🎯 问题文件分析:")
        problem_files = ['group49-shard1of1', 'group50-shard1of1', 'group51-shard1of1', 
                        'group52-shard1of1', 'group53-shard1of1', 'group54-shard1of1', 'group55-shard1of1']
        
        for file_name in problem_files:
            if file_name in expected_files_set:
                print(f"  ✅ {file_name} - 在 model.json 中需要")
            else:
                print(f"  ❌ {file_name} - 在 model.json 中不需要")
        
        return len(all_files), missing_files, extra_files
        
    except Exception as e:
        print(f"💥 分析失败: {e}")
        return None, None, None

def main():
    """主函数"""
    print("🚀 MobileNet model.json 分析工具")
    print("=" * 50)
    
    expected_count, missing_files, extra_files = analyze_model_json()
    
    if expected_count is not None:
        print(f"\n{'='*50}")
        print("分析结论:")
        print(f"  model.json 期望文件数: {expected_count}")
        print(f"  缺失文件数: {len(missing_files) if missing_files else 0}")
        print(f"  多余文件数: {len(extra_files) if extra_files else 0}")
        
        if expected_count <= 48:
            print(f"\n💡 重要发现:")
            print(f"  model.json 只需要 {expected_count} 个文件")
            print(f"  group49-group55 可能是多余的文件")
            print(f"  这解释了为什么这些文件返回 404 错误")
        
        print(f"\n🛠️  建议:")
        print(f"1. 检查 model.json 的实际需求")
        print(f"2. 移除多余的文件或路由")
        print(f"3. 更新服务器路由配置")

if __name__ == "__main__":
    main()
