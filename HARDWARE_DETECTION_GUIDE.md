# ESP32-S3-EYE 硬件检测指南

## 🎯 目标

检测 ESP32-S3-EYE 开发板的实际硬件规格，包括：
- Flash 存储大小
- PSRAM 大小
- 芯片信息
- 分区配置
- 内存使用情况

## 🛠️ 检测方法

### 方法 1: 使用硬件检测代码（推荐）

这是最准确的方法，直接从设备读取硬件信息。

#### 步骤：
1. **备份当前代码**：
   ```bash
   cp src/main.cpp src/main.cpp.backup
   ```

2. **替换检测代码**：
   ```bash
   cp hardware_detection_sketch.cpp src/main.cpp
   ```

3. **编译并上传**：
   ```bash
   pio run -e esp32s3_eye -t upload
   ```

4. **查看检测结果**：
   ```bash
   pio device monitor -e esp32s3_eye
   ```

5. **恢复原代码**：
   ```bash
   cp src/main.cpp.backup src/main.cpp
   ```

#### 预期输出示例：
```
🚀 ESP32-S3-EYE 硬件信息检测

============================================================
🔍 芯片基本信息
============================================================
📱 芯片型号: ESP32-S3
📱 芯片版本: 0
⚡ CPU 频率: 240 MHz
🔢 CPU 核心数: 2
🏭 芯片特性: WiFi Bluetooth BLE 
🌐 WiFi MAC: 24:6F:28:XX:XX:XX

============================================================
🔍 Flash 存储信息
============================================================
💾 Flash 大小: 16777216 bytes (16.0 MB)
⚡ Flash 速度: 80 MHz
🔧 Flash 模式: QIO
📝 程序大小: 1234 KB
📝 程序可用空间: 5678 KB

============================================================
🔍 PSRAM 信息
============================================================
🧠 PSRAM: ✅ 检测到
📏 PSRAM 总大小: 8388608 bytes (8.0 MB)
💚 PSRAM 可用: 8000000 bytes (7.6 MB)
📊 PSRAM 使用率: 4.6%

============================================================
🔍 分区表信息
============================================================
🏃 运行分区: app0
📍 分区地址: 0x10000
📏 分区大小: 1280 KB

📋 所有分区:
  📄 nvs: DATA (Other, 20 KB)
  📄 otadata: DATA (Other, 8 KB)
  📄 app0: APP (Other, 1280 KB)
  📄 app1: APP (Other, 1280 KB)
  📄 spiffs: DATA (SPIFFS, 1408 KB)
```

### 方法 2: 使用 esptool 命令行工具

#### 检测 Flash 信息：
```bash
esptool.py --port COM11 flash_id
```

#### 检测芯片信息：
```bash
esptool.py --port COM11 chip_id
```

#### 读取分区表：
```bash
esptool.py --port COM11 read_flash 0x8000 0x1000 partition_table.bin
```

### 方法 3: 使用自动检测脚本

```bash
python detect_esp32s3_hardware.py
```

这个脚本会：
- 自动检测串口
- 运行 esptool 命令
- 分析 platformio.ini 配置
- 生成硬件检测代码

## 📊 ESP32-S3-EYE 标准规格

### 官方规格：
- **芯片**: ESP32-S3 (双核 Xtensa LX7 @ 240MHz)
- **Flash**: 16MB
- **PSRAM**: 8MB OPI PSRAM
- **摄像头**: OV2640 (2MP)
- **显示屏**: 1.3英寸 LCD (240x240)
- **连接**: USB-C, WiFi, Bluetooth

### 分区布局（参考）：
```
Name      Type    SubType   Offset    Size
nvs       data    nvs       0x9000    20KB
otadata   data    ota       0xe000    8KB
app0      app     ota_0     0x10000   1280KB
app1      app     ota_1     0x150000  1280KB
spiffs    data    spiffs    0x290000  1408KB
```

## 🔍 故障排除

### 问题 1: 检测到的 Flash 小于 16MB
**可能原因**：
- 开发板型号不是 ESP32-S3-EYE
- Flash 芯片损坏
- 固件配置错误

**解决方案**：
- 检查开发板标签确认型号
- 尝试重新烧录固件
- 联系供应商确认规格

### 问题 2: 未检测到 PSRAM
**可能原因**：
- PSRAM 配置未启用
- 硬件连接问题
- 固件编译配置错误

**解决方案**：
- 检查 `platformio.ini` 中的 PSRAM 配置
- 确认 `board_build.psram_type = opi`
- 重新编译固件

### 问题 3: 分区表与预期不符
**可能原因**：
- `partitions.csv` 配置错误
- 使用了错误的分区表
- Flash 大小配置不匹配

**解决方案**：
- 检查 `partitions.csv` 文件
- 确认分区大小总和不超过 Flash 容量
- 重新烧录分区表

## 📝 检测报告模板

使用硬件检测后，您可以填写以下报告：

```
ESP32-S3-EYE 硬件检测报告
========================

检测时间: [日期时间]
固件版本: [版本号]

硬件规格:
- 芯片型号: [ESP32-S3]
- Flash 大小: [实际大小] MB
- PSRAM 大小: [实际大小] MB
- CPU 频率: [频率] MHz

配置状态:
- PSRAM 启用: [是/否]
- Flash 模式: [QIO/DIO/等]
- 分区表: [正常/异常]

问题记录:
- [记录发现的任何问题]

建议:
- [基于检测结果的建议]
```

## 🔗 相关文档

- [ESP32-S3-EYE 快速开始指南](QUICK_START_ESP32S3_EYE.md)
- [文件系统修复指南](FILESYSTEM_REPAIR_GUIDE.md)
- [文件系统调试指南](filesystem_debug_guide.md)

---

**注意**: 硬件检测是诊断问题的第一步。如果检测结果与预期不符，请参考相应的故障排除指南。
