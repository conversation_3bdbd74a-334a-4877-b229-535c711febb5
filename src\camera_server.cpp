#include "app_config.h"

// 流相关常量
static const char* _STREAM_CONTENT_TYPE = "multipart/x-mixed-replace;boundary=" PART_BOUNDARY;
static const char* _STREAM_BOUNDARY = "\r\n--" PART_BOUNDARY "\r\n";
static const char* _STREAM_PART = "Content-Type: image/jpeg\r\nContent-Length: %u\r\n\r\n";

// HTML页面内容在app_config.h中已声明

// 处理图像流请求的函数
static esp_err_t stream_handler(httpd_req_t* req) {
  camera_fb_t* fb = NULL;
  esp_err_t res = ESP_OK;
  size_t _jpg_buf_len = 0;
  uint8_t* _jpg_buf = NULL;
  char* part_buf[64];

  // 设置MIME类型和跨域头
  res = httpd_resp_set_type(req, _STREAM_CONTENT_TYPE);
  if (res != ESP_OK) {
    Serial.println("设置内容类型失败");
    return res;
  }

  httpd_resp_set_hdr(req, "Access-Control-Allow-Origin", "*");
  httpd_resp_set_hdr(req, "X-Framerate", "30");

  Serial.println("开始处理视频流请求...");

  int64_t fr_start = esp_timer_get_time();
  int64_t fr_ready = 0;
  int64_t fr_encode = 0;
  const int64_t frame_interval = 16666;  // 约60fps (1秒 = 1000000微秒, 1000000/60 ≈ 16666) - 提高帧率

  // 帧率统计变量
  int frame_count = 0;
  int64_t last_fps_update = esp_timer_get_time();

  while (true) {
    // 获取相机帧
    fb = esp_camera_fb_get();
    if (!fb) {
      Serial.println("获取相机帧缓冲区失败");
      res = ESP_FAIL;
    } else {
      fr_ready = esp_timer_get_time();

      // 将图像转换为JPEG格式（如果不是的话）
      if (fb->format != PIXFORMAT_JPEG) {
        bool jpeg_converted = frame2jpg(fb, 10, &_jpg_buf, &_jpg_buf_len);
        esp_camera_fb_return(fb);
        fb = NULL;
        if (!jpeg_converted) {
          Serial.println("JPEG压缩失败");
          res = ESP_FAIL;
        }
      } else {
        _jpg_buf_len = fb->len;
        _jpg_buf = fb->buf;
      }
    }

    // 发送多部分响应头
    if (res == ESP_OK) {
      size_t hlen = snprintf((char*)part_buf, 64, _STREAM_PART, _jpg_buf_len);
      res = httpd_resp_send_chunk(req, _STREAM_BOUNDARY, strlen(_STREAM_BOUNDARY));
      if (res == ESP_OK) {
        res = httpd_resp_send_chunk(req, (const char*)part_buf, hlen);
      }
    }

    // 发送JPEG数据
    if (res == ESP_OK) {
      res = httpd_resp_send_chunk(req, (const char*)_jpg_buf, _jpg_buf_len);
      fr_encode = esp_timer_get_time();
    }

    // 清理资源
    if (fb) {
      esp_camera_fb_return(fb);
      fb = NULL;
      _jpg_buf = NULL;
    } else if (_jpg_buf) {
      free(_jpg_buf);
      _jpg_buf = NULL;
    }

    // 检查客户端是否断开连接
    if (res != ESP_OK) {
      Serial.printf("图像流发送失败: %d\n", res);
      break;
    }

    // 刷新ESP32的看门狗
    delay(1);

    // 更新起始时间
    fr_start = esp_timer_get_time();

    // 帧率控制 - 限制在约60fps以获得更流畅的视频
    int64_t frame_time = esp_timer_get_time() - fr_start;
    if (frame_time < frame_interval) {
      // 如果处理时间小于目标帧间隔，则等待剩余时间
      int32_t wait_time = frame_interval - frame_time;
      if (wait_time > 0) {
        delayMicroseconds(wait_time);
      }
    }

    // 帧率统计 - 每秒更新一次
    frame_count++;
    int64_t now = esp_timer_get_time();
    if (now - last_fps_update > 1000000) {  // 每秒更新一次
      float fps = frame_count * 1000000.0 / (now - last_fps_update);
      Serial.printf("摄像头帧率: %.2f fps\n", fps);
      frame_count = 0;
      last_fps_update = now;
    }
  }

  Serial.println("视频流结束");
  return res;
}

// 处理根路径请求
static esp_err_t index_handler(httpd_req_t* req) {
  Serial.println("\n=== 主页请求处理 ===");
  Serial.println("🏠 收到根路径请求 '/'");

  // 检查文件系统状态
  if (!LittleFS.begin()) {
    Serial.println("❌ 文件系统未挂载，使用备用HTML");
    httpd_resp_set_type(req, "text/html; charset=utf-8");
    return httpd_resp_send(req, (const char*)FALLBACK_INDEX_HTML, strlen(FALLBACK_INDEX_HTML));
  }

  // 尝试从文件系统读取index.html
  Serial.println("📖 尝试从文件系统读取 /index.html");
  String content = readFile("/index.html");

  if (content.length() > 0) {
    Serial.printf("✅ 成功读取文件系统中的 index.html (%d bytes)\n", content.length());
    Serial.println("📤 发送文件系统版本的HTML");
    httpd_resp_set_type(req, "text/html; charset=utf-8");
    return httpd_resp_send(req, content.c_str(), content.length());
  } else {
    Serial.println("❌ 无法读取文件系统中的 index.html");
    Serial.println("🔄 回退到备用HTML页面");
    Serial.printf("📤 发送备用HTML (%d bytes)\n", strlen(FALLBACK_INDEX_HTML));

    // 如果文件系统失败，使用备用HTML
    httpd_resp_set_type(req, "text/html; charset=utf-8");
    return httpd_resp_send(req, (const char*)FALLBACK_INDEX_HTML, strlen(FALLBACK_INDEX_HTML));
  }
}

// 启动HTTP服务器
void startCameraServer() {
  httpd_config_t config = HTTPD_DEFAULT_CONFIG();
  config.server_port = 80;
  config.core_id = 0;
  config.stack_size = 16384;     // 增加堆栈大小
  config.max_uri_handlers = 74;  // 支持所有模型文件和页面路由 (当前需要约69个)
  config.max_resp_headers = 16;
  config.recv_wait_timeout = 30;  // 增加超时时间
  config.send_wait_timeout = 30;  // 增加超时时间
  config.max_open_sockets = 13;   // 增加最大并发连接数
  config.backlog_conn = 5;        // 增加连接队列

  httpd_uri_t index_uri = {
    .uri = "/",
    .method = HTTP_GET,
    .handler = index_handler,
    .user_ctx = NULL
  };

  // 创建定义stream_uri (保留HTTP流端点作为备用)
  httpd_uri_t stream_uri = {
    .uri = "/stream",
    .method = HTTP_GET,
    .handler = stream_handler,
    .user_ctx = NULL
  };

  // 静态文件路由 - 使用具体路径而不是通配符
  httpd_uri_t css_uri = {
    .uri = "/css/style.css",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };



  httpd_uri_t js_uri = {
    .uri = "/js/app.js",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };

  // TensorFlow.js库文件路由
  httpd_uri_t tfjs_uri = {
    .uri = "/js/tfjs.js",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };

  // TensorFlow.js库文件路由（gzip版本）
  httpd_uri_t tfjs_gz_uri = {
    .uri = "/js/tfjs.js.gz",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };

  // KNN分类器库文件路由
  httpd_uri_t knn_uri = {
    .uri = "/js/knn-classifier.js",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };

  // 数据采集训练页面路由
  httpd_uri_t collecttrain_uri = {
    .uri = "/collecttrain.html",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };



  // 数据采集训练JS文件路由
  httpd_uri_t collecttrain_js_uri = {
    .uri = "/js/collecttrain.js",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };

  // jQuery库文件路由（未压缩版本，服务器会自动选择gzip版本如果存在）
  httpd_uri_t jquery_uri = {
    .uri = "/js/jquery-3.6.0.min.js",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };

  // JSZip库文件路由（未压缩版本，服务器会自动选择gzip版本如果存在）
  httpd_uri_t jszip_uri = {
    .uri = "/js/jszip.min.js",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };

  // FileSaver库文件路由（未压缩版本，服务器会自动选择gzip版本如果存在）
  httpd_uri_t filesaver_uri = {
    .uri = "/js/file-saver.js",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };

  // TensorFlow.js可视化库文件路由（未压缩版本，服务器会自动选择gzip版本如果存在）
  httpd_uri_t tfjs_vis_uri = {
    .uri = "/js/tfjs-vis.js",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };

  // 模型文件路由 - 使用通用处理器处理所有模型文件
  httpd_uri_t model_json_uri = {
    .uri = "/models/mobilenet/model.json",
    .method = HTTP_GET,
    .handler = static_file_handler,
    .user_ctx = NULL
  };

  // 为所有模型分片文件创建路由数组
  static httpd_uri_t model_shard_uris[55];
  static char uri_paths[55][64];  // 静态数组存储URI路径，移到循环外
  
  // 初始化所有模型分片文件的路由
  for (int i = 1; i <= 55; i++) {
    snprintf(uri_paths[i-1], sizeof(uri_paths[i-1]), "/models/mobilenet/group%d-shard1of1", i);
    
    model_shard_uris[i-1].uri = uri_paths[i-1];
    model_shard_uris[i-1].method = HTTP_GET;
    model_shard_uris[i-1].handler = static_file_handler;
    model_shard_uris[i-1].user_ctx = NULL;
  }

  Serial.printf("启动Web服务器在端口: '%d'\n", config.server_port);
  if (httpd_start(&camera_httpd, &config) == ESP_OK) {
    httpd_register_uri_handler(camera_httpd, &index_uri);
    httpd_register_uri_handler(camera_httpd, &stream_uri);  // 在主服务器上注册流处理程序
    httpd_register_uri_handler(camera_httpd, &css_uri);     // 注册CSS文件处理程序
    httpd_register_uri_handler(camera_httpd, &js_uri);      // 注册JS文件处理程序
    httpd_register_uri_handler(camera_httpd, &tfjs_uri);    // 注册TensorFlow.js库文件处理程序
    httpd_register_uri_handler(camera_httpd, &tfjs_gz_uri); // 注册TensorFlow.js库文件处理程序（gzip版本）
    httpd_register_uri_handler(camera_httpd, &knn_uri);     // 注册KNN分类器库文件处理程序

    // 注册数据采集训练相关路由
    httpd_register_uri_handler(camera_httpd, &collecttrain_uri);    // 数据采集训练页面
    httpd_register_uri_handler(camera_httpd, &collecttrain_js_uri); // 数据采集训练JS文件
    httpd_register_uri_handler(camera_httpd, &jquery_uri);          // jQuery库文件
    httpd_register_uri_handler(camera_httpd, &jszip_uri);           // JSZip库文件
    httpd_register_uri_handler(camera_httpd, &filesaver_uri);       // FileSaver库文件
    httpd_register_uri_handler(camera_httpd, &tfjs_vis_uri);        // TensorFlow.js可视化库文件
    httpd_register_uri_handler(camera_httpd, &model_json_uri); // 注册模型JSON文件处理程序
    
    // 注册所有模型分片文件处理程序
    Serial.println("开始注册模型分片文件路由...");
    for (int i = 0; i < 55; i++) {
      esp_err_t result = httpd_register_uri_handler(camera_httpd, &model_shard_uris[i]);
      if (result != ESP_OK) {
        Serial.printf("❌ 路由注册失败: group%d-shard1of1 (错误: %d)\n", i+1, result);
      } else if (i >= 48) {  // 只显示最后几个的成功信息
        Serial.printf("✅ 路由注册成功: group%d-shard1of1\n", i+1);
      }
    }
    Serial.println("模型分片文件路由注册完成");
    
    // 注册404错误处理器
    httpd_register_err_handler(camera_httpd, HTTPD_404_NOT_FOUND, error_404_handler);
    
    Serial.println("主Web服务器启动成功");
    Serial.println("静态文件服务已启用 (CSS, JS)");
    Serial.println("CSS文件路由已注册:");
    Serial.println("  - /css/style.css");
    Serial.println("数据采集训练页面路由已注册:");
    Serial.println("  - /collecttrain.html");
    Serial.println("  - /js/collecttrain.js");
    Serial.println("  - /js/jquery-3.6.0.min.js (自动选择.gz版本)");
    Serial.println("  - /js/jszip.min.js (自动选择.gz版本)");
    Serial.println("  - /js/file-saver.js (自动选择.gz版本)");
    Serial.println("  - /js/tfjs-vis.js (自动选择.gz版本)");
    Serial.println("通用静态文件处理器已注册");
    stream_httpd = camera_httpd;  // 使两个句柄指向同一个服务器
  } else {
    Serial.println("主Web服务器启动失败");
    return;
  }

  // 启动WebSocket服务器
  wsServer.listen(81);  // 在端口81上启动WebSocket服务器
  Serial.println("WebSocket服务器启动在端口: 81");
  Serial.print("WebSocket服务器是否可用: ");
  Serial.println(wsServer.available() ? "是" : "否");

  Serial.println("摄像头服务器初始化完成!");
} 