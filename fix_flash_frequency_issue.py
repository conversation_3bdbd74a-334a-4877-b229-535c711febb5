#!/usr/bin/env python3
"""
ESP32-S3-EYE Flash 频率问题修复脚本
基于 GitHub issue #10989 的解决方案
"""

import subprocess
import time
import sys
import os

def run_command(command, description, timeout=120):
    """执行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🔧 {description}")
    print(f"📝 命令: {command}")
    print('='*60)
    
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            cwd=os.getcwd()
        )
        
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
                output_lines.append(output.strip())
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"\n✅ {description} - 成功")
            return True
        else:
            print(f"\n❌ {description} - 失败 (返回码: {return_code})")
            return False
            
    except Exception as e:
        print(f"\n💥 {description} - 异常: {e}")
        return False

def check_platformio_config():
    """检查 platformio.ini 配置"""
    print("🔍 检查 platformio.ini 配置...")
    
    if not os.path.exists("platformio.ini"):
        print("❌ 未找到 platformio.ini 文件")
        return False
    
    with open("platformio.ini", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查关键配置
    checks = {
        "Flash 频率 (40MHz)": "board_build.f_flash = 40000000L" in content,
        "Flash 模式 (QIO)": "board_build.flash_mode = qio" in content,
        "上传速度 (115200)": "upload_speed = 115200" in content,
        "PSRAM 类型": "board_build.psram_type = opi" in content,
        "ESP32S3_EYE 模型": "CAMERA_MODEL_ESP32S3_EYE" in content
    }
    
    print("📋 配置检查结果:")
    all_good = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def main():
    """主修复流程"""
    print("🚀 ESP32-S3-EYE Flash 频率问题修复工具")
    print("基于 GitHub issue #10989 的解决方案")
    print("=" * 60)
    
    # 检查配置
    if not check_platformio_config():
        print("\n⚠️  配置检查发现问题，但继续执行修复...")
    else:
        print("\n✅ 配置检查通过")
    
    print("\n🎯 开始修复流程...")
    print("💡 修复要点:")
    print("  1. 使用正确的 Flash 频率 (40MHz)")
    print("  2. 降低上传速度 (115200)")
    print("  3. 使用稳定的连接参数")
    
    # 修复步骤 - 使用更保守的方法
    steps = [
        ("pio run -e esp32s3_eye -t erase", "步骤 1: 完全擦除 Flash"),
        ("pio run -e esp32s3_eye -t clean", "步骤 2: 清理构建缓存"),
        ("pio run -e esp32s3_eye", "步骤 3: 重新编译 (使用正确的 Flash 频率)"),
    ]
    
    # 执行前三个步骤
    for command, description in steps:
        if not run_command(command, description):
            print(f"\n💥 修复失败于: {description}")
            return False
        
        # 在关键步骤之间等待
        time.sleep(2)
    
    # 特别处理上传步骤
    print("\n🎯 步骤 4: 上传固件 (使用低速稳定连接)")
    print("💡 使用 115200 波特率避免数据损坏")
    
    if not run_command("pio run -e esp32s3_eye -t upload", "上传固件"):
        print("\n❌ 上传失败，尝试替代方案...")
        
        # 尝试手动 esptool 上传
        print("🔄 尝试使用 esptool 手动上传...")
        manual_upload_cmd = (
            "esptool.py --chip esp32s3 --port COM11 --baud 115200 "
            "--before default_reset --after hard_reset write_flash "
            "--flash_mode qio --flash_freq 40m --flash_size 16MB "
            "0x0 .pio/build/esp32s3_eye/bootloader.bin "
            "0x8000 .pio/build/esp32s3_eye/partitions.bin "
            "0x10000 .pio/build/esp32s3_eye/firmware.bin"
        )
        
        if not run_command(manual_upload_cmd, "手动上传固件"):
            print("\n💥 所有上传方法都失败了")
            print("🛠️  建议:")
            print("  1. 检查 USB 连接质量")
            print("  2. 尝试不同的 USB 端口")
            print("  3. 检查 USB 线质量")
            print("  4. 按住 BOOT 按钮再尝试上传")
            return False
    
    print("\n⏳ 等待设备重启...")
    time.sleep(5)
    
    # 上传文件系统
    print("\n🔄 步骤 5: 上传文件系统")
    if run_command("pio run -e esp32s3_eye -t uploadfs", "上传文件系统"):
        print("\n🎉 修复完成!")
    else:
        print("\n⚠️  文件系统上传失败，但固件应该已经正常工作")
        print("💡 设备将使用备用 HTML 页面")
    
    # 启动监控
    print("\n📺 启动串口监控验证修复结果...")
    print("💡 查看是否出现文件系统相关错误")
    print("💡 按 Ctrl+C 退出监控")
    time.sleep(2)
    
    try:
        subprocess.run("pio device monitor -e esp32s3_eye", shell=True)
    except KeyboardInterrupt:
        print("\n👋 监控已停止")
    
    return True

def show_troubleshooting():
    """显示故障排除信息"""
    print("""
🛠️  ESP32-S3-EYE Flash 频率问题故障排除

常见问题和解决方案:

1. 文件系统损坏 (Corrupted dir pair)
   原因: Flash 频率配置错误 (8MHz vs 40MHz)
   解决: 使用正确的 40MHz 频率配置

2. 上传失败
   原因: USB 连接不稳定或波特率过高
   解决: 降低波特率到 115200，检查 USB 连接

3. CH340 串口芯片问题
   原因: CH340 芯片在高波特率下不稳定
   解决: 使用较低的波特率，确保稳定连接

4. 配置参数:
   - Flash 频率: 40MHz (不是 8MHz 或 80MHz)
   - 上传速度: 115200 (不是 921600)
   - Flash 模式: QIO
   - PSRAM 类型: OPI

5. 如果问题持续:
   - 尝试不同的 USB 端口
   - 使用高质量的 USB 线
   - 按住 BOOT 按钮进行上传
   - 检查电源稳定性

参考资料:
- GitHub Issue: https://github.com/espressif/arduino-esp32/issues/10989
- ESP32-S3 技术参考手册
""")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help", "troubleshoot"]:
        show_troubleshooting()
    else:
        try:
            success = main()
            if success:
                print("\n✅ Flash 频率问题修复完成!")
                print("🔍 请检查串口输出确认文件系统正常工作")
                print("📖 如果仍有问题，运行: python fix_flash_frequency_issue.py troubleshoot")
            else:
                print("\n❌ 修复失败")
                print("📖 运行故障排除: python fix_flash_frequency_issue.py troubleshoot")
            
            sys.exit(0 if success else 1)
            
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            sys.exit(1)
        except Exception as e:
            print(f"\n💥 意外错误: {e}")
            sys.exit(1)
