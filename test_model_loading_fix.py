#!/usr/bin/env python3
"""
测试模型加载修复效果
验证并发控制和延迟机制是否正确实现
"""

import os
import re

def test_collecttrain_js_fixes():
    """测试 collecttrain.js 的修复"""
    print("🔍 检查 collecttrain.js 的并发控制修复...")
    
    if not os.path.exists("data/js/collecttrain.js"):
        print("❌ 未找到 data/js/collecttrain.js")
        return False
    
    with open("data/js/collecttrain.js", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查修复内容
    checks = {
        "请求延迟参数": "requestDelay = 50" in content,
        "单并发设置": "maxConcurrent = 1" in content,
        "延迟执行": "setTimeout(() => {" in content,
        "请求日志": "发起请求:" in content,
        "队列日志": "请求排队:" in content,
        "优化的并发调用": "createLimitedFetch(1, 100)" in content,
        "ESP32优化注释": "针对ESP32优化" in content
    }
    
    print("📋 并发控制修复检查:")
    all_good = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def test_server_config():
    """测试服务器配置"""
    print("\n🔍 检查服务器配置优化...")
    
    if not os.path.exists("src/camera_server.cpp"):
        print("❌ 未找到 src/camera_server.cpp")
        return False
    
    with open("src/camera_server.cpp", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查服务器配置
    checks = {
        "最大连接数": "max_open_sockets = 13" in content,
        "连接队列": "backlog_conn = 5" in content,
        "接收超时": "recv_wait_timeout = 30" in content,
        "发送超时": "send_wait_timeout = 30" in content,
        "URI处理器数量": "max_uri_handlers = 64" in content,
        "堆栈大小": "stack_size = 16384" in content
    }
    
    print("📋 服务器配置检查:")
    all_good = True
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_good = False
    
    return all_good

def analyze_fix_strategy():
    """分析修复策略"""
    print("\n🎯 修复策略分析:")
    
    print("\n1️⃣ 并发控制优化:")
    print("   - 从3个并发减少到1个并发")
    print("   - 添加100ms请求延迟")
    print("   - 添加详细的请求日志")
    print("   - 实现请求队列机制")
    
    print("\n2️⃣ 服务器配置优化:")
    print("   - max_open_sockets: 13 (足够处理模型文件)")
    print("   - backlog_conn: 5 (连接队列)")
    print("   - 超时时间: 30秒 (足够长)")
    print("   - URI处理器: 64个 (支持所有路由)")
    
    print("\n3️⃣ 预期效果:")
    print("   - 减少服务器负载")
    print("   - 避免并发请求冲突")
    print("   - 提高请求成功率")
    print("   - 更好的错误处理")

def generate_testing_guide():
    """生成测试指南"""
    print("\n📝 测试指南:")
    
    print("\n🚀 部署步骤:")
    print("1. 编译并上传固件:")
    print("   pio run -e seeed_xiao_esp32s3_sense -t upload")
    print("2. 重新上传文件系统 (确保所有文件完整):")
    print("   pio run -e seeed_xiao_esp32s3_sense -t uploadfs")
    print("3. 重启设备")
    
    print("\n🔍 测试步骤:")
    print("1. 访问: http://[设备IP]/collecttrain.html")
    print("2. 打开浏览器开发者工具 (F12)")
    print("3. 查看 Console 标签页")
    print("4. 观察模型加载过程")
    
    print("\n✅ 成功标志:")
    print("- 看到 '🔄 发起请求:' 日志")
    print("- 看到 '✅ 请求完成:' 日志")
    print("- 所有 group1-55 文件都返回 200 状态")
    print("- 最终显示 'MobileNet 预训练模型加载完成'")
    
    print("\n❌ 失败标志:")
    print("- 看到 '❌ 请求失败:' 日志")
    print("- 任何文件返回 404 状态")
    print("- 显示 '模型加载失败'")
    
    print("\n🛠️ 进一步优化 (如果仍有问题):")
    print("1. 进一步减少并发:")
    print("   createLimitedFetch(1, 200)  // 增加到200ms延迟")
    print("2. 添加重试机制:")
    print("   在 fetch 失败时自动重试")
    print("3. 分批加载:")
    print("   将55个文件分成多批次加载")

def check_file_system_integrity():
    """检查文件系统完整性"""
    print("\n🔍 检查文件系统完整性...")
    
    model_dir = "data/models/mobilenet"
    if not os.path.exists(model_dir):
        print(f"❌ 模型目录不存在: {model_dir}")
        return False
    
    # 检查关键文件
    critical_files = [
        "model.json",
        "group1-shard1of1",
        "group50-shard1of1",
        "group51-shard1of1",
        "group52-shard1of1",
        "group53-shard1of1",
        "group54-shard1of1",
        "group55-shard1of1"
    ]
    
    print("📋 关键文件检查:")
    all_exist = True
    for file_name in critical_files:
        file_path = os.path.join(model_dir, file_name)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"  ✅ {file_name} ({file_size} bytes)")
        else:
            print(f"  ❌ {file_name} - 缺失")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("🚀 模型加载修复效果测试")
    print("=" * 50)
    
    # 检查修复实现
    js_fixes_ok = test_collecttrain_js_fixes()
    
    # 检查服务器配置
    server_ok = test_server_config()
    
    # 检查文件完整性
    files_ok = check_file_system_integrity()
    
    # 分析修复策略
    analyze_fix_strategy()
    
    # 生成测试指南
    generate_testing_guide()
    
    print(f"\n{'='*50}")
    print("修复状态总结:")
    print(f"  JavaScript 修复: {'✅ 完成' if js_fixes_ok else '❌ 需要修复'}")
    print(f"  服务器配置: {'✅ 优化' if server_ok else '❌ 需要优化'}")
    print(f"  文件完整性: {'✅ 正常' if files_ok else '❌ 有问题'}")
    
    if js_fixes_ok and server_ok and files_ok:
        print("\n🎉 修复完成！现在可以测试模型加载了")
        print("💡 关键改进:")
        print("   - 单并发请求 (避免服务器过载)")
        print("   - 100ms 请求延迟 (给服务器喘息时间)")
        print("   - 详细日志 (便于调试)")
        print("   - 请求队列 (有序处理)")
    else:
        print("\n⚠️  请先解决上述问题再进行测试")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"💥 测试过程出错: {e}")
        exit(1)
