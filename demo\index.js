// 引入TensorFlow.js和KNN分类器作为ES模块
import * as tf from '@tensorflow/tfjs';
import * as knnClassifier from '@tensorflow-models/knn-classifier';

 // IndexedDB相关代码
 let db;
 const DB_NAME = 'knnClassifierDB';
 const STORE_NAME = 'knnDatasets';

 // 初始化IndexedDB
 function initDB() {
     return new Promise((resolve, reject) => {
         const request = indexedDB.open(DB_NAME, 1);

         request.onerror = (event) => {
             console.error("缓存KNN数据打开失败:", event);
             document.getElementById('status_info').innerHTML = '缓存KNN数据打开失败，将使用备选数据';
             resolve(false);
         };

         request.onsuccess = (event) => {
             db = event.target.result;
             console.log("缓存KNN数据打开成功");
             resolve(true);
         };

         request.onupgradeneeded = (event) => {
             const db = event.target.result;
             if (!db.objectStoreNames.contains(STORE_NAME)) {
                 db.createObjectStore(STORE_NAME, { keyPath: 'name' });
                 console.log("创建数据存储");
             }
         };
     });
 }

 // 保存数据到IndexedDB
 function saveToIndexedDB(name, data) {
     return new Promise((resolve, reject) => {
         if (!db) {
             console.warn("缓存KNN数据未初始化，使用localStorage");
             localStorage.setItem(name, data);
             resolve();
             return;
         }

         const transaction = db.transaction([STORE_NAME], "readwrite");
         const store = transaction.objectStore(STORE_NAME);

         const record = {
             name: name,
             data: data,
             timestamp: new Date().getTime()
         };

         const request = store.put(record);

         request.onsuccess = () => {
             console.log("KNN数据保存到缓存成功");
             resolve();
         };

         request.onerror = (event) => {
             console.error("保存KNN数据到缓存失败:", event);
             // 备选方案：保存到localStorage
             localStorage.setItem(name, data);
             resolve();
         };
     });
 }

 // 从IndexedDB加载数据
 function loadFromIndexedDB(name) {
     return new Promise((resolve, reject) => {
         if (!db) {
             console.warn("KNN缓存数据未初始化，使用localStorage");
             const data = localStorage.getItem(name);
             resolve(data);
             return;
         }

         const transaction = db.transaction([STORE_NAME], "readonly");
         const store = transaction.objectStore(STORE_NAME);
         const request = store.get(name);

         request.onsuccess = (event) => {
             if (request.result) {
                 console.log("从缓存加载KNN数据成功");
                 resolve(request.result.data);
             } else {
                 console.log("缓存中未找到数据，尝试从localStorage加载");
                 const data = localStorage.getItem(name);
                 resolve(data);
             }
         };

         request.onerror = (event) => {
             console.error("从缓存加载KNN数据失败:", event);
             // 备选方案：从localStorage加载
             const data = localStorage.getItem(name);
             resolve(data);
         };
     });
 }

 if (window.FileList && window.File && window.FileReader) {
     document.getElementById('file-selector').addEventListener('change', event => {
         const file = event.target.files[0];
         const reader = new FileReader();
         reader.addEventListener('load', event => {
             savetoLocalstorage(event.target.result);
         });
         reader.readAsText(file);
     });
 }

 async function savetoLocalstorage(str) {
     await saveToIndexedDB(fileName, str);
     document.getElementById('status_info').innerHTML = '数据已上传！';
 }

 function jsonFileNameChanged() {
     fileName = document.getElementById('jsonFileName').value;
     console.log(fileName);
 }

 let net; // 模型引用
 let fileName = document.getElementById('jsonFileName').value;
 const webcamElement = document.getElementById('webcam');
 const classifier = knnClassifier.create();
 
 // 使用本地的FeatureVector模型
 // MobileNet V1 025 224 特征向量模型(直接输出特征向量)
 const featureVectorModel = './model.json'; // 本地模型路径
 
 // 帧率计算相关变量
 let frameCount = 0;
 let lastTime = performance.now();
 let fps = 0;
 const fpsElement = document.getElementById('fps');
 const memoryElement = document.getElementById('memory-info');

 // 格式化内存大小
 function formatBytes(bytes, decimals = 2) {
     if (bytes === 0) return '0 B';
     const k = 1024;
     const dm = decimals < 0 ? 0 : decimals;
     const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
     const i = Math.floor(Math.log(bytes) / Math.log(k));
     return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
 }

 // K值设置
 let kValue = 3; // 默认K值为3
 // 用于暂停预测的标志
 window.pausePrediction = false;

 function kValueChanged() {
     const kInput = document.getElementById('k-value');
     let newK = parseInt(kInput.value);

     // 验证K值范围
     if (isNaN(newK) || newK < 1) {
         newK = 1;
         kInput.value = 1;
     } else if (newK > 9) {
         newK = 9;
         kInput.value = 9;
     }

     kValue = newK;
     // 更新显示的K值
     document.getElementById('k-value-display').innerText = kValue;
     document.getElementById('status_info').innerHTML = `K值已设置为: ${kValue}`;
     console.log(`K值已更改为: ${kValue}`);
 }

 async function app() {
     // 初始化IndexedDB
     await initDB();

     // 步骤1: 加载特征向量模型
     document.getElementById('status_info').innerHTML = 'MobileNet_V1_025_224特征向量模型载入中';

     try {
         // 使用tf.loadGraphModel加载本地特征向量模型
         // 与mobilenet.load不同，这里直接使用TensorFlow.js的加载API
         net = await tf.loadGraphModel(featureVectorModel);
         document.getElementById('status_info').innerHTML = 'MobileNet_V1_025_224特征向量模型已载入';
         console.log('成功加载MobileNet V1 025 224特征向量模型');
     } catch (error) {
         console.error('加载特征向量模型失败:', error);
         document.getElementById('status_info').innerHTML = '特征向量模型加载失败: ' + error.message;
         return;
     }

     // 步骤2: 初始化网络摄像头
     const camconfig = { facingMode: 'environment' }
     const webcam = await tf.data.webcam(webcamElement, camconfig);

     // 步骤3: 定义添加样本的函数
     const addExample = async classId => {
         // 捕获图像
         const img = await webcam.capture();

         // 图像预处理:
         // 1. 调整大小到224x224(MobileNet的标准输入尺寸)
         // 2. 将像素值从[0,255]范围归一化到[-1,1]范围
         // 3. 扩展维度以匹配批处理输入要求
         const processedImg = tf.tidy(() => {
             return tf.expandDims(
                 tf.image.resizeBilinear(img, [224, 224])
                    .div(127.5).sub(1), // 对于MobileNet v1: -1到1的范围
                 0
             );
         });

         // 特征提取:
         // 使用特征向量模型直接输出特征表示
         const activation = net.predict(processedImg);

         // 添加样本到KNN分类器
         classifier.addExample(activation, classId);

         // 更新样本数量显示
         updateKnnDataInfo();

         // 释放内存
         img.dispose();
         processedImg.dispose();
         activation.dispose();
     };

     // 步骤4: 设置UI事件监听器
     document.getElementById('class-a').addEventListener('click', () => addExample('go'));
     document.getElementById('class-b').addEventListener('click', () => addExample('left'));
     document.getElementById('class-c').addEventListener('click', () => addExample('right'));
     document.getElementById('class-d').addEventListener('click', () => addExample('stop'));

     // 步骤5: 主预测循环
     while (true) {
         const startTime = performance.now();

         // 创建内存作用域以自动管理张量
         tf.engine().startScope();
         
         // 仅在不暂停且有类别数据时进行预测
         if (!window.pausePrediction && classifier.getNumClasses() > 0) {
             try {
                 // 捕获并预处理图像
                 const img = await webcam.capture();
                 const processedImg = tf.tidy(() => {
                     return tf.expandDims(
                         tf.image.resizeBilinear(img, [224, 224])
                            .div(127.5).sub(1),
                         0
                     );
                 });

                 // 特征提取
                 const activation = net.predict(processedImg);
                 
                 // KNN分类预测
                 const result = await classifier.predictClass(activation, kValue);

                 // 显示预测结果
                 document.getElementById('console').innerText = `推理结果:${result.label}:${(result.confidences[result.label]).toFixed(2)} (K=${kValue})`;
                 
                 // 如果蓝牙已连接，发送预测结果
                 if (bleSerial.isConnected()) {
                     try {
                         // 使用全局sendCommand函数发送预测结果
                         await sendCommand(result.label);

                         // 处理置信度数据
                         let out2 = result.confidences;
                         Object.keys(out2).forEach(function (key, index) {
                             out2[key] = parseFloat((out2[key]).toFixed(2));
                         });

                         let out = JSON.stringify(out2);
                         out = out.replaceAll('"', '');
                     } catch (error) {
                         console.error('发送KNN结果错误:', error);
                     }
                 }

                 // 释放内存
                 img.dispose();
                 processedImg.dispose();
                 activation.dispose();
             } catch (error) {
                 console.error('预测过程中出错:', error);
                 await sleep(500);
             }
         } else if (window.pausePrediction) {
             document.getElementById('console').innerText = '预测已暂停 (正在处理数据...)';
         } else if (classifier.getNumClasses() === 0) {
             document.getElementById('console').innerText = '请添加样本数据或载入KNN数据';
         }

         // 计算和显示帧率与内存使用情况
         frameCount++;
         const now = performance.now();
         const elapsed = now - lastTime;

         if (elapsed >= 1000) {
             fps = Math.round((frameCount * 1000) / elapsed);
             frameCount = 0;
             lastTime = now;
             fpsElement.innerText = `帧率: ${fps} FPS`;

             // 更新内存使用情况
             const memoryInfo = tf.memory();
             updateMemoryInfo(memoryInfo);
         }

         // 等待下一帧并结束内存作用域
         await tf.nextFrame();
         tf.engine().endScope();
     }
 }

 app();

 // 保存KNN分类器数据到文件和IndexedDB
 // 注意：此函数适用于任何特征提取模型，包括MobileNet和本地特征向量模型
 async function save() {
     let str = JSON.stringify(Object.entries(classifier.getClassifierDataset()).map(([label, data]) => [label, Array.from(data.dataSync()), data.shape]));
     await saveToIndexedDB(fileName, str);
     document.getElementById('status_info').innerHTML = 'KNN数据已保存到缓存';
     download(str, fileName + '.txt', 'application/octet-stream');
 }

 // 从IndexedDB或文件加载KNN分类器数据
 // 注意：此函数适用于任何特征提取模型，包括MobileNet和本地特征向量模型
 async function load() {
     try {
         document.getElementById('status_info').innerHTML = '<span style="color:#ff6600">正在加载KNN数据...</span>';
         
         // 获取数据集
         let dataset = await loadFromIndexedDB(fileName);
         if (!dataset) {
             document.getElementById('status_info').innerHTML = '未找到缓存的KNN数据';
             return;
         }
         
         // 暂停主循环中的预测
         window.pausePrediction = true;
         
         // 等待一小段时间，确保当前的处理完成
         await sleep(300);
         
         // 清理当前分类器，避免潜在的内存冲突
         try {
             // 在加载新数据前清理当前模型的内存
             if (classifier && typeof classifier.clearAllClasses === 'function') {
                 classifier.clearAllClasses();
                 console.log('分类器已清理');
             }
             
             // 手动进行张量垃圾回收
             tf.engine().startScope(); // 开始一个新的内存作用域
             
             // 解析数据集
             const parsedDataset = JSON.parse(dataset);
             console.log(`加载数据集: 包含 ${parsedDataset.length} 个分类`);
             
             // 准备新的数据集对象
             const tensorMap = {};
             
             // 为每个标签创建新的张量
             for (const [label, data, shape] of parsedDataset) {
                 if (!data || !shape) {
                     console.warn(`标签 "${label}" 的数据不完整，跳过`);
                     continue;
                 }
                 
                 // 创建持久化的张量
                 const tensor = tf.tensor(data, shape);
                 // 将张量保持在内存中（防止被垃圾回收）
                 tf.keep(tensor);
                 tensorMap[label] = tensor;
                 console.log(`为标签 "${label}" 创建了形状为 [${shape}] 的张量`);
             }
             
             // 检查是否成功创建了至少一个张量
             if (Object.keys(tensorMap).length === 0) {
                 throw new Error("没有创建任何有效的张量");
             }
             
             // 设置分类器数据集（使用新创建的张量）
             classifier.setClassifierDataset(tensorMap);
             
             // 结束内存作用域
             tf.engine().endScope();
             
             document.getElementById('status_info').innerHTML = '<span style="color:#33cc33">KNN数据已成功载入! 共 ' + Object.keys(tensorMap).length + ' 个分类</span>';
             console.log('KNN数据已成功载入，分类器已更新');

             // 更新样本数量显示
             updateKnnDataInfo();
         } catch (innerError) {
             console.error('处理KNN数据时出错:', innerError);
             document.getElementById('status_info').innerHTML = '<span style="color:#ff0000">处理KNN数据时出错: ' + innerError.message + '</span>';
         } finally {
             // 恢复主循环中的预测
             window.pausePrediction = false;
         }
     } catch (error) {
         console.error('载入KNN数据时出错:', error);
         document.getElementById('status_info').innerHTML = '<span style="color:#ff0000">载入KNN数据时出错: ' + error.message + '</span>';
         window.pausePrediction = false; // 确保预测循环能够恢复
     }
 }

 function download(content, fileName, contentType) {
     var a = document.createElement("a");
     var file = new Blob([content], { type: 'application/octet-stream' });
     a.href = URL.createObjectURL(file);
     a.download = fileName;
     a.click();
 }

 ////////////////////////////////////////////////////
 // Support for Web Bluetooth

 // 常量定义
 const MICROBLOCKS_SERVICE_UUID = 'bb37a001-b922-4018-8e74-e14824b3a638';
 const MICROBLOCKS_RX_CHAR_UUID = 'bb37a002-b922-4018-8e74-e14824b3a638'; // board receive characteristic
 const MICROBLOCKS_TX_CHAR_UUID = 'bb37a003-b922-4018-8e74-e14824b3a638'; // board transmit characteristic

 // 兼容ESP32的其他可能的蓝牙UUID
 const ESP32_SERVICE_UUIDS = [
     MICROBLOCKS_SERVICE_UUID
 ];

 // 蓝牙通信参数
 const BLE_PACKET_LEN = 240; // Max BLE attribute length is 512 but 240 gives best performance
 const BLE_RECONNECT_DELAY = 1000; // 重连等待时间
 const BLE_CONNECTION_TIMEOUT = 10000; // 连接超时时间
 const BLE_MAX_RETRIES = 3; // 最大重试次数
 const DISCONNECT_DELAY = 500; // 断开连接前的等待时间

 // 全局变量
 const GP_serialInputBuffers = [];
 let deviceCache = null; // 缓存已连接的设备
 let isDisconnecting = false; // 正在断开连接的标志
 let deviceIsConnected = false;
 let sendCommandsEnabled = true; // 是否发送命令到小车的标志

 function stringToUint8Array(str) {
     const encoder = new TextEncoder();
     return encoder.encode(str);
 }

 function bleSerialBroadcastCmd(str) {
     let length = str.length + 1;
     return new Uint8Array([251, 27, 0, length % 256, Math.floor(length / 256), ...stringToUint8Array(str), 254]);
 }

 // 安全地执行函数，捕获并记录错误但不抛出
 function safeExecute(fn, ...args) {
     try {
         return fn(...args);
     } catch (error) {
         console.log(`执行函数 ${fn.name || '匿名函数'} 时出错:`, error);
         return null;
     }
 }

 // 清除缓存的设备信息
 function clearDeviceCache() {
     deviceCache = null;
 }

 // 等待指定的毫秒数
 function sleep(ms) {
     return new Promise(resolve => setTimeout(resolve, ms));
 }

 class NimBLESerial {
     constructor() {
         this.device = null;
         this.server = null;
         this.service = null;
         this.rx_char = null;
         this.tx_char = null;
         this.connected = false;
         this.sendInProgress = false;
         this.disconnectRequested = false;
         this.reconnectAttempts = 0;
         this.notificationsActive = false;

         // 绑定this上下文
         this.handle_disconnected = this.handle_disconnected.bind(this);
         this.handle_read = this.handle_read.bind(this);
         this.connect = this.connect.bind(this);
         this.reconnect = this.reconnect.bind(this);
     }

     handle_disconnected(event) {
         console.log("BLE断开连接事件触发");

         // 如果已经在处理断开连接，无需重复处理
         if (isDisconnecting) {
             console.log("断开连接过程已在进行中，跳过重复处理");
             return;
         }

         this.cleanupConnection();

         // 更新UI
         this.updateUI('蓝牙设备已断开连接');

         console.log("已处理断开连接事件，UI已更新");

         // 如果不是用户手动断开，尝试自动重连
         if (!this.disconnectRequested && this.device) {
             console.log("非用户请求断开，将尝试重连");
             // 设备存在但断开了，尝试重连
             setTimeout(() => this.reconnect(), BLE_RECONNECT_DELAY);
         }
     }

     // 清理连接资源的通用方法
     cleanupConnection(clearDevice = true) {
         // 清理通知
         if (this.tx_char && this.notificationsActive) {
             try {
                 // 我们不等待这个操作完成，因为可能已经断开连接
                 safeExecute(() => {
                     this.tx_char.removeEventListener('characteristicvaluechanged', this.handle_read);
                 });
             } catch (e) {
                 console.log('清理通知监听器失败，但这是预期的:', e);
             }
         }

         // 清理状态
         this.server = null;
         this.service = null;
         this.rx_char = null;
         this.tx_char = null;
         this.connected = false;
         this.sendInProgress = false;
         this.notificationsActive = false;
         deviceIsConnected = false;

         // 是否需要清除设备引用
         if (clearDevice) {
             this.device = null;
         }
     }

     handle_read(event) {
         let data = new Uint8Array(event.target.value.buffer);
         GP_serialInputBuffers.push(data);

         // 显示接收到的数据（用于调试）
         // console.log(event.target.value);
         const value = new TextDecoder().decode(event.target.value);
         const lettersOnly = value.replace(/[^A-Za-z]/g, '');
         // console.log(`收到数据: ${lettersOnly}`);
     }

     // 集中更新UI状态
     updateUI(message, enableConnect = true, enableDisconnect = false) {
         document.getElementById('status_info').innerHTML = message;
         document.getElementById('connBle').disabled = !enableConnect;
         document.getElementById('disconnBle').disabled = !enableDisconnect;
     }

     // 尝试重新连接到设备
     async reconnect() {
         if (this.reconnectAttempts >= BLE_MAX_RETRIES || !this.device) {
             console.log(`达到最大重连次数(${BLE_MAX_RETRIES})或设备不存在，不再尝试重连`);
             this.updateUI('重连失败，请手动重新连接');
             this.reconnectAttempts = 0;
             return false;
         }

         this.reconnectAttempts++;
         console.log(`尝试重连 (${this.reconnectAttempts}/${BLE_MAX_RETRIES})...`);
         this.updateUI(`正在尝试重新连接...(${this.reconnectAttempts}/${BLE_MAX_RETRIES})`);

         try {
             return await this.connectToDevice(this.device);
         } catch (error) {
             console.log('重连失败:', error);

             if (this.reconnectAttempts < BLE_MAX_RETRIES) {
                 console.log(`${BLE_RECONNECT_DELAY / 1000}秒后将再次尝试...`);
                 setTimeout(() => this.reconnect(), BLE_RECONNECT_DELAY);
             } else {
                 this.updateUI('重连失败，请手动重新连接');
                 this.reconnectAttempts = 0;
             }
             return false;
         }
     }

     // 连接设备 - 此方法分离出来供初始连接和重连使用
     async connectToDevice(device) {
         console.log("尝试连接到GATT服务器...");

         // 使用超时Promise包装连接过程
         try {
             // 先执行一些清理工作
             this.cleanupConnection(false);
             
             // 如果最近刚断开连接，增加一个短暂的延迟
             if (window.lastBleDisconnectTime && (new Date().getTime() - window.lastBleDisconnectTime) < 1500) {
                 console.log("设备最近刚断开，增加延迟以确保蓝牙堆栈准备好");
                 await sleep(1500);
             }
             
             const connectWithTimeout = Promise.race([
                 device.gatt.connect(),
                 new Promise((_, reject) =>
                     setTimeout(() => reject(new Error('连接超时')), BLE_CONNECTION_TIMEOUT)
                 )
             ]);

             this.server = await connectWithTimeout;
             console.log("GATT服务器连接成功");

             // 增加一个短暂的延迟，让设备有时间准备服务
             await sleep(500);

             // 尝试获取所有已知服务
             let serviceFound = false;
             let serviceError = null;
             
             for (const serviceUuid of ESP32_SERVICE_UUIDS) {
                 try {
                     // 尝试获取服务
                     this.service = await this.server.getPrimaryService(serviceUuid);
                     if (this.service) {
                         console.log(`找到服务: ${serviceUuid}`);
                         serviceFound = true;
                         break;
                     }
                 } catch (e) {
                     console.log(`服务 ${serviceUuid} 不可用: ${e.message}`);
                     serviceError = e;
                     
                     // 如果失败是因为设备没有准备好，等待一会再试
                     if (e.message.includes('没有找到') || e.message.includes('not found')) {
                         await sleep(800);
                         try {
                             this.service = await this.server.getPrimaryService(serviceUuid);
                             if (this.service) {
                                 console.log(`第二次尝试找到服务: ${serviceUuid}`);
                                 serviceFound = true;
                                 break;
                             }
                         } catch (e2) {
                             console.log(`第二次尝试获取服务 ${serviceUuid} 仍然失败: ${e2.message}`);
                         }
                     }
                 }
             }

             if (!serviceFound) {
                 const errorMsg = serviceError ? 
                     `找不到任何所需的蓝牙服务: ${serviceError.message}` : 
                     "找不到任何所需的蓝牙服务";
                 throw new Error(errorMsg);
             }

             console.log("服务发现成功");

             this.tx_char = await this.service.getCharacteristic(MICROBLOCKS_TX_CHAR_UUID);
             this.rx_char = await this.service.getCharacteristic(MICROBLOCKS_RX_CHAR_UUID);
             console.log("特征值获取成功");

             // 确保之前的监听器被移除
             try {
                 this.tx_char.removeEventListener('characteristicvaluechanged', this.handle_read);
             } catch (e) {
                 // 忽略错误
             }

             await this.tx_char.startNotifications();
             this.tx_char.addEventListener("characteristicvaluechanged", this.handle_read);
             this.notificationsActive = true;

             this.connected = true;
             deviceIsConnected = true;
             this.sendInProgress = false;
             this.reconnectAttempts = 0; // 重置重连计数

             console.log("蓝牙连接成功");

             // 更新UI
             this.updateUI('已连接蓝牙设备: ' + device.name, false, true);

             // 更新设备缓存
             deviceCache = device;

             // 确保标记为非断开状态
             isDisconnecting = false;

             // 发送一个stop命令测试连接稳定性
             try {
                 const stopData = bleSerialBroadcastCmd('stop');
                 await this.write_data(stopData);
                 console.log("连接测试成功");
             } catch (e) {
                 console.warn("连接测试命令失败:", e);
             }

             return true;
         } catch (error) {
             console.error('连接到GATT服务器失败:', error);
             this.cleanupConnection();
             throw error;
         }
     }

     // 主连接方法
     async connect() {
         try {
             this.disconnectRequested = false;

             // 首先尝试使用当前实例的设备进行重连
             if (this.device) {
                 console.log('尝试连接到之前的设备:', this.device.name || '未知设备');
                 try {
                     return await this.connectToDevice(this.device);
                 } catch (error) {
                     console.log('之前设备连接失败，尝试使用缓存或发现新设备:', error);
                     // 不清除设备引用，可能在某些浏览器中可以重用
                 }
             }

             // 尝试使用缓存的设备进行重连
             if (deviceCache) {
                 console.log('尝试连接到缓存的设备:', deviceCache.name || '未知设备');
                 try {
                     // 更新当前实例的设备引用
                     this.device = deviceCache;
                     return await this.connectToDevice(deviceCache);
                 } catch (error) {
                     console.log('缓存设备连接失败，尝试发现新设备:', error);
                     clearDeviceCache(); // 清除已失效的缓存
                 }
             }

             // 请求新的蓝牙设备
             const requestOptions = {
                 // 使用多个过滤器来提高设备发现率
                 filters: [
                     // 主要通过服务UUID过滤
                     { services: [MICROBLOCKS_SERVICE_UUID] },
                     { namePrefix: 'Micro' },
                     { namePrefix: 'micro' },
                     { namePrefix: 'ESP' }
                 ],
                 // 可选服务
                 optionalServices: ESP32_SERVICE_UUIDS
             };

             console.log('请求新的蓝牙设备...');
             this.updateUI('请选择您的蓝牙设备...');
             
             this.device = await navigator.bluetooth.requestDevice(requestOptions);

             console.log("设备选择成功:", this.device.name || "未知设备");
             this.device.addEventListener('gattserverdisconnected', this.handle_disconnected);

             return await this.connectToDevice(this.device);
         } catch (error) {
             console.error('连接错误：', error);
             // 连接失败时重置状态
             this.cleanupConnection();
             
             // 更新UI显示具体错误
             this.updateUI('连接失败: ' + error.message);
             return false;
         }
     }

     // 安全地停止通知
     async stopNotificationsSafely() {
         if (this.tx_char && this.notificationsActive) {
             try {
                 console.log('尝试停止通知...');
                 // 首先移除事件监听器
                 this.tx_char.removeEventListener('characteristicvaluechanged', this.handle_read);

                 // 然后停止通知
                 await this.tx_char.stopNotifications();
                 console.log('成功停止通知');
                 this.notificationsActive = false;
                 return true;
             } catch (e) {
                 console.log('停止通知失败，这可能是正常的:', e);
                 return false;
             }
         }
         return false;
     }

     // 单独发送停止指令的方法，以便可以在其他地方调用
     async sendStopCommand() {
         if (!this.isConnected()) {
             console.log("设备未连接，无法发送停止指令");
             return false;
         }
         
         try {
             // 使用全局的sendCommand函数
             return await sendCommand('stop');
         } catch (error) {
             console.error("发送停止指令失败:", error);
             return false;
         }
     }

     async disconnect() {
         // 避免重复断开操作
         if (isDisconnecting) {
             console.log('断开连接操作已经在进行中');
             return;
         }

         isDisconnecting = true;

         // 标记这是用户请求的断开
         this.disconnectRequested = true;

         // 更新UI以反映断开中状态
         this.updateUI('正在停止小车并断开蓝牙连接...', false, true);

         if (this.device && this.server && this.connected) {
             try {
                 // 先发送stop指令停止小车
                 try {
                     document.getElementById('status_info').innerHTML = '<span style="color:#ff6600">正在发送停止指令...</span>';
                     await sendCommand('stop');
                     document.getElementById('status_info').innerHTML = '<span style="color:#33cc33">停止指令已发送，正在断开连接...</span>';
                     
                     // 等待较长时间确保stop指令处理完成
                     await sleep(DISCONNECT_DELAY);
                 } catch (error) {
                     console.log("发送停止指令错误：" + error);
                     document.getElementById('status_info').innerHTML = '<span style="color:#ff0000">停止指令发送失败，正在断开连接...</span>';
                 }

                 // 安全地尝试停止通知
                 await this.stopNotificationsSafely();

                 // 短暂等待以确保通知已停止
                 await sleep(100);

                 // 发送系统重置指令 (OpCode: 0x0F)
                 try {
                     const resetCommand = new Uint8Array([250, 15, 0]); // 0xFA(250), 0x0F(15), 0
                     await this.rx_char.writeValue(resetCommand);
                     console.log("系统重置指令已发送");
                     
                     // 等待短暂时间确保指令处理
                     await sleep(DISCONNECT_DELAY);
                 } catch (error) {
                     console.log("发送重置指令错误：" + error);
                 }

                 console.log('正在断开与设备的连接...');
                 this.device.gatt.disconnect();
                 console.log('断开连接成功');
             } catch (error) {
                 console.error('断开连接时出错:', error);
             }
         } else {
             console.log('无需断开连接，因为设备已经断开或未连接');
         }

         // 无论如何都确保清理状态，但保留设备信息以便重连
         this.cleanupConnection(false); // 设置为false，不清除设备缓存

         // 更新UI
         this.updateUI('蓝牙设备已断开');
         console.log('断开蓝牙设备完成，状态已重置');

         // 重置断开状态标志
         isDisconnecting = false;
     }

     isConnected() {
         return this.connected && this.rx_char != null && !isDisconnecting;
     }

     write_data(data) {
         if (!this.rx_char) {
             throw new TypeError("未连接");
         }
         if (this.sendInProgress || !this.connected || isDisconnecting) {
             return Promise.reject(new Error("正在发送中或未连接"));
         }
         let byteCount = (data.length > BLE_PACKET_LEN) ? BLE_PACKET_LEN : data.length;
         return this.write_loop(data.subarray(0, byteCount)).then(() => byteCount);
     }

     async write_loop(data) {
         this.sendInProgress = true;

         return new Promise(async (resolve, reject) => {
             try {
                 let attempts = 0;
                 const maxAttempts = 3; // 最多尝试3次

                 while (attempts < maxAttempts) {
                     try {
                         await this.rx_char.writeValueWithoutResponse(data);
                         this.sendInProgress = false;
                         resolve(); // 成功时解决Promise
                         return;
                     } catch (error) {
                         attempts++;
                         console.log(`BLE写入失败 (尝试 ${attempts}/${maxAttempts}):\n`, error);

                         // 如果不再连接，则立即放弃
                         if (!this.isConnected()) {
                             this.sendInProgress = false;
                             reject(error); // 失败时拒绝Promise
                             return;
                         }

                         // 如果达到最大尝试次数
                         if (attempts >= maxAttempts) {
                             this.sendInProgress = false;
                             reject(new Error("达到最大写入尝试次数"));
                             return;
                         }

                         // 等待一小段时间再重试
                         await new Promise(r => setTimeout(r, 100));
                     }
                 }
             } catch (error) {
                 this.sendInProgress = false;
                 reject(error);
             }
         });
     }
 }

 const bleSerial = new NimBLESerial();
 
 // 连接按钮事件监听器
 document.getElementById('connBle').addEventListener('click', async () => {
     try {
         document.getElementById('connBle').disabled = true;
         document.getElementById('status_info').innerHTML = '正在搜索蓝牙设备...';
         
         // 检查是否最近断开过连接，如果是则多等待一会儿
         const currentTime = new Date().getTime();
         const lastDisconnectTime = window.lastBleDisconnectTime || 0;
         const timeSinceDisconnect = currentTime - lastDisconnectTime;
         
         // 如果断开连接后不到2秒就尝试重连，先等待一会儿
         if (timeSinceDisconnect < 2000 && timeSinceDisconnect > 0) {
             const waitTime = 2000 - timeSinceDisconnect;
             document.getElementById('status_info').innerHTML = `蓝牙设备准备中，请稍候(${Math.ceil(waitTime/1000)}秒)...`;
             await sleep(waitTime);
         }
         
         await bleSerial.connect();
     } catch (error) {
         console.error('连接过程中出现异常:', error);
         document.getElementById('status_info').innerHTML = '连接失败: ' + error.message;
         document.getElementById('connBle').disabled = false;
     }
 });
 
 // 断开按钮事件监听器
 document.getElementById('disconnBle').addEventListener('click', async () => {
     document.getElementById('disconnBle').disabled = true;
     await bleSerial.disconnect();
     
     // 记录断开连接的时间
     window.lastBleDisconnectTime = new Date().getTime();
 });

 // 命令发送开关事件监听器
 document.getElementById('send-commands-toggle').addEventListener('change', function() {
     sendCommandsEnabled = this.checked;
     updateToggleState();
     
     // 如果关闭发送且蓝牙已连接，发送停止命令
     if (!this.checked && bleSerial.isConnected()) {
         try {
             console.log("禁用蓝牙发送，发送停止命令");
             
             // 安全地发送停止命令
             safelyExecuteBleCommand('stop');
         } catch (e) {
             console.warn("尝试发送停止命令时出错:", e);
         }
     }
 });
 
 // 为整个开关容器添加点击事件，提高用户交互体验
 document.querySelector('.toggle-container').addEventListener('click', function(event) {
     // 排除点击标签文本和状态文本的情况
     if (event.target.tagName.toLowerCase() !== 'label' && 
         event.target.id !== 'toggle-status') {
         const toggleInput = document.getElementById('send-commands-toggle');
         toggleInput.checked = !toggleInput.checked;
         
         // 触发change事件，确保事件处理器被调用
         const changeEvent = new Event('change');
         toggleInput.dispatchEvent(changeEvent);
     }
 });
 
 // 安全地执行蓝牙命令的辅助函数（带重试和超时）
 function safelyExecuteBleCommand(command) {
     // 如果未连接，直接返回
     if (!bleSerial.isConnected()) {
         console.log('设备未连接，无法发送命令');
         return Promise.resolve(false);
     }
     
     // 如果正在发送，先延迟100ms后再尝试
     if (bleSerial.sendInProgress) {
         console.log('命令正在发送中，稍后重试...');
         return new Promise((resolve) => {
             setTimeout(() => {
                 // 重试一次
                 if (!bleSerial.sendInProgress && bleSerial.isConnected()) {
                     const data = bleSerialBroadcastCmd(command);
                     bleSerial.write_data(data)
                         .then(() => resolve(true))
                         .catch(err => {
                             console.warn("重试发送命令失败:", err);
                             resolve(false);
                         });
                 } else {
                     console.log('设备仍在发送或已断开，跳过命令');
                     resolve(false);
                 }
             }, 100);
         });
     }
     
     // 正常情况，直接发送
     const data = bleSerialBroadcastCmd(command);
     return bleSerial.write_data(data)
         .then(() => {
             // console.log(`${command} 命令已发送`);
             return true;
         })
         .catch(err => {
             console.warn(`发送 ${command} 命令失败:`, err);
             return false;
         });
 }
 
 // 更新开关状态显示
 function updateToggleState() {
     const statusElement = document.getElementById('toggle-status');
     
     if (sendCommandsEnabled) {
         statusElement.innerText = '已启用';
         statusElement.style.color = '#4CAF50'; // 绿色
         document.getElementById('status_info').innerHTML = '已启用发送指令到小车';
     } else {
         statusElement.innerText = '已禁用';
         statusElement.style.color = '#F44336'; // 红色
         document.getElementById('status_info').innerHTML = '已禁用发送指令，指令不会发送给小车';
     }
     
     console.log(`命令发送状态: ${sendCommandsEnabled ? '启用' : '禁用'}`);
 }

 // 更新JavaScript代码来处理内存警告
 function updateMemoryInfo(memoryInfo) {
     const usedBytes = formatBytes(memoryInfo.numBytes);
     memoryElement.innerHTML = `内存使用: ${usedBytes}`;
     
     // 使用添加/移除class的方式来显示内存警告
     if (memoryInfo.numBytes > 100 * 1024 * 1024) { // 超过100MB
         memoryElement.classList.add('warning');
     } else {
         memoryElement.classList.remove('warning');
     }
 }

 // 发送命令的通用方法
 async function sendCommand(cmd) {
     // 首先检查是否允许发送命令
     if (!sendCommandsEnabled) {
         // console.log(`命令发送已禁用，跳过发送: ${cmd}`);
         return true; // 返回成功，但实际并未发送
     }
     
     // 如果允许发送，使用安全的命令发送函数
     return safelyExecuteBleCommand(cmd);
 }

 // 重置分类器功能
 document.getElementById('resetClassifier').addEventListener('click', async function() {
     try {
         // 暂停预测
         window.pausePrediction = true;
         
         // 显示状态信息
         document.getElementById('status_info').innerHTML = '<span style="color:#ff6600">正在重置分类器...</span>';
         document.getElementById('console').innerText = '预测已暂停 (正在重置分类器...)';
         
         // 等待短暂时间以确保当前预测循环完成
         await sleep(300);
         
         // 使用TensorFlow作用域来管理内存
         tf.engine().startScope();
         
         // 清空分类器
         classifier.clearAllClasses();
         console.log('分类器已重置，所有样本数据已清空');
         
         // 结束内存作用域
         tf.engine().endScope();
         
         // 手动触发垃圾回收
         tf.disposeVariables();
         
         // 更新样本数量显示
         updateKnnDataInfo();
         
         // 更新UI
         document.getElementById('status_info').innerHTML = '<span style="color:#33cc33">分类器已重置成功！请重新添加样本数据</span>';
         document.getElementById('console').innerText = '请添加样本数据或载入KNN数据';
     } catch (error) {
         console.error('重置分类器时出错:', error);
         document.getElementById('status_info').innerHTML = '<span style="color:#ff0000">重置分类器失败: ' + error.message + '</span>';
     } finally {
         // 恢复预测
         window.pausePrediction = false;
     }
 });

 // 更新KNN样本数量信息显示
 function updateKnnDataInfo() {
     try {
         const dataset = classifier.getClassifierDataset();
         if (!dataset || Object.keys(dataset).length === 0) {
             document.getElementById('knndata_info').innerText = '暂无样本数据，请采先集样本数据';
             return;
         }

         // 获取每个类别的样本数
         const classInfo = [];
         Object.entries(dataset).forEach(([label, tensor]) => {
             // 获取张量形状，第一维是样本数量
             const sampleCount = tensor.shape[0];
             classInfo.push(`${label}(${sampleCount})`);
         });

         // 拼接成 "样本数据：go(3) left(4) right(6) stop(6)" 格式
         document.getElementById('knndata_info').innerText = '样本数据：' + classInfo.join(' ');
     } catch (error) {
         console.error('更新KNN样本数量信息时出错:', error);
         document.getElementById('knndata_info').innerText = '样本数据：读取错误';
     }
 }

 // 在页面加载时初始化样本数量显示
 document.addEventListener('DOMContentLoaded', function() {
     // 初始化显示
     document.getElementById('knndata_info').innerText = '样本数据：暂无';
     
     // 初始化命令发送开关状态
     const toggleElement = document.getElementById('send-commands-toggle');
     toggleElement.checked = sendCommandsEnabled;
     updateToggleState();
     
     // 添加K值滑块的事件监听器
     document.getElementById('k-value').addEventListener('input', kValueChanged);
     
     // 添加保存和载入按钮的事件监听器
     document.getElementById('save').addEventListener('click', save);
     document.getElementById('load').addEventListener('click', load);
     
     // 添加文件名输入框的事件监听器
     document.getElementById('jsonFileName').addEventListener('change', jsonFileNameChanged);
 });