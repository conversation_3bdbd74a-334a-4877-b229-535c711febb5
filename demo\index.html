<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>KNN Transfer Learning AI RCCar</title>
    <!-- <script src="https://fastly.jsdelivr.net/npm/@tensorflow/tfjs@4.7.0"></script>
    
    <script src="https://fastly.jsdelivr.net/npm/@tensorflow-models/knn-classifier@1.2.4"></script> -->
    
    <!-- CDN脚本已移除，通过模块化方式引入 -->
    
    <style>
        /* 基础样式 */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            padding: 10px;
            background-color: #f8f9fa;
        }

        /* 容器样式 */
        .container {
            height: auto;
            width: 95%;
            max-width: 1200px;
            margin: 10px auto;
            position: relative;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            align-items: center;
        }

        .block {
            margin: 5px;
            width: 100%;
            display: flex;
            justify-content: center;
        }

        /* 按钮基础样式 */
        .btn {
            width: 150px;
            height: 60px;
            font-size: 24px;
            padding: 0;
            line-height: 1;
            border-radius: 8px;
            border: none;
            background-color: #4CAF50;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .btn:hover {
            background-color: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn:active {
            transform: translateY(1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 宽按钮样式 */
        .btn-wide {
            width: 280px;
            font-size: 20px;
        }

        /* 按钮行样式 */
        .buttons-row {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .buttons-row .btn {
            flex: 1;
            min-width: 100px;
            max-width: 200px;
            height: 50px;
            padding: 0 5px;
        }

        /* 控制按钮颜色 */
        #class-a, #go { background-color: #4CAF50; } /* 绿色 - 前进 */
        #class-b, #left { background-color: #2196F3; } /* 蓝色 - 左转 */
        #class-c, #right { background-color: #FF9800; } /* 橙色 - 右转 */
        #class-d, #stop { background-color: #f44336; } /* 红色 - 停止 */
        
        #connBle { background-color: #2196F3; } /* 紫色 - 连接 */
        #disconnBle { background-color: #2196F3; } /* 棕色 - 断开 */
        
        #save, #load { background-color: #607D8B; } /* 蓝灰色 - 数据操作 */

        /* K值选择器样式 */
        .k-selector {
            display: flex;
            align-items: center;
            margin: 15px 0;
            width: 100%;
            max-width: 400px;
            background-color: #fff;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .k-selector label {
            margin-right: 10px;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .k-selector .slider-container {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .k-selector input[type="range"] {
            flex: 1;
            height: 25px;
            margin: 0 10px;
            -webkit-appearance: none;
            appearance: none;
            background: #ddd;
            border-radius: 5px;
            height: 8px;
        }

        .k-selector input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #4CAF50;
            border-radius: 50%;
            cursor: pointer;
        }

        .k-selector input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: #4CAF50;
            border-radius: 50%;
            cursor: pointer;
            border: none;
        }

        .k-selector .k-value-display {
            font-size: 18px;
            font-weight: bold;
            min-width: 30px;
            text-align: center;
            color: #4CAF50;
        }

        /* 网络摄像头容器 */
        .webcam-container {
            position: relative;
            margin: 0 auto;
            text-align: center;
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
            max-width: 224px;
            height: auto;
        }

        #webcam {
            width: 100%;
            height: auto;
            display: block;
        }

        /* 信息显示 */
        #console, #status_info, .system-info-container {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            width: 100%;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        #console {
            background-color: #e3f2fd;
            font-size: 20px;
            font-weight: bold;
            color: #0d47a1;
            min-height: 50px;
        }

        /* 系统信息显示容器 */
        .system-info-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            background-color: #f5f5f5;
            padding: 8px 15px;
            gap: 10px;
        }

        /* 帧率和内存使用信息在同一行显示 */
        #fps, #memory-info {
            flex: 1;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            padding: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: background-color 0.3s ease;
        }

        #fps {
            color: #333;
            background-color: #e8f5e9;
        }

        /* 内存使用情况样式 */
        #memory-info {
            color: #0066cc;
            background-color: #e3f2fd;
        }

        /* 系统信息图标 */
        #fps:before, #memory-info:before {
            content: "";
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 8px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        #fps:before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23388e3c'%3E%3Cpath d='M13 2.05v3.03c3.39.49 6 3.39 6 6.92 0 .9-.18 1.75-.5 2.54l2.6 1.53c.56-1.24.9-2.62.9-4.07 0-5.18-3.95-9.45-9-9.95zM12 19c-3.87 0-7-3.13-7-7 0-3.53 2.61-6.43 6-6.92V2.05c-5.06.5-9 4.76-9 9.95 0 5.52 4.47 10 9.99 10 3.31 0 6.24-1.61 8.06-4.09l-2.6-1.53C16.17 17.98 14.21 19 12 19z'/%3E%3C/svg%3E");
        }

        #memory-info:before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230d47a1'%3E%3Cpath d='M2 2v20h20V2H2zm2 2h16v16H4V4zm2 4h4v2H6V8zm0 4h4v2H6v-2zm0 4h4v2H6v-2zm10 0h-4v-2h4v2z'/%3E%3C/svg%3E");
        }

        /* 高内存警告样式 */
        #memory-info.warning {
            color: #ff3d00;
            background-color: #fff3e0;
        }

        #memory-info.warning:before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff3d00'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
        }

        /* 状态信息样式 */
        #status_info {
            color: #e53935;
            font-weight: bold;
            font-size: 16px;
            background-color: #ffebee;
            min-height: 50px;
        }

        /* 文件上传区域 */
        .file-upload {
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
            margin: 15px auto;
        }

        .file-upload input[type="text"] {
            padding: 8px 12px;
            width: 100%;
            margin: 8px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }

        .file-upload input[type="file"] {
            margin: 10px 0;
            width: 100%;
        }
        
        /* 切换开关样式 */
        .toggle-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
            padding: 10px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .toggle-container label {
            margin-right: 10px;
            font-weight: bold;
            color: #333;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #2196F3;
        }
        
        input:focus + .slider {
            box-shadow: 0 0 1px #2196F3;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .toggle-status {
            margin-left: 10px;
            font-weight: bold;
            min-width: 80px;
        }

        /* 响应式布局 - 平板电脑 */
        @media (min-width: 601px) and (max-width: 900px) {
            .container {
                width: 90%;
            }

            .btn {
                font-size: 20px;
                height: 50px;
            }

            .btn-wide {
                width: 240px;
                font-size: 18px;
            }

            .buttons-row .btn {
                min-width: 90px;
                max-width: 160px;
            }

            #console {
                font-size: 18px;
            }
        }

        /* 响应式布局 - 手机 */
        @media (max-width: 600px) {
            .container {
                width: 98%;
                padding: 5px;
            }

            .btn {
                font-size: 16px;
                height: 45px;
                width: 120px;
            }

            .btn-wide {
                width: 200px;
                font-size: 16px;
            }

            .buttons-row .btn {
                min-width: 70px;
                max-width: 130px;
                font-size: 14px;
            }

            .k-selector {
                flex-direction: column;
                align-items: flex-start;
            }

            .k-selector label {
                margin-bottom: 5px;
            }

            #console {
                font-size: 16px;
                padding: 8px;
            }

            #status_info, #fps, #memory-info {
                font-size: 13px;
                padding: 6px 3px;
            }

            #fps:before, #memory-info:before {
                width: 14px;
                height: 14px;
                margin-right: 4px;
            }

            .system-info-container {
                padding: 5px;
            }
        }

        /* 极小屏幕设备（小型手机）*/
        @media (max-width: 380px) {
            .btn {
                font-size: 14px;
                height: 40px;
                width: 100px;
            }

            .btn-wide {
                width: 180px;
                font-size: 14px;
            }

            .buttons-row {
                gap: 5px;
            }

            .buttons-row .btn {
                min-width: 60px;
                font-size: 12px;
            }

            #fps, #memory-info {
                font-size: 12px;
                padding: 4px 2px;
            }
            
            #fps:before, #memory-info:before {
                width: 12px;
                height: 12px;
                margin-right: 3px;
            }
        }

        #knndata_info {
            background-color: #e8f5e9;
            border-radius: 8px;
            padding: 8px;
            font-weight: bold;
            color: #2e7d32;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="webcam-container">
            <video autoplay playsinline muted id="webcam" width="224" height="224"></video>
        </div>
    </div>
    <div class="container">
        <div id="console">等待预测结果...</div>
    </div>

    <!-- 系统信息：帧率和内存使用（在一行中显示） -->
    <div class="container">
        <div class="system-info-container">
            <div id="fps">帧率: 0 FPS</div>
            <div id="memory-info">内存使用: 0 B</div>
        </div>
    </div>

    <!-- 控制按钮 -->
    <div class="container">
        <div class="buttons-row">
            <button id="class-a" class="btn">go</button>
            <button id="class-b" class="btn">left</button>
            <button id="class-c" class="btn">right</button>
            <button id="class-d" class="btn">stop</button>
        </div>
    </div>

    <!-- K值选择器 -->
    <div class="container">
        <div class="block k-selector">
            <label for="k-value">K值:</label>
            <div class="slider-container">
                <input type="range" id="k-value" min="1" max="9" value="3">
                <div class="k-value-display" id="k-value-display">3</div>
            </div>
        </div>
    </div>
    <div class="container">
        <div id="knndata_info"></div>
    </div>
    <div class="container">
        <div id="status_info">准备就绪</div>
    </div>
    
    <!-- 控制是否发送指令的开关 -->
    <div class="container">
        <div class="toggle-container">
            <label for="send-commands-toggle">发送指令到小车:</label>
            <label class="toggle-switch">
                <input type="checkbox" id="send-commands-toggle" checked>
                <span class="slider"></span>
            </label>
            <span id="toggle-status" class="toggle-status">已启用</span>
        </div>
    </div>
    
    <!-- 蓝牙连接按钮 -->
    <div class="container">
        <div class="buttons-row">
            <button id="connBle" class="btn">连接蓝牙</button>
            <button id="disconnBle" class="btn">断开蓝牙</button>
        </div>
    </div>
    
    <!-- KNN数据保存和加载 -->
    <div class="container">
        <div class="block"><button id="save" class="btn btn-wide">保存KNN样本数据</button></div>
    </div>
    <div class="container">
        <div class="block"><button id="load" class="btn btn-wide">载入KNN样本数据</button></div>
    </div>

    <!-- 文件选择区域 -->
    <div class="container">
        <div class="file-upload">
            <div>KNN数据文件名：<input id="jsonFileName" type="text" value="myKnnDataset"></div>
            <input type="file" id="file-selector" alt="上传KNN数据">
        </div>
    </div>
    
    <!-- 重置分类器按钮 -->
    <div class="container">
        <div class="block"><button id="resetClassifier" class="btn btn-wide" style="background-color: #E53935;">重置分类器</button></div>
    </div>

    <div class="container">
    <div>开发：上海市松江区青少年综合实践教育中心 汤铭</div> 
    </div>
    
    <!-- 引入JavaScript主文件 -->
    <script type="module" src="index.js"></script>
</body>

</html>