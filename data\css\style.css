/* ESP32-S3 AI自动驾驶小车 - 样式表 */
/* 响应式设计，支持移动设备和桌面设备 */

body {
    font-family: Arial, sans-serif;
    text-align: center;
    margin: 0;
    padding: 10px;
    max-width: 100%;
    box-sizing: border-box;
}

h3 {
    margin: 0;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 10px;
}

.image-container {
    margin: 10px auto;
    width: 224px;
    height: 224px;
    overflow: hidden;
}

.image-container img {
    width: 224px;
    height: 224px;
    object-fit: cover;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#console {
    margin: 15px auto;
    padding: 10px;
    border: 1px solid #ddd;
    min-height: 40px;
    background-color: #fafafa;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    word-wrap: break-word;
    color: #333;
    text-align: left;
    font-weight: normal;
    overflow-x: auto;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

#console table {
    width: 100%;
    margin-top: 10px;
    border-collapse: collapse;
    font-size: 0.9em;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

#console th {
    background-color: #f0f0f0;
    font-weight: bold;
    padding: 6px 4px;
}

#console th, #console td {
    border: 1px solid #ddd;
    padding: 4px;
    text-align: center;
}

#console .top-k {
    background-color: #a5d6a7;
    border: 1px solid #4CAF50;
    font-weight: bold;
}

#console .prediction-result {
    font-size: 1.1em;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    color: #e53935;
}

#console .vote-result {
    font-size: 0.95em;
    margin-bottom: 8px;
    color: #e53935;
}

#console .vote-value {
    font-weight: bold;
    color: #b71c1c;
}

button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* 按钮基础样式 - 与demo保持一致 */
.btn {
    width: 150px;
    height: 60px;
    font-size: 24px;
    padding: 0;
    line-height: 1;
    border-radius: 8px;
    border: none;
    background-color: #4CAF50;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.btn:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 宽按钮样式 */
.btn-wide {
    width: 280px;
    font-size: 20px;
}

/* 按钮行样式 */
.buttons-row {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
}

.buttons-row .btn {
    flex: 1;
    min-width: 100px;
    max-width: 200px;
    height: 50px;
    padding: 0 5px;
}

#status {
    margin: 10px auto;
    color: #0d47a1;
    width: 90%;
    max-width: 600px;
    padding: 12px;
    font-weight: bold;
    font-size: 1.1em;
    background-color: #e3f2fd;
    border-radius: 8px;
    border: 1px solid #bbdefb;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 系统信息容器样式 */
.system-info-container {
    display: flex;
    justify-content: space-around;
    gap: 10px;
    margin: 10px auto;
    width: 90%;
    max-width: 600px;
}

#fps, #memory-info {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 8px;
    font-size: 0.95em;
    font-weight: bold;
}

#fps {
    background-color: #e8f5e9;
    color: #2e7d32;
}

#memory-info {
    background-color: #e3f2fd;
    color: #0d47a1;
}

#fps svg, #memory-info svg {
    margin-right: 8px;
}

.sample-counts {
    margin: 5px auto;
    display: flex;
    justify-content: center;
    flex-wrap: nowrap;
    gap: 5px;
    width: 90%;
    max-width: 600px;
    padding: 5px;
    box-sizing: border-box;
}

.sample-counter {
    display: inline-block;
    padding: 5px 5px;
    border-radius: 15px;
    color: white;
    font-weight: bold;
    margin: 2px;
    font-size: 0.95em;
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin: 10px auto;
    width: 90%;
    max-width: 600px;
}

.sample-button-group {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px dotted #eee;
}

.reset-button-group {
    margin-top: 15px;
}

.sample-label {
    font-weight: bold;
    margin-right: 10px;
    display: inline-block;
    margin-bottom: 8px;
}

.sample-buttons-container {
    width: 100%;
    text-align: center;
    padding: 5px;
}

.sample-buttons-row {
    display: flex;
    justify-content: center;
    gap: 25px;
    margin: 12px 0;
}

.counter-go { background-color: #2196F3; }
.counter-left { background-color: #FF9800; }
.counter-right { background-color: #9C27B0; }
.counter-stop { background-color: #E91E63; }

#class-go, #class-left, #class-right, #class-stop {
    margin: 5px 8px;
    font-size: 1.2em;
    padding: 12px 24px;
    min-width: 80px;
}

#class-go { background-color: #2196F3; }
#class-left { background-color: #FF9800; }
#class-right { background-color: #9C27B0; }
#class-stop { background-color: #E91E63; }

/* 控制部分样式 */
.control-section {
    margin: 15px auto;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    max-width: 600px;
}

/* 开关容器样式 */
.switch-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.switch-container label {
    margin-right: 15px;
    font-weight: bold;
    font-size: 1em;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

#toggle-status {
    margin-left: 10px;
    font-weight: bold;
    font-size: 1em;
}

/* 文件输入容器样式 */
.file-input-container {
    margin-top: 15px;
    padding: 10px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.file-input-container label {
    font-weight: bold;
}

.file-input-container input[type="text"] {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.file-input-container button {
    background-color: #607D8B;
}

/* 按钮组内按钮通用样式 */
.button-group button {
    background-color: #007bff; /* 默认蓝色 */
    flex: 1; /* 占据等分空间 */
    min-width: 150px;
}

/* 特定按钮颜色 */
#connBle { background-color: #2196F3; }
#disconnBle { background-color: #2196F3; }
#save-data, #load-data { background-color: #607D8B; }
#reset { background-color: #f44336; }

/* 调整特定按钮的尺寸和字体 */
#connBle, #disconnBle, #save-data, #load-data {
    min-width: 150px;
    max-width: 200px;
    height: 60px;
    font-size: 20px;
    padding: 0;
    line-height: 1;
    flex-grow: 0; /* 防止按钮过度拉伸 */
}

#reset {
    font-size: 1.1em; /* 增大字体 */
    min-width: 140px;   /* 减小宽度 */
    max-width: 200px;
    padding: 10px 20px;
}

.k-value-control {
    margin: 15px auto;
    width: 90%;
    max-width: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 8px 0 15px 0;
    border-bottom: 1px dotted #eee;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.k-value-control label {
    font-weight: bold;
    color: #333;
    font-size: 1em;
}

.k-value-control input {
    width: 60%;
    max-width: 300px;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    text-align: center;
    background-color: #fff;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.k-value-control span {
    font-weight: bold;
    color: #b71c1c;
    min-width: 30px;
    text-align: center;
    font-size: 1.2em;
    padding: 2px 8px;
    background-color: #ffebee;
    border-radius: 4px;
}

/* 移动设备适配 */
@media screen and (max-width: 600px) {
    h3 { font-size: 1.2em; }
    button { padding: 10px 16px; font-size: 1em; }
    #class-go, #class-left, #class-right, #class-stop {
        margin: 8px; padding: 15px 20px; min-width: 80px; font-size: 1.1em;
    }
    .sample-buttons-row { gap: 16px; }
    .sample-counter { padding: 4px 8px; font-size: 0.85em; margin: 1px; }
    .sample-counts { gap: 3px; }
    #console, #status { font-size: 0.9em; }
    
    /* 蓝牙按钮响应式样式 */
    .btn {
        font-size: 14px;
        height: 45px;
        width: 120px;
    }
    
    .buttons-row .btn {
        min-width: 70px;
        max-width: 130px;
        font-size: 12px;
    }
}

@media screen and (max-width: 400px) {
    button { margin: 5px; padding: 10px 15px; font-size: 0.9em; }
    #class-go, #class-left, #class-right, #class-stop {
        margin: 6px; padding: 14px 18px; min-width: 70px; font-size: 1em;
    }
    .button-group { width: 100%; }
    .sample-button-group { display: flex; flex-direction: column; width: 92%; }
    .sample-buttons-row { gap: 10px; }
    .sample-counter { padding: 3px 6px; font-size: 0.8em; margin: 1px; border-radius: 10px; }
    .sample-counts { gap: 2px; width: 98%; }
    
    /* 蓝牙按钮极小屏幕响应式样式 */
    .btn {
        font-size: 12px;
        height: 40px;
        width: 100px;
    }
    
    .buttons-row {
        gap: 5px;
    }
    
    .buttons-row .btn {
        min-width: 60px;
        font-size: 10px;
    }
}

#console .predicted-class {
    font-weight: bold;
    font-size: 1.1em;
    color: #b71c1c;
    background-color: #ffebee;
    padding: 2px 6px;
    border-radius: 4px;
    margin: 0 2px;
}

#console .probability {
    font-weight: bold;
    color: #b71c1c;
}

#console .k-value {
    color: #b71c1c;
    font-weight: bold;
}

#console .vote-title {
    display: inline-block;
    font-weight: bold;
    margin-right: 8px;
} 