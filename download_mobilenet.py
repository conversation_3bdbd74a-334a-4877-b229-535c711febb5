#!/usr/bin/env python3
"""
下载MobileNet模型文件到ESP32文件系统
"""

import os
import requests
import json
from pathlib import Path

# 模型URL配置 - 使用更小的0.25版本
MODEL_BASE_URL = "https://storage.googleapis.com/tfjs-models/tfjs/mobilenet_v1_0.25_224/"

# 目标目录
TARGET_DIR = Path("data/models/mobilenet")

def get_model_files():
    """获取所有模型文件列表"""
    files = {"model.json": "model.json"}
    
    # 添加所有分片文件 group1-shard1of1 到 group55-shard1of1
    for i in range(1, 56):  # 1到55
        filename = f"group{i}-shard1of1"
        files[filename] = filename
    
    return files

def download_file(url, filepath):
    """下载文件"""
    print(f"正在下载: {url}")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        # 创建目录（如果不存在）
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # 下载文件
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        file_size = filepath.stat().st_size
        print(f"✅ 下载完成: {filepath.name} ({file_size:,} bytes = {file_size/1024:.1f} KB)")
        return True, file_size
        
    except Exception as e:
        print(f"❌ 下载失败: {filepath.name} - {e}")
        return False, 0

def main():
    """主函数"""
    print("=" * 70)
    print("MobileNet v1 0.25 224 模型下载工具")
    print("=" * 70)
    
    # 检查目标目录
    if not TARGET_DIR.exists():
        TARGET_DIR.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {TARGET_DIR}")
    
    # 获取所有文件列表
    model_files = get_model_files()
    print(f"准备下载 {len(model_files)} 个文件...")
    
    total_size = 0
    success_count = 0
    failed_files = []
    
    # 下载每个文件
    for i, (filename, url_path) in enumerate(model_files.items(), 1):
        url = MODEL_BASE_URL + url_path
        filepath = TARGET_DIR / filename
        
        print(f"\n[{i}/{len(model_files)}] ", end="")
        success, file_size = download_file(url, filepath)
        
        if success:
            success_count += 1
            total_size += file_size
        else:
            failed_files.append(filename)
    
    print("\n" + "=" * 70)
    print("下载摘要:")
    print(f"成功下载: {success_count}/{len(model_files)} 个文件")
    print(f"总大小: {total_size:,} bytes = {total_size/1024:.1f} KB = {total_size/1024/1024:.2f} MB")
    
    if failed_files:
        print(f"\n❌ 下载失败的文件 ({len(failed_files)} 个):")
        for filename in failed_files:
            print(f"  - {filename}")
    
    if success_count == len(model_files):
        print("\n🎉 所有模型文件下载成功!")
        print("\n📋 存储空间检查:")
        print(f"模型大小: {total_size/1024/1024:.2f} MB")
        print(f"可用空间: 4.62 MB")
        if total_size/1024/1024 <= 4.5:
            print("✅ 存储空间充足")
        else:
            print("⚠️  存储空间可能不足，请检查")
        
        print("\n📝 下一步:")
        print("1. 使用 'pio run --target uploadfs' 上传文件系统")
        print("2. 烧录固件到ESP32")
        print("3. 测试本地模型加载")
    else:
        print(f"\n❌ {len(failed_files)} 个文件下载失败，请检查网络连接后重试")
    
    print("=" * 70)

if __name__ == "__main__":
    main() 