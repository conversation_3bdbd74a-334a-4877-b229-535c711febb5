# ESP32-S3-EYE 开发板快速开始指南

## 开发板概述

ESP32-S3-EYE 是乐鑫官方推出的基于 ESP32-S3 芯片的 AI 开发板，专为计算机视觉和语音识别应用设计。

### 硬件规格
- **芯片**: ESP32-S3 (双核 Xtensa LX7 @ 240MHz)
- **Flash**: 16MB
- **PSRAM**: 8MB OPI PSRAM
- **摄像头**: OV2640 (2MP)
- **显示屏**: 1.3英寸 LCD (240x240)
- **麦克风**: 数字麦克风
- **加速度计**: 三轴加速度计
- **USB**: USB-C 接口

### 摄像头引脚配置
```cpp
#define PWDN_GPIO_NUM     -1
#define RESET_GPIO_NUM    -1
#define XCLK_GPIO_NUM     15
#define SIOD_GPIO_NUM     4
#define SIOC_GPIO_NUM     5

#define Y2_GPIO_NUM       11
#define Y3_GPIO_NUM       9
#define Y4_GPIO_NUM       8
#define Y5_GPIO_NUM       10
#define Y6_GPIO_NUM       12
#define Y7_GPIO_NUM       18
#define Y8_GPIO_NUM       17
#define Y9_GPIO_NUM       16

#define VSYNC_GPIO_NUM    6
#define HREF_GPIO_NUM     7
#define PCLK_GPIO_NUM     13
```

## 编译和上传

### 1. 环境准备
确保已安装 PlatformIO IDE 或 PlatformIO Core。

### 2. 选择环境
在 `platformio.ini` 中使用 `esp32s3_eye` 环境：

```ini
[env:esp32s3_eye]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino
```

### 3. 编译命令
```bash
# 编译项目
pio run -e esp32s3_eye

# 编译并上传
pio run -e esp32s3_eye -t upload

# 编译并上传文件系统
pio run -e esp32s3_eye -t uploadfs

# 监控串口输出
pio device monitor -e esp32s3_eye
```

### 4. 一键编译上传
```bash
# 完整流程：编译 + 上传固件 + 上传文件系统 + 监控
pio run -e esp32s3_eye -t upload && pio run -e esp32s3_eye -t uploadfs && pio device monitor -e esp32s3_eye
```

### 4. 文件系统上传（重要）
ESP32-S3-EYE 需要上传文件系统才能正常显示Web界面：

```bash
# 单独上传文件系统
pio run -e esp32s3_eye -t uploadfs

# 如果上传失败，先清除再上传
pio run -e esp32s3_eye -t erase
pio run -e esp32s3_eye -t uploadfs
```

**注意**: 如果跳过文件系统上传，设备将使用备用HTML页面，功能会受限。

## 功能特性

### 支持的功能
- ✅ 摄像头实时预览 (240x240 分辨率)
- ✅ AI 图像分类 (TensorFlow.js)
- ✅ 数据收集和训练
- ✅ WebSocket 实时通信
- ✅ 文件系统支持 (LittleFS)
- ✅ WiFi 连接和 AP 模式
- ✅ PSRAM 优化 (8MB)

### 优化配置
- **分辨率**: 240x240 (正方形，适合 AI 分类)
- **像素格式**: RGB565 (优先) / YUV422 / GRAYSCALE (备选)
- **帧缓冲**: 双缓冲 (PSRAM 可用时)
- **JPEG 质量**: 30 (平衡速度和质量)

## 使用步骤

### 1. 硬件连接
- 使用 USB-C 线连接 ESP32-S3-EYE 到电脑
- 确保摄像头模块正确连接

### 2. 首次配置
1. 上传固件和文件系统
2. 打开串口监视器 (115200 波特率)
3. 等待设备启动并连接 WiFi

### 3. 访问 Web 界面
1. 在串口输出中找到设备 IP 地址
2. 在浏览器中访问 `http://[设备IP]`
3. 开始使用摄像头和 AI 功能

## 故障排除

### 常见问题

#### 1. 显示备用HTML页面而非文件系统版本
**症状**: 网页显示简化版界面，功能受限
**原因**: 文件系统未上传或上传失败
**解决方案**:
```bash
# 重新上传文件系统
pio run -e esp32s3_eye -t uploadfs

# 如果失败，先清除再上传
pio run -e esp32s3_eye -t erase
pio run -e esp32s3_eye -t uploadfs
```

**调试方法**: 查看串口输出，寻找以下信息：
- `✅ LittleFS挂载成功` - 文件系统正常
- `⚠️ 文件系统为空` - 需要上传文件系统
- `❌ LittleFS挂载失败` - 分区配置问题

#### 2. 摄像头初始化失败
- 检查摄像头模块连接
- 确认引脚配置正确
- 尝试降低时钟频率

#### 3. PSRAM 检测失败
- 确认 PSRAM 配置正确
- 检查 `board_build.psram_type = opi`

#### 4. WiFi 连接问题
- 检查 WiFi 凭据
- 尝试使用 AP 模式进行配置

#### 5. 编译错误
- 确认选择了正确的环境 `esp32s3_eye`
- 检查 PlatformIO 平台版本

### 调试技巧
1. **串口监视器**: 查看详细的启动和运行日志
2. **文件系统检查**: 观察文件系统初始化过程
3. **I2C设备扫描**: 检查摄像头传感器连接
4. **内存监控**: 确保有足够的可用内存
5. **分区信息**: 验证分区表配置正确

### 详细调试指南
如需深入调试文件系统问题，请参考：
📖 [文件系统调试指南](filesystem_debug_guide.md)

## 性能优化

### 摄像头参数调整
```cpp
// 亮度调整 (-2 到 2)
adjustCameraSettings(1, 1, 0, 1, 5);

// 参数说明：亮度, 对比度, 饱和度, 曝光等级, 增益
```

### 内存优化
- 启用 PSRAM 支持
- 使用双缓冲提高性能
- 优化图像分辨率和质量

## 技术支持

如遇到问题，请：
1. 查看串口输出日志
2. 检查硬件连接
3. 参考官方文档
4. 提交 Issue 并附上详细信息

---

**注意**: ESP32-S3-EYE 开发板具有丰富的外设，本项目主要专注于摄像头和 AI 功能。其他外设（如 LCD、麦克风、加速度计）可根据需要进行扩展。
