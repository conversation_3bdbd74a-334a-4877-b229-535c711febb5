# 文件系统上传指南

## 🎯 问题解决

您遇到的404错误是因为文件系统没有上传到ESP32设备。

## 📋 上传步骤

### 方法1: 使用VSCode PlatformIO插件

1. **打开VSCode**
2. **确保PlatformIO插件已安装**
3. **在VSCode底部状态栏找到PlatformIO图标**
4. **点击 "Upload Filesystem Image"** 或使用快捷键
5. **等待上传完成**

### 方法2: 使用PlatformIO命令行

如果命令行可用：
```bash
# 上传文件系统
platformio run --target uploadfs

# 或者使用简写
pio run -t uploadfs
```

### 方法3: 使用VSCode命令面板

1. **按 Ctrl+Shift+P** 打开命令面板
2. **输入 "PlatformIO: Upload Filesystem Image"**
3. **选择并执行命令**

## 🔍 验证上传

上传成功后，串口监视器会显示：

```
初始化文件系统...
LittleFS挂载成功
文件系统总容量: 4736 KB
已使用空间: 35 KB
可用空间: 4701 KB
使用率: 0.7%

文件系统内容:
  /index.html (2048 bytes)
  /css/style.css (8192 bytes)
  /js/app.js (16384 bytes)
文件总大小: 26624 bytes (26.0 KB)
```

## 🛠️ 故障排除

### 如果上传失败：

1. **检查设备连接**
   - 确保ESP32正确连接到电脑
   - 检查COM端口是否正确

2. **检查文件结构**
   ```
   data/
   ├── index.html
   ├── css/
   │   └── style.css
   └── js/
       └── app.js
   ```

3. **重新编译并上传**
   ```bash
   # 先上传文件系统
   pio run -t uploadfs
   
   # 再上传程序
   pio run -t upload
   ```

### 如果仍然404：

1. **检查串口输出**
   - 查看文件系统是否正确挂载
   - 确认文件列表是否正确

2. **手动测试**
   - 访问 `http://192.168.0.241/` 查看主页
   - 检查浏览器开发者工具的网络标签

3. **重启设备**
   - 有时需要重启ESP32以刷新文件系统

## 📊 调试信息

修改后的代码会提供详细的调试信息：

```
请求文件: /css/style.css
文件不存在: /css/style.css
可用文件列表:
  /index.html
  /css/style.css
  /js/app.js
```

这将帮助您确定：
- 文件是否存在
- 路径是否正确
- 文件系统是否正常工作

## ✅ 成功标志

当一切正常时，您会看到：

```
请求文件: /css/style.css
成功发送文件: /css/style.css (8192 bytes)
```

## 🔄 完整流程

1. **确保data目录结构正确**
2. **上传文件系统** (`Upload Filesystem Image`)
3. **编译并上传程序** (`Upload`)
4. **打开串口监视器** 查看调试信息
5. **访问网页** 测试功能

按照这个流程，您的静态文件应该能正常加载！ 