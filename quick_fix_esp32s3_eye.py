#!/usr/bin/env python3
"""
ESP32-S3-EYE 快速修复脚本
简化版本，避免复杂的上传参数
"""

import subprocess
import time
import sys
import os

def run_command(command, description, timeout=120):
    """执行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"🔧 {description}")
    print(f"📝 命令: {command}")
    print('='*50)
    
    try:
        # 使用实时输出
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            cwd=os.getcwd()
        )
        
        # 实时显示输出
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
                output_lines.append(output.strip())
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"\n✅ {description} - 成功")
            return True
        else:
            print(f"\n❌ {description} - 失败 (返回码: {return_code})")
            return False
            
    except Exception as e:
        print(f"\n💥 {description} - 异常: {e}")
        return False

def main():
    """主修复流程"""
    print("🚀 ESP32-S3-EYE 快速修复工具")
    print("=" * 50)
    
    # 检查基本文件
    if not os.path.exists("platformio.ini"):
        print("❌ 未找到 platformio.ini 文件")
        return False
    
    print("✅ 项目文件检查通过")
    
    # 简化的修复步骤
    steps = [
        ("pio run -e esp32s3_eye -t erase", "步骤 1: 擦除 Flash"),
        ("pio run -e esp32s3_eye -t clean", "步骤 2: 清理构建"),
        ("pio run -e esp32s3_eye", "步骤 3: 编译固件"),
        ("pio run -e esp32s3_eye -t upload", "步骤 4: 上传固件"),
    ]
    
    # 执行修复步骤
    for command, description in steps:
        print(f"\n🎯 开始执行: {description}")
        
        if not run_command(command, description):
            print(f"\n💥 修复失败于: {description}")
            
            # 提供具体的解决建议
            if "上传固件" in description:
                print("\n🛠️  上传失败的可能解决方案:")
                print("1. 检查 ESP32-S3-EYE 是否正确连接")
                print("2. 确认 COM 端口是否正确")
                print("3. 尝试按住 BOOT 按钮再上传")
                print("4. 检查 USB 线质量")
                print("5. 尝试不同的 USB 端口")
                print("\n🔄 手动重试命令:")
                print(f"   {command}")
            
            return False
        
        # 在关键步骤之间等待
        if "上传固件" in description:
            print("⏳ 等待设备重启...")
            time.sleep(3)
    
    # 上传文件系统
    print("\n🔄 准备上传文件系统...")
    time.sleep(2)
    
    if run_command("pio run -e esp32s3_eye -t uploadfs", "步骤 5: 上传文件系统"):
        print("\n🎉 修复完成!")
        
        # 启动监控
        print("\n📺 启动串口监控...")
        print("💡 查看输出确认文件系统是否正常")
        print("💡 按 Ctrl+C 退出监控")
        time.sleep(2)
        
        try:
            subprocess.run("pio device monitor -e esp32s3_eye", shell=True)
        except KeyboardInterrupt:
            print("\n👋 监控已停止")
        
        return True
    else:
        print("\n⚠️  文件系统上传失败")
        print("💡 这可能是正常的，如果 data 目录为空")
        print("💡 设备仍可使用备用 HTML 页面工作")
        
        # 仍然启动监控查看结果
        print("\n📺 启动串口监控查看结果...")
        try:
            subprocess.run("pio device monitor -e esp32s3_eye", shell=True)
        except KeyboardInterrupt:
            print("\n👋 监控已停止")
        
        return True

def show_usage():
    """显示使用说明"""
    print("""
ESP32-S3-EYE 快速修复工具

这个脚本会执行以下操作:
1. 擦除 ESP32-S3-EYE 的 Flash
2. 清理构建缓存
3. 重新编译固件
4. 上传固件
5. 上传文件系统 (如果存在)
6. 启动串口监控

使用方法:
  python quick_fix_esp32s3_eye.py

注意事项:
- 确保 ESP32-S3-EYE 已通过 USB 连接
- 确保没有其他程序占用串口
- 如果上传失败，尝试按住 BOOT 按钮

故障排除:
- 如果上传失败，检查 USB 连接和端口
- 如果编译失败，检查代码语法
- 如果文件系统上传失败，检查 data 目录
""")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        show_usage()
    else:
        try:
            success = main()
            if success:
                print("\n✅ 修复流程完成!")
                print("🔍 请检查串口输出确认设备正常工作")
            else:
                print("\n❌ 修复流程失败")
                print("📖 请参考 FILESYSTEM_REPAIR_GUIDE.md 获取更多帮助")
            
            sys.exit(0 if success else 1)
            
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            sys.exit(1)
        except Exception as e:
            print(f"\n💥 意外错误: {e}")
            sys.exit(1)
